"""
Shell 命令管理器模块
用于处理 shell 命令中的 AI 请求，帮助用户生成 shell 命令
"""

import os
import asyncio
import logging
import platform
from typing import Dict, Any
from asgiref.sync import async_to_sync

try:
    from .llm.llm_agent import LLMAgent
except ImportError:
    from llm.llm_agent import LLMAgent


class ShellManager:
    """Shell 命令管理器"""

    def __init__(self, provider="local"):
        self.provider = provider

    def get_system_info(self) -> str:
        """获取当前操作系统信息"""
        try:
            system = platform.system()  # Linux, Windows, Darwin
            release = platform.release()  # 版本号
            version = platform.version()  # 详细版本信息
            machine = platform.machine()  # 架构: x86_64, arm64, etc.

            # 尝试获取发行版信息（仅 Linux）
            distro_info = ""
            if system == "Linux":
                try:
                    with open("/etc/os-release", "r") as f:
                        lines = f.readlines()
                        for line in lines:
                            if line.startswith("PRETTY_NAME="):
                                distro_info = line.split("=")[1].strip().strip('"')
                                break
                except:
                    pass

            # 组合系统信息
            if distro_info:
                system_info = f"{distro_info} ({machine})"
            else:
                system_info = f"{system} {release} ({machine})"

            return system_info

        except Exception as e:
            logging.error(f"获取系统信息失败: {e}")
            return "Linux"

    async def ask_shell_command(self, work_dir:str, user_query: str) -> Dict[str, Any]:
        """
        让 AI 根据用户需求生成 shell 命令

        Args:
            user_query: 用户的需求描述，例如 "查询主板型号"

        Returns:
            Dict 包含:
                - success: bool
                - command: str (AI 生成的命令)
                - message: str (错误信息或说明)
        """
        system_info = self.get_system_info()

        # 构建 prompt
        prompt = f"""你是一个 Shell 命令专家。当前操作系统: {system_info}

你的任务是根据用户需求，生成精确的 shell 命令。

**重要约束**:
1. 只返回可以直接执行的命令，不要返回任何解释或说明
2. 如果需要多个命令，用 && 或 ; 连接
3. 命令必须适配当前操作系统
4. 不要使用 sudo，除非用户明确要求
5. 优先使用通用性强、安全的命令
6. 只输出命令本身，不要有任何前缀或后缀文字
7. 当前工作目录为: {work_dir}

**示例**:
用户: 查询主板型号
你的回复: cat /sys/devices/virtual/dmi/id/board_name

用户: 查看当前目录所有文件的大小
你的回复: ls -lh

用户: 查找包含 "error" 的日志文件
你的回复: grep -r "error" /var/log/

现在请根据用户需求生成命令。"""

        result = {}
        try:
            logging.info(f"Shell AI: 处理请求 - {user_query}")

            # 使用 LLM Agent（快速、不深度思考）
            ai_agent = LLMAgent(prompt, knowledge_manager=None)

            # 运行 AI
            ai_result = await ai_agent.run_agent(
                work_dir=os.getcwd(),
                user_req=user_query,
                session_id=None,
                log_callback=None,
            )

            if ai_result.get("success"):
                command = ai_result.get("message", "").strip()

                # 清理可能的 markdown 代码块格式
                if command.startswith("```"):
                    lines = command.split("\n")
                    # 移除第一行和最后一行的 ``` 标记
                    if lines[0].startswith("```"):
                        lines = lines[1:]
                    if lines and lines[-1].strip() == "```":
                        lines = lines[:-1]
                    command = "\n".join(lines).strip()

                # 移除可能的 bash/sh 前缀
                if command.startswith("bash"):
                    command = command[4:].strip()
                if command.startswith("sh"):
                    command = command[2:].strip()

                result["success"] = True
                result["command"] = command
                result["message"] = "命令生成成功"
                logging.info(f"Shell AI: 生成命令 - {command}")

            else:
                result["success"] = False
                result["command"] = ""
                result["message"] = ai_result.get("message", "生成命令失败")
                logging.error(f"Shell AI: 生成失败 - {result['message']}")

        except Exception as e:
            logging.error(f"Shell AI 处理出错: {e}")
            result["success"] = False
            result["command"] = ""
            result["message"] = str(e)

        return result

    def ask_shell_command_sync(self, work_dir:str, user_query: str) -> Dict[str, Any]:
        """
        同步版本的 ask_shell_command

        Args:
            user_query: 用户的需求描述

        Returns:
            Dict 包含:
                - success: bool
                - command: str
                - message: str
        """
        run_sync = async_to_sync(self.ask_shell_command)
        return run_sync(work_dir, user_query)


# 全局单例
_shell_manager_instance = None


def get_shell_manager(provider="local") -> ShellManager:
    """获取 ShellManager 单例"""
    global _shell_manager_instance
    if _shell_manager_instance is None:
        _shell_manager_instance = ShellManager(provider)
    return _shell_manager_instance
