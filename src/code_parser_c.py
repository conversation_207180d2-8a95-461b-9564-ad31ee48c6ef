#!/usr/bin/env python3
"""
C/C++代码解析器
用于从C/C++源代码中提取函数和类/结构体定义
"""

import logging
import re
import uuid
from typing import List
from tree_sitter import Language, Parser
import tree_sitter_c as tsc
import tree_sitter_cpp as tscpp
from code_util import CodeChunk,CodeSymbol,_read_lines,_node_text,_node_lines,_sliding_window


class CodeParserC:
    """C/C++代码解析器类"""

    def __init__(self, code_chunk_size: int = 500):
        self.win_size = code_chunk_size
        self.overlap = int(self.win_size * 0.1)

    def extract_code_chunks(
        self, file_path: str, content: str, language: str
    ) -> List[CodeChunk]:
        if file_path.endswith((".c", ".cpp", ".cc", ".cxx", ".h", ".hpp")):
            LANG = "cpp"
            t_lang = Language(tscpp.language())
        else:
            LANG = "c"
            t_lang = Language(tsc.language())
        parser = Parser(t_lang)

        lines = _read_lines(file_path)
        total_lines = len(lines)
        tree = parser.parse("".join(lines).encode("utf8"))
        root = tree.root_node

        chunks: List[CodeChunk] = []
        acc_start, acc_end, acc_text, acc_node = None, None, [], None

        def _get_node_name(node):
            type_str = (
                "class"
                if node.type == "class_specifier"
                else "function" if node.type == "function_definition" else "other"
            )
            # 提取名称（简单版）
            name = None
            if type_str == "class":
                name = next(
                    (
                        ch.text.decode()
                        for ch in node.children
                        if ch.type == "identifier"
                    ),
                    None,
                )
            if type_str == "function":
                name = next(
                    (
                        ch.text.decode()
                        for ch in node.children
                        if ch.type == "function_declarator"
                    ),
                    None,
                )
            return type_str, name

        def _flush_acc(force_type="other"):
            """把累加器打包成 1 个 chunk"""
            nonlocal acc_end, acc_text, acc_start, acc_node
            if not acc_text or not acc_node or len(acc_text) <= 1:
                return
            type_str, name = _get_node_name(acc_node)
            start, end = acc_node.start_point[0], acc_node.end_point[0]
            chunks.append(
                CodeChunk(
                    chunk_id=str(uuid.uuid4()),
                    file_path=file_path,
                    content="".join(acc_text),
                    chunk_type=force_type,
                    language=LANG,
                    start_line=acc_start,
                    end_line=acc_end,
                    class_name=name if type_str == "class" else None,
                    function_name=name if type_str == "function" else None,
                )
            )
            acc_start, acc_end, acc_text, acc_node = None, None, [], None

        # 遍历一级 child (无法提取namespace下的class，code_chunk类型都是other)
        for node in root.children:
            node_line_cnt = _node_lines(node, lines)
            # 1) 大节点 → 滑动窗口
            if node_line_cnt >= self.win_size:
                _flush_acc()  # 先打包前面累加
                start, end = node.start_point[0], node.end_point[0]
                # 如果是超大节点，则用滑动窗口切分
                type_str, name = _get_node_name(node)
                for s, e, text in _sliding_window(
                    lines, start, end, self.win_size, self.overlap
                ):
                    chunks.append(
                        CodeChunk(
                            chunk_id=str(uuid.uuid4()),
                            file_path=file_path,
                            content=text,
                            chunk_type=type_str,
                            language=LANG,
                            start_line=s,
                            end_line=e,
                            class_name=name if type_str == "class" else None,
                            function_name=name if type_str == "function" else None,
                        )
                    )
                continue
            # 2) 小节点 → 累加
            if acc_start is None:
                acc_start = node.start_point[0]
                acc_node = node
            acc_end = node.end_point[0]
            acc_text.append(_node_text(node, lines))
            # 累加满窗口就切
            if (acc_end - acc_start + 1) >= self.win_size:
                _flush_acc()
        # 尾部剩余
        _flush_acc()
        return chunks
