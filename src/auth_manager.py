"""
用户认证和会话管理模块
"""

import os
import json
import hashlib
import secrets
from datetime import datetime, timedelta
from functools import wraps
from flask import session, request, jsonify, redirect
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class AuthManager:
    """用户认证管理器"""

    # 会话存储文件
    SESSION_FILE = os.path.join(os.path.dirname(__file__), '..', '.sessions.json')

    def __init__(self):
        self.username = os.getenv('AUTH_USERNAME', 'admin')
        self.password_hash = self._hash_password(os.getenv('AUTH_PASSWORD', 'admin123'))
        self.session_timeout_hours = int(os.getenv('SESSION_TIMEOUT_HOURS', '24'))
        self.sessions = self._load_sessions()

    def _hash_password(self, password: str) -> str:
        """使用SHA256哈希密码"""
        return hashlib.sha256(password.encode()).hexdigest()

    def _load_sessions(self) -> dict:
        """从文件加载会话信息"""
        if os.path.exists(self.SESSION_FILE):
            try:
                with open(self.SESSION_FILE, 'r') as f:
                    sessions = json.load(f)
                    # 清理过期会话
                    return self._clean_expired_sessions(sessions)
            except:
                return {}
        return {}

    def _save_sessions(self):
        """保存会话信息到文件"""
        try:
            with open(self.SESSION_FILE, 'w') as f:
                json.dump(self.sessions, f, indent=2)
        except Exception as e:
            print(f"保存会话信息失败: {e}")

    def _clean_expired_sessions(self, sessions: dict) -> dict:
        """清理过期的会话"""
        current_time = datetime.now()
        valid_sessions = {}

        for session_id, session_data in sessions.items():
            expire_time = datetime.fromisoformat(session_data['expire_at'])
            if expire_time > current_time:
                valid_sessions[session_id] = session_data

        return valid_sessions

    def authenticate(self, username: str, password: str) -> tuple:
        """
        验证用户名和密码

        Returns:
            (success: bool, message: str, session_id: str)
        """
        if username != self.username:
            return False, "用户名或密码错误", None

        password_hash = self._hash_password(password)
        if password_hash != self.password_hash:
            return False, "用户名或密码错误", None

        # 生成会话ID
        session_id = secrets.token_urlsafe(32)
        expire_at = datetime.now() + timedelta(hours=self.session_timeout_hours)

        # 保存会话
        self.sessions[session_id] = {
            'username': username,
            'created_at': datetime.now().isoformat(),
            'expire_at': expire_at.isoformat(),
            'last_activity': datetime.now().isoformat()
        }

        self._save_sessions()

        return True, "登录成功", session_id

    def validate_session(self, session_id: str) -> bool:
        """
        验证会话是否有效

        Returns:
            bool: 会话是否有效
        """
        if not session_id or session_id not in self.sessions:
            return False

        session_data = self.sessions[session_id]
        expire_time = datetime.fromisoformat(session_data['expire_at'])

        if expire_time < datetime.now():
            # 会话已过期，删除
            del self.sessions[session_id]
            self._save_sessions()
            return False

        # 更新最后活动时间
        session_data['last_activity'] = datetime.now().isoformat()
        self._save_sessions()

        return True

    def logout(self, session_id: str):
        """注销会话"""
        if session_id in self.sessions:
            del self.sessions[session_id]
            self._save_sessions()

    def change_password(self, old_password: str, new_password: str) -> tuple:
        """
        修改密码

        Returns:
            (success: bool, message: str)
        """
        # 验证旧密码
        old_password_hash = self._hash_password(old_password)
        if old_password_hash != self.password_hash:
            return False, "原密码错误"

        if len(new_password) < 6:
            return False, "新密码长度至少6位"

        # 更新密码（需要手动更新.env文件）
        self.password_hash = self._hash_password(new_password)

        # 更新.env文件
        env_file = os.path.join(os.path.dirname(__file__), '..', '.env')
        if os.path.exists(env_file):
            try:
                with open(env_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                with open(env_file, 'w', encoding='utf-8') as f:
                    for line in lines:
                        if line.startswith('AUTH_PASSWORD='):
                            f.write(f'AUTH_PASSWORD={new_password}\n')
                        else:
                            f.write(line)

                # 清除所有会话，要求重新登录
                self.sessions.clear()
                self._save_sessions()

                return True, "密码修改成功，请重新登录"
            except Exception as e:
                return False, f"密码修改失败: {e}"

        return False, "配置文件不存在"

    def get_current_user(self, session_id: str) -> dict:
        """获取当前用户信息"""
        if session_id and session_id in self.sessions:
            return {
                'username': self.sessions[session_id]['username'],
                'logged_in': True
            }
        return {'logged_in': False}


# 全局认证管理器实例
auth_manager = AuthManager()

def login_required(f):
    """登录保护装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        session_id = request.cookies.get('session_id') or session.get('session_id')

        if not session_id or not auth_manager.validate_session(session_id):
            # API请求返回JSON错误
            if request.path.startswith('/aicode/api/'):
                return jsonify({'error': '未登录或会话已过期', 'login_required': True}), 401

            # 页面请求重定向到登录页
            return redirect('/aicode/login.html')

        return f(*args, **kwargs)

    return decorated_function
