#!/usr/bin/env python3
"""
JSON文档切块处理器
实现按照JSON结构层次进行智能切块的功能
"""

import json
import logging
import hashlib
from typing import List, Dict, Any, Union, Set

# JSON文档切块配置常量
JSON_CHUNK_SIZE_THRESHOLD = 1500  # 分块阈值（字节）
JSON_SLIDING_OVERLAP = 100  # 滑动窗口重叠大小


class JsonChunker:
    """JSON文档切块器"""

    def __init__(self, json_processing: dict = {}):
        self.logger = logging.getLogger(__name__)
        # 分块阈值
        self.chunk_threshold = json_processing.get(
            "chunk_threshold", JSON_CHUNK_SIZE_THRESHOLD
        )
        # 是否启用内容去重
        self.enable_deduplication = json_processing.get(
            "enable_deduplication", False
        )
        # 内容哈希集合，用于去重
        self.content_hashes: Set[str] = set()
        # 重复内容统计
        self.duplicate_count = 0

    def chunk_json_document(
        self, json_content: str, source_name: str = None
    ) -> List[Dict[str, Any]]:
        """
        对JSON文档进行递归分层切块处理

        Args:
            json_content: JSON文档内容
            source_name: 源文档名称

        Returns:
            切块结果列表，每个块包含内容和元数据
        """
        try:
            # 解析JSON内容
            json_data = json.loads(json_content)

            # 生成文档ID
            doc_id = source_name or "json_document"

            # 重置去重状态
            self.content_hashes.clear()
            self.duplicate_count = 0

            # 开始递归分层分块处理
            chunks = self._chunk_by_recursive_level(json_data, doc_id, 1, [])

            # 记录去重统计信息
            if self.enable_deduplication and self.duplicate_count > 0:
                self.logger.info(f"JSON文档分块过程中去除了 {self.duplicate_count} 个重复内容块")

            self.logger.info(f"JSON文档递归分层切块完成，共生成 {len(chunks)} 个块")
            return chunks

        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败: {e}")
            return []
        except Exception as e:
            self.logger.error(f"JSON文档切块失败: {e}")
            return []

    def _calculate_content_hash(self, content: str) -> str:
        """
        计算内容的哈希值用于去重

        Args:
            content: 要计算哈希的内容

        Returns:
            内容的MD5哈希值
        """
        return hashlib.md5(content.encode('utf-8')).hexdigest()

    def _is_duplicate_content(self, content: str) -> bool:
        """
        检查内容是否重复

        Args:
            content: 要检查的内容

        Returns:
            如果内容重复返回True，否则返回False
        """
        if not self.enable_deduplication:
            return False

        content_hash = self._calculate_content_hash(content)
        if content_hash in self.content_hashes:
            self.duplicate_count += 1
            return True
        else:
            self.content_hashes.add(content_hash)
            return False

    def _chunk_by_recursive_level(
        self,
        json_data: Any,
        doc_id: str,
        current_level: int,
        parent_keys: List[str] = None,
    ) -> List[Dict[str, Any]]:
        """
        递归分层分块：从指定层级开始，如果超过阈值则继续向下分层

        Args:
            json_data: 当前层级的JSON数据
            doc_id: 文档ID
            current_level: 当前层级
            parent_keys: 父级键路径

        Returns:
            切块结果列表
        """
        if parent_keys is None:
            parent_keys = []

        chunks = []

        # 分块的阈值
        threshold = self.chunk_threshold

        # 处理字典类型
        if isinstance(json_data, dict):
            for key, value in json_data.items():
                # 构建当前键路径
                current_path = parent_keys + [key]

                # 创建当前层级的JSON对象
                current_obj = {key: value}
                chunk_content = json.dumps(current_obj, ensure_ascii=False, indent=2)
                chunk_bytes = chunk_content.encode("utf-8")

                # 检查是否超过阈值
                if len(chunk_bytes) <= threshold:
                    # 不超过阈值，检查是否重复内容
                    if not self._is_duplicate_content(chunk_content):
                        # 不是重复内容，直接作为一个块
                        chunk = {
                            "content": chunk_content,
                            "metadata": {
                                "type": "json",
                                "level": current_level,
                                "key": key,
                                "size_bytes": len(chunk_bytes),
                                "doc_id": doc_id,
                                "parent_keys": parent_keys,
                            },
                        }
                        chunks.append(chunk)
                else:
                    # 超过阈值，继续向下分层
                    if isinstance(value, (dict, list)):
                        # 如果值是字典或列表，可以继续分层
                        deeper_chunks = self._chunk_by_recursive_level(
                            value, doc_id, current_level + 1, current_path
                        )
                        chunks.extend(deeper_chunks)
                    else:
                        # 如果值是基本类型，使用滑动窗口分块
                        sliding_chunks = self._chunk_by_sliding_window(
                            chunk_content,
                            doc_id,
                            current_level,
                            parent_keys=parent_keys,
                            key=key,
                        )
                        chunks.extend(sliding_chunks)

        # 处理列表类型
        elif isinstance(json_data, list):
            for i, item in enumerate(json_data):
                # 构建当前键路径
                current_path = parent_keys + [f"[{i}]"]

                # 创建当前层级的JSON对象
                current_obj = item
                chunk_content = json.dumps(current_obj, ensure_ascii=False, indent=2)
                chunk_bytes = chunk_content.encode("utf-8")

                # 检查是否超过阈值
                if len(chunk_bytes) <= threshold:
                    # 不超过阈值，检查是否重复内容
                    if not self._is_duplicate_content(chunk_content):
                        # 不是重复内容，直接作为一个块
                        chunk = {
                            "content": chunk_content,
                            "metadata": {
                                "type": "json",
                                "level": current_level,
                                "index": i,
                                "size_bytes": len(chunk_bytes),
                                "doc_id": doc_id,
                                "parent_keys": parent_keys,
                            },
                        }
                        chunks.append(chunk)
                else:
                    # 超过阈值，继续向下分层
                    if isinstance(item, (dict, list)):
                        # 如果项是字典或列表，可以继续分层
                        deeper_chunks = self._chunk_by_recursive_level(
                            item, doc_id, current_level + 1, current_path
                        )
                        chunks.extend(deeper_chunks)
                    else:
                        # 如果项是基本类型，使用滑动窗口分块
                        sliding_chunks = self._chunk_by_sliding_window(
                            chunk_content,
                            doc_id,
                            current_level,
                            parent_keys=parent_keys,
                            index=i,
                        )
                        chunks.extend(sliding_chunks)

        # 处理基本类型（最底层）
        else:
            # 已经到达最底层，如果还超过阈值则使用滑动窗口
            chunk_content = json.dumps(json_data, ensure_ascii=False, indent=2)
            chunk_bytes = chunk_content.encode("utf-8")

            if len(chunk_bytes) <= self.chunk_threshold:
                # 检查是否重复内容
                if not self._is_duplicate_content(chunk_content):
                    chunk = {
                        "content": chunk_content,
                        "metadata": {
                            "type": "json",
                            "level": current_level,
                            "size_bytes": len(chunk_bytes),
                            "doc_id": doc_id,
                            "parent_keys": parent_keys,
                        },
                    }
                    chunks.append(chunk)
            else:
                # 使用滑动窗口分块
                sliding_chunks = self._chunk_by_sliding_window(
                    chunk_content, doc_id, current_level, parent_keys=parent_keys
                )
                chunks.extend(sliding_chunks)

        return chunks

    def _chunk_by_sliding_window(
        self, content: str, doc_id: str, level: int = 0, **metadata
    ) -> List[Dict[str, Any]]:
        """
        使用滑动窗口进行分块

        Args:
            content: 要分块的内容
            doc_id: 文档ID
            level: 层级
            **metadata: 其他元数据

        Returns:
            滑动窗口切块结果
        """
        chunks = []
        content_bytes = content.encode("utf-8")
        content_length = len(content_bytes)

        if content_length <= self.chunk_threshold:
            # 内容小于窗口大小，检查是否重复内容
            if not self._is_duplicate_content(content):
                # 不是重复内容，直接作为一个块
                chunk = {
                    "content": content,
                    "metadata": {
                        "type": "json",
                        "level": level,
                        "chunk_method": "sliding_window",
                        "window_start": 0,
                        "window_end": content_length,
                        "size_bytes": content_length,
                        "doc_id": doc_id,
                        **metadata,
                    },
                }
                chunks.append(chunk)
        else:
            # 使用滑动窗口分块
            start = 0
            chunk_index = 0

            while start < content_length:
                # 计算窗口结束位置
                end = min(start + self.chunk_threshold, content_length)

                # 尝试在UTF-8字符边界处分割，避免截断字符
                if end < content_length:
                    # 向前找到最近的UTF-8字符边界
                    while end > start and (content_bytes[end] & 0xC0) == 0x80:
                        end -= 1

                # 提取窗口内容
                window_bytes = content_bytes[start:end]
                window_content = window_bytes.decode("utf-8", errors="ignore")

                # 检查是否重复内容
                if not self._is_duplicate_content(window_content):
                    # 创建块
                    chunk = {
                        "content": window_content,
                        "metadata": {
                            "type": "json",
                            "level": level,
                            "chunk_method": "sliding_window",
                            "window_start": start,
                            "window_end": end,
                            "chunk_index": chunk_index,
                            "size_bytes": len(window_bytes),
                            "doc_id": doc_id,
                            **metadata,
                        },
                    }
                    chunks.append(chunk)

                # 移动窗口（考虑重叠）
                start = start + self.chunk_threshold - JSON_SLIDING_OVERLAP
                chunk_index += 1

        return chunks
      

    def get_json_summary(self, json_content: str) -> Dict[str, Any]:
        """
        获取JSON文档的摘要信息

        Args:
            json_content: JSON文档内容

        Returns:
            JSON文档摘要信息
        """
        try:
            json_data = json.loads(json_content)

            summary = {
                "type": "json",
                "content_size_bytes": len(json_content.encode("utf-8")),
                "structure_type": type(json_data).__name__,
                "root_keys": [],
                "max_depth": 0,
                "total_elements": 0,
            }

            if isinstance(json_data, dict):
                summary["root_keys"] = list(json_data.keys())
                summary["total_elements"] = len(json_data)
                summary["max_depth"] = self._calculate_max_depth(json_data)
            elif isinstance(json_data, list):
                summary["total_elements"] = len(json_data)
                summary["max_depth"] = self._calculate_max_depth(json_data)

            return summary

        except json.JSONDecodeError as e:
            return {
                "type": "json",
                "error": f"JSON解析失败: {str(e)}",
                "content_size_bytes": len(json_content.encode("utf-8")),
            }

    def _calculate_max_depth(self, obj: Any, current_depth: int = 0) -> int:
        """
        计算JSON对象的最大深度

        Args:
            obj: JSON对象
            current_depth: 当前深度

        Returns:
            最大深度
        """
        if isinstance(obj, dict):
            if not obj:
                return current_depth
            return max(
                self._calculate_max_depth(v, current_depth + 1) for v in obj.values()
            )
        elif isinstance(obj, list):
            if not obj:
                return current_depth
            return max(
                self._calculate_max_depth(item, current_depth + 1) for item in obj
            )
        else:
            return current_depth
