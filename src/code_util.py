#!/usr/bin/env python3
"""
代码工具模块
包含代码符号和代码块的定义
"""

import json
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict


@dataclass
class KnowledgeBase:
    """知识库数据类"""

    kb_id: str
    name: str
    description: str
    project_id: str
    collection_name: str
    created_at: str
    updated_at: str
    document_count: int = 0
    chunk_count: int = 0

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class Document:
    """文档数据类"""

    doc_id: str
    kb_id: str
    title: str
    content: str
    metadata: Dict[str, Any]
    created_at: str

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

@dataclass
class CodeSymbol:
    """代码符号"""
    name: str
    type: str  # function, variable, struct, class, macro, etc.
    file_path: str
    line_number: int
    column: int
    signature: Optional[str] = None
    docstring: Optional[str] = None
    parent: Optional[str] = None
    children: List[str] = None
    
    def __post_init__(self):
        if self.children is None:
            self.children = []


@dataclass
class CodeChunk:
    """代码块数据类"""
    chunk_id: str
    file_path: str
    content: str
    chunk_type: str  # 'function', 'class'
    language: str
    start_line: int
    end_line: int
    function_name: Optional[str] = None
    class_name: Optional[str] = None
    metadata: Dict[str, Any] = None

    def to_dict(self) -> Dict[str, Any]:
        return {
            "chunk_id": self.chunk_id,
            "file_path": self.file_path,
            "content": self.content,
            "chunk_type": self.chunk_type,
            "language": self.language,
            "start_line": self.start_line,
            "end_line": self.end_line,
            "function_name": self.function_name,
            "class_name": self.class_name,
            #"metadata": self.metadata or {}
        }
###################工具方法#####################################################
# 工具
def _read_lines(path: str) -> List[str]:
    with open(path, encoding="utf-8") as f:
        return f.readlines()

def _node_lines(node, lines: List[str]) -> int:
    """节点占用行数"""
    start, end = node.start_point[0], node.end_point[0]
    return end - start + 1

def _node_text(node, lines: List[str]) -> str:
    """节点完整文本"""
    start, end = node.start_point[0], node.end_point[0]
    return "".join(lines[start: end + 1])

def _sliding_window(lines: List[str], start: int, end: int, win: int, overlap: int):
    """对 [start, end] 区间做滑动窗口"""
    i = start
    while i <= end:
        chunk_end = min(i + win - 1, end)
        yield i, chunk_end, "".join(lines[i: chunk_end + 1])
        i += win - overlap
        if i + win - overlap > end and i < end:  # 最后一段
            yield i, end, "".join(lines[i: end + 1])
            break

##############################################################################