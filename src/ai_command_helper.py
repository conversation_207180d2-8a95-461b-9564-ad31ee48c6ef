#!/usr/bin/env python3
"""
AI 命令助手 - 由 bash wrapper 调用
接收用户查询，返回生成的 shell 命令
"""

import sys
import os
import logging

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def _configure_logging():
    """将日志默认级别降为 WARNING，避免噪声输出到终端"""
    level_name = os.environ.get("AI_COMMAND_LOG_LEVEL", "WARNING").upper()
    level = getattr(logging, level_name, logging.WARNING)

    root_logger = logging.getLogger()
    root_logger.setLevel(level)

    if not root_logger.handlers:
        handler = logging.StreamHandler()
        handler.setLevel(level)
        root_logger.addHandler(handler)
    else:
        for handler in root_logger.handlers:
            handler.setLevel(level)

    # 降低第三方库日志等级
    logging.getLogger("httpx").setLevel(max(level, logging.WARNING))
    logging.getLogger("httpcore").setLevel(max(level, logging.WARNING))


_configure_logging()


def main():
    if len(sys.argv) < 2:
        print("# 使用方法: /ai <你想执行的操作>", file=sys.stderr)
        sys.exit(1)

    # 获取查询内容（所有参数合并）
    query = " ".join(sys.argv[1:])
    
    work_dir = None
    if len(sys.argv) > 2:
        work_dir = sys.argv[2]

    try:
        from shell_manager import get_shell_manager

        # 从环境变量获取 provider（如果有）
        provider = os.environ.get("AI_PROVIDER", "local")

        shell_manager = get_shell_manager(provider)
        result = shell_manager.ask_shell_command_sync(work_dir, query)

        if result.get("success"):
            command = result.get("command", "")
            # 只输出命令本身
            print(command)
            sys.exit(0)
        else:
            # 输出错误信息到 stderr
            message = result.get("message", "未知错误")
            print(message, file=sys.stderr)
            sys.exit(1)

    except Exception as e:
        print(f"错误: {str(e)}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
