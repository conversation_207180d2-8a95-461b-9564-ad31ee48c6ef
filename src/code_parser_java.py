#!/usr/bin/env python3
"""
Java代码解析器
用于从Java源代码中提取方法和类定义
"""

import uuid
from typing import List
from tree_sitter import Language, Parser, QueryCursor
import tree_sitter_java as tsjava
from code_util import CodeChunk,CodeSymbol,_read_lines,_node_text,_node_lines,_sliding_window


class CodeParserJava:
    """Java 代码分块器类"""
    def __init__(self, code_chunk_size: int = 500):
        self.win_size = code_chunk_size
        self.overlap = int(self.win_size * 0.1)

    def extract_code_chunks(self, file_path: str, content: str, language: str) -> List[CodeChunk]:
        # 1. 初始化解析器
        java_lang = Language(tsjava.language())
        parser = Parser(java_lang)
        lines = _read_lines(file_path)
        tree = parser.parse("".join(lines).encode("utf8"))
        root = tree.root_node

        chunks: List[CodeChunk] = []
        # 累加器
        acc_start, acc_end, acc_text, acc_node = None, None, [], None

        def _get_node_name(node):
            """提取类名或方法名"""
            type_str = "class" if node.type == "class_declaration" else "function" if node.type == "method_declaration" else "other"
            name = None
            if type_str == "class":
                # class Foo { ... }
                for ch in node.children:
                    if ch.type == "identifier":
                        name = ch.text.decode()
                        break
            if type_str == "function":
                # method_declaration → 第一个 identifier 即方法名
                for ch in node.children:
                    if ch.type == "identifier":
                        name = ch.text.decode()
                        break
            return type_str, name

        def _flush_acc(force_type="other"):
            nonlocal acc_end, acc_text, acc_start, acc_node
            if not acc_text or not acc_node or len(acc_text) <= 1:
                return
            type_str, name = _get_node_name(acc_node)
            chunks.append(CodeChunk(
                chunk_id=str(uuid.uuid4()),
                file_path=file_path,
                content="".join(acc_text),
                chunk_type=force_type,
                language="java",
                start_line=acc_start,
                end_line=acc_end,
                class_name=name if type_str == "class" else None,
                function_name=name if type_str == "function" else None,
            ))
            acc_start, acc_end, acc_text, acc_node = None, None, [], None

        # 遍历一级 child
        for node in root.children:
            node_line_cnt = _node_lines(node, lines)
            # 1) 大节点 → 滑动窗口
            if node_line_cnt >= self.win_size:
                _flush_acc()  # 先打包前面累加
                start, end = node.start_point[0], node.end_point[0]
                type_str, name = _get_node_name(node)
                for s, e, text in _sliding_window(lines, start, end, self.win_size, self.overlap):
                    chunks.append(CodeChunk(
                        chunk_id=str(uuid.uuid4()),
                        file_path=file_path,
                        content=text,
                        chunk_type=type_str,
                        language="java",
                        start_line=s,
                        end_line=e,
                        class_name=name if type_str == "class" else None,
                        function_name=name if type_str == "function" else None,
                    ))
                continue
            # 2) 小节点 → 累加
            if acc_start is None:
                acc_start = node.start_point[0]
                acc_node = node
            acc_end = node.end_point[0]
            acc_text.append(_node_text(node, lines))
            # 累加满窗口就切
            if (acc_end - acc_start + 1) >= self.win_size:
                _flush_acc()
        # 尾部剩余
        _flush_acc()
        return chunks