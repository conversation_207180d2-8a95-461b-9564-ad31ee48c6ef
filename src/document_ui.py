"""
文档管理UI模块
处理与文档相关的路由
"""

import os
import json
import logging
from flask import Blueprint, request, jsonify
from project_manager import ProjectManager

# 创建蓝图
document_bp = Blueprint("document", __name__)

# 全局变量
project_manager = None
app = None

def init_document_app(pm: ProjectManager, app_instance=None):
    """设置项目管理器实例"""
    global project_manager,app
    project_manager = pm
    app = app_instance
@document_bp.route("/aicode/requirement_manager.html")
def requirement_manager_html():
    """需求管理HTML"""
    return app.send_static_file("requirement_manager.html")


@document_bp.route("/aicode/design_manager.html")
def design_manager_html():
    """设计管理HTML"""
    return app.send_static_file("design_manager.html")


@document_bp.route("/aicode/api/projects/<project_id>/requirement", methods=["GET", "PUT"])
def api_project_requirement(project_id):
    """项目需求管理API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({"error": "项目不存在"}), 404

    if request.method == "GET":
        # 从文件中加载最新的需求内容
        requirement_content = project.load_requirement_from_file()
        return jsonify({"requirement": requirement_content})

    elif request.method == "PUT":
        data = request.get_json()
        requirement = data.get("requirement", "")
        success = project_manager.update_project(project_id, requirement=requirement)
        if success:
            return jsonify({"success": True})
        else:
            return jsonify({"success": False, "message": "更新失败"}), 500

@document_bp.route("/aicode/api/projects/<project_id>/optimize_requirement", methods=["POST"])
def api_optimize_requirement(project_id):
    """优化需求内容API"""
    try:
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({"error": "项目不存在"}), 404

        data = request.get_json() or {}
        requirement = data.get("requirement", "")
        enable_kb = data.get("enable_kb", False)

        if not requirement:
            return jsonify({"success": False, "message": "需求内容不能为空"}), 400

        doc_manager = project_manager.get_document_manager(project_id)
        if not doc_manager:
            return jsonify({"error": "无法获取DocumentManager"}), 404
        
        result = doc_manager.optimize_requirement(requirement , enable_kb)

        if result.get("success"):
            return jsonify({"success": True, "message": result.get("message", "").strip()})
        else:
            return jsonify(
                {"success": False, "message": result.get("message", "优化失败").strip()}
            )
    except Exception as e:
        logging.error(f"优化需求失败: {e}")
        return jsonify({"success": False, "message": str(e)}), 500


@document_bp.route("/aicode/api/projects/<project_id>/run_requirement", methods=["POST"])
def api_run_requirement(project_id):
    """直接实现用户需求内容"""
    try:
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({"error": "项目不存在"}), 404
        requirement = project.requirement

        task_manager = project_manager.get_task_manager(project_id)
        if not task_manager:
            return jsonify({"error": "无法获取TaskManager"}), 404
        result = task_manager.run_requirement(user_req=requirement, enable_kb=True)

        if result.get("success"):
            return jsonify({"success": True, "message": result.get("message", "")})
        else:
            return jsonify(
                {"success": False, "message": result.get("message", "优化失败")}
            )
    except Exception as e:
        logging.error(f"需求实现失败: {e}")
        return jsonify({"success": False, "message": str(e)}), 500

# 新增设计管理API
@document_bp.route("/aicode/api/projects/<project_id>/design", methods=["GET", "PUT"])
def api_project_design(project_id):
    """项目设计管理API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({"error": "项目不存在"}), 404

    if request.method == "GET":
        # 从文件中加载最新的设计内容
        design_content = project.load_design_from_file()
        return jsonify({"design": design_content})

    elif request.method == "PUT":
        data = request.get_json()
        design = data.get("design", "")
        success = project_manager.update_project(project_id, design=design)
        if success:
            return jsonify({"success": True})
        else:
            return jsonify({"success": False, "message": "更新失败"}), 500

@document_bp.route("/aicode/api/projects/<project_id>/gen_design", methods=["POST"])
def gen_design(project_id):
    """生成设计API"""
    try:
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({"error": "项目不存在"}), 404

        requirement = project.requirement

        if not requirement:
            return jsonify({"success": False, "message": "需求内容不能为空"}), 400

        doc_manager = project_manager.get_document_manager(project_id)
        if not doc_manager:
            return jsonify({"error": "无法获取DocumentManager"}), 404
        result = doc_manager.gen_design(requirement, enable_kb=True)

        if result.get("success"):
            design_content = result.get("message", "")
            success = project_manager.update_project(project_id, design=design_content)
            return jsonify({"success": success, "message": design_content})
        else:
            return jsonify(
                {"success": False, "message": result.get("message", "生成设计失败")}
            )
    except Exception as e:
        logging.error(f"生成设计失败: {e}")
        return jsonify({"success": False, "message": str(e)}), 500

@document_bp.route("/aicode/api/projects/<project_id>/run_design", methods=["POST"])
def api_run_design(project_id):
    """直接实现用户的设计文档内容"""
    try:
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({"error": "项目不存在"}), 404

        design = project.design

        task_manager = project_manager.get_task_manager(project_id)
        if not task_manager:
            return jsonify({"error": "无法获取TaskManager"}), 404
        result = task_manager.run_design(design_doc=design, enable_kb=True)

        if result.get("success"):
            return jsonify({"success": True, "message": result.get("message", "")})
        else:
            return jsonify(
                {"success": False, "message": result.get("message", "优化失败")}
            )
    except Exception as e:
        logging.error(f"设计实现失败: {e}")
        return jsonify({"success": False, "message": str(e)}), 500

# 对选中的文档片段进行AI优化
@document_bp.route("/aicode/api/projects/<project_id>/documentai", methods=["POST"])
def api_optimize_document(project_id):
    """优化文档内容API"""
    try:
        project = project_manager.get_project(project_id)
        doc_manager = project_manager.get_document_manager(project_id)
        if not project or not doc_manager:
            return jsonify({"error": "项目或文档管理器不存在"}), 404

        data = request.get_json() or {}
        selection = data.get("selection", "")
        enable_kb = data.get("enable_kb", False)

        if not selection:
            return jsonify({"success": False, "message": "未选中文本"}), 400
                
        result = doc_manager.optimize_document(data, enable_kb)

        if result.get("success"):
            return jsonify({"success": True, "message": result.get("message", "").strip()})
        else:
            return jsonify(
                {"success": False, "message": result.get("message", "优化失败")}
            )
    except Exception as e:
        logging.error(f"优化需求失败: {e}")
        return jsonify({"success": False, "message": str(e)}), 500
# 对选中的文档片段进行AI通用处理
@document_bp.route("/aicode/api/projects/<project_id>/askai", methods=["POST"])
def api_document_askai(project_id):
    """优化文档内容API"""
    try:
        project = project_manager.get_project(project_id)
        doc_manager = project_manager.get_document_manager(project_id)
        if not project or not doc_manager:
            return jsonify({"error": "项目或文档管理器不存在"}), 404

        data = request.get_json() or {}
        selection = data.get("selection", "")
        if not selection:
            return jsonify({"success": False, "message": "未选中文本"}), 400
        
        enable_kb = data.get("enable_kb", False)
        
        if enable_kb:
            kb_manager = project_manager.get_knowledge_manager(project_id)
        
        result = doc_manager.askAI(data, kb_manager)

        if result.get("success"):
            return jsonify({"success": True, "message": result.get("message", "").strip()})
        else:
            return jsonify(
                {"success": False, "message": result.get("message", "优化失败")}
            )
    except Exception as e:
        logging.error(f"优化需求失败: {e}")
        return jsonify({"success": False, "message": str(e)}), 500
# 根据选中的文本片段创建ai任务
@document_bp.route("/aicode/api/projects/<project_id>/gen_task", methods=["POST"])
def api_document_gentask(project_id):
    """生成任务API"""
    try:
        project = project_manager.get_project(project_id)
        doc_manager = project_manager.get_document_manager(project_id)
        if not project or not doc_manager:
            return jsonify({"error": "项目或文档管理器不存在"}), 404

        data = request.get_json() or {}
        selection = data.get("selection", "")
        if not selection:
            return jsonify({"success": False, "message": "未选中文本"}), 400
        
        enable_kb = data.get("enable_kb", False)
        
        result = doc_manager.new_task(data, enable_kb)

        if result.get("success"):
            return jsonify({"success": True, "message": result.get("message", "").strip()})
        else:
            return jsonify(
                {"success": False, "message": result.get("message", "任务创建失败")}
            )
    except Exception as e:
        logging.error(f"任务创建失败: {e}")
        return jsonify({"success": False, "message": str(e)}), 500

# 对选中的文档片段进行AI绘图
@document_bp.route("/aicode/api/projects/<project_id>/drawai", methods=["POST"])
def api_document_drawai(project_id):
    """文档内容绘图API"""
    try:
        project = project_manager.get_project(project_id)
        doc_manager = project_manager.get_document_manager(project_id)
        if not project or not doc_manager:
            return jsonify({"error": "项目或文档管理器不存在"}), 404

        data = request.get_json() or {}
        selection = data.get("selection", "")
        if not selection:
            return jsonify({"success": False, "message": "未选中文本"}), 400
        
        enable_kb = data.get("enable_kb", False)
        
        result = doc_manager.drawAI(data, enable_kb)

        if result.get("success"):
            return jsonify({"success": True, "message": result.get("message", "").strip()})
        else:
            return jsonify(
                {"success": False, "message": result.get("message", "绘图失败")}
            )
    except Exception as e:
        logging.error(f"优化需求失败: {e}")
        return jsonify({"success": False, "message": str(e)}), 500

# AI引导式需求生成
@document_bp.route("/aicode/api/projects/<project_id>/chatai", methods=["POST"])
def api_document_chatai(project_id):
    """AI引导式需求生成API"""
    try:
        project = project_manager.get_project(project_id)
        doc_manager = project_manager.get_document_manager(project_id)
        if not project or not doc_manager:
            return jsonify({"error": "项目或文档管理器不存在"}), 404

        data = request.get_json() or {}
        session_id = data.get("session_id")
        message = data.get("message", "")
        prev_lines = data.get("prev_lines", "")
        next_lines = data.get("next_lines", "")

        if not message:
            return jsonify({"success": False, "message": "消息内容不能为空"}), 400

        enable_kb = data.get("enable_kb", False)

        result = doc_manager.chatAI(session_id, message, enable_kb, prev_lines=prev_lines, next_lines=next_lines)

        if result.get("success"):
            return jsonify({
                "success": True,
                "message": result.get("message", "").strip(),
                "is_final": result.get("is_final", False)
            })
        else:
            return jsonify(
                {"success": False, "message": result.get("message", "处理失败")}
            )
    except Exception as e:
        logging.error(f"AI引导式需求生成失败: {e}")
        return jsonify({"success": False, "message": str(e)}), 500

# 初始化规则
@document_bp.route("/aicode/api/projects/<project_id>/init_rules", methods=["POST"])
def api_init_rules(project_id):
    """初始化项目规则"""
    try:
        project = project_manager.get_project(project_id)
        doc_manager = project_manager.get_document_manager(project_id)
        if not project or not doc_manager:
            return jsonify({"error": "项目或文档管理器不存在"}), 404

        result = doc_manager.init_rules()

        if result.get("success"):
            # 更新项目的规则约束
            # project_manager.update_project(project_id, rules_constraint=result.get("message", ""))
            return jsonify({
                "success": True,
                "message": result.get("message", "")
            })
        else:
            return jsonify(
                {"success": False, "message": result.get("message", "初始化失败")}
            )
    except Exception as e:
        logging.error(f"初始化规则失败: {e}")
        return jsonify({"success": False, "message": str(e)}), 500