#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import requests
from typing import Optional

try:
    from .config import Config
except ImportError:
    from config import Config
    
class FileConverter:
    """文件格式转换工具"""
    
    def __init__(self):
        self.api_endpoint = Config.MINERU_API
    
    def doc_to_markdown(self, doc_file: str, write_file = False) -> str:
        """
        将PDF/word文件转为Markdown,写到同文件目录下
        """        
        try:
            # 读取文件
            with open(doc_file, "rb") as f:
                # 根据文件后缀名是 word文件或pdf文件，设置文件类型。其他类型暂不支持
                file_type = "application/vnd.openxmlformats-officedocument.wordprocessingml.document" if doc_file.endswith(".docx") else "application/pdf"
                files = {"file": (os.path.basename(doc_file), f, file_type)}
                
                # 发送POST请求到Dify
                response = requests.post(self.api_endpoint, files=files, verify=False, timeout=1800)
                
                if response.status_code == 200:
                    json_data = response.json()
                    # 检查是否存在 markdown 字段
                    md_content = json_data.get("markdown")
                    if md_content:
                        md_content = md_content
                        # 先将所有###替换为临时占位符
                        # md_content = md_content.replace('###', '@@@')
                        # # 再将所有##替换为###
                        # md_content = md_content.replace('##', '###')
                        # # 最后将临时占位符替换为##
                        # md_content = md_content.replace('@@@', '##')
                    else:
                        md_content = f"{json_data}"
                    
                    if write_file:                        
                        pdf_folder = os.path.dirname(doc_file)
                        md_file_path = os.path.join(pdf_folder, os.path.splitext(os.path.basename(doc_file))[0] + ".md")
                        # 检查md文件是否存在，并告警信息
                        if os.path.exists(md_file_path):
                            print(f"WARN：{md_file_path} 已存在！！！")
                        # 保存N内容
                        with open(md_file_path, "w", encoding="utf-8") as mf:
                            mf.write(md_content)                    
                        print(f"markdown已保存到: {md_file_path}")
                    
                    return md_content
                else:
                    raise Exception(f"转换失败: {doc_file} - 状态码: {response.status_code}")
                    
        except Exception as e:
            raise Exception(f"处理文件 {doc_file} 时出错: {str(e)}")
        
    def markdown_to_html(self, markdown_content: str) -> str:
        """将Markdown转换为HTML（用于预览）"""
        try:
            import markdown
            md = markdown.Markdown(extensions=['tables', 'fenced_code', 'toc'])
            html = md.convert(markdown_content)
            
            # 添加基本的CSS样式
            styled_html = f"""
            <div style="max-width: 800px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
                {html}
            </div>
            <style>
                table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                code {{ background-color: #f4f4f4; padding: 2px 4px; border-radius: 3px; }}
                pre {{ background-color: #f4f4f4; padding: 10px; border-radius: 5px; overflow-x: auto; }}
                blockquote {{ border-left: 4px solid #ddd; margin: 0; padding-left: 20px; color: #666; }}
            </style>
            """
            
            return styled_html
            
        except ImportError:
            # 如果没有markdown库，使用简单的HTML转换
            html = markdown_content.replace('\n', '<br>')
            html = re.sub(r'^# (.+)$', r'<h1>\1</h1>', html, flags=re.MULTILINE)
            html = re.sub(r'^## (.+)$', r'<h2>\1</h2>', html, flags=re.MULTILINE)
            html = re.sub(r'^### (.+)$', r'<h3>\1</h3>', html, flags=re.MULTILINE)
            html = re.sub(r'^#### (.+)$', r'<h4>\1</h4>', html, flags=re.MULTILINE)
            html = re.sub(r'\*\*(.+?)\*\*', r'<strong>\1</strong>', html)
            html = re.sub(r'\*(.+?)\*', r'<em>\1</em>', html)
            
            return f'<div style="max-width: 800px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">{html}</div>'
        
        except Exception as e:
            return f'<div>HTML转换失败: {str(e)}</div>'