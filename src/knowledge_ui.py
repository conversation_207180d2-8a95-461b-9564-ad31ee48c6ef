#!/usr/bin/env python3
"""
知识库管理UI路由
处理知识库相关的前端路由和API接口
"""

import os
import json
import logging
import io
import threading
from flask import Blueprint, request, jsonify
from project_manager import ProjectManager
from config import Config

# 获取当前文件所在目录
current_dir = os.path.dirname(os.path.abspath(__file__))

# 创建蓝图
knowledge_bp = Blueprint("knowledge", __name__)

# 在应用初始化时会被设置
project_manager = None
app = None

def init_knowledge_app(pm: ProjectManager, web_app):
    """设置知识库管理器实例"""
    global project_manager
    global app
    project_manager = pm
    app = web_app


# HTML路由
@knowledge_bp.route("/aicode/knowledge_manager.html")
def knowledge_manager_html():
    """知识库管理HTML"""
    global app
    return app.send_static_file("knowledge_manager.html")


@knowledge_bp.route("/aicode/knowledge_detail.html")
def knowledge_detail_html():
    """知识库详情HTML"""
    global app
    return app.send_static_file("knowledge_detail.html")


# API路由
@knowledge_bp.route("/aicode/api/projects/<project_id>/knowledge_bases", methods=["GET"])
def api_knowledge_bases(project_id):
    """知识库API"""
    global project_manager

    # 获取项目的知识库（自动创建默认知识库）
    try:
        # 验证项目是否存在
        knowledge_manager = project_manager.get_knowledge_manager(project_id)
        if not knowledge_manager:
            return jsonify({"success": False, "message": "项目不存在"})

        # 获取或创建项目的默认知识库
        kb = knowledge_manager.get_or_create_knowledge_base("kb_documents")

        return jsonify({"success": True, "knowledge_base": kb.to_dict()})
    except Exception as e:
        logging.error(e)
        return jsonify({"success": False, "message": str(e)})


@knowledge_bp.route("/aicode/api/projects/<project_id>/code_knowledge_stats", methods=["GET"])
def api_code_knowledge_stats(project_id):
    """代码知识库统计信息API"""
    global project_manager

    try:
        # 验证项目是否存在
        knowledge_manager = project_manager.get_knowledge_manager(project_id)
        if not knowledge_manager:
            return jsonify({"success": False, "message": "项目不存在"})

        # 获取代码知识库统计信息
        code_kb = knowledge_manager.get_or_create_knowledge_base("kb_code")        
        
        stats = {
            "file_count": getattr(code_kb, 'document_count', 0),
            "chunk_count": getattr(code_kb, 'chunk_count', 0)
        }

        return jsonify({"success": True, "stats": stats})
    except Exception as e:
        logging.error(e)
        return jsonify({"success": False, "message": str(e)})


@knowledge_bp.route("/aicode/api/knowledge_bases/<project_id>", methods=["GET", "DELETE"])
def api_knowledge_base_detail(project_id):
    """知识库详情API"""
    knowledge_manager = project_manager.get_knowledge_manager(project_id)
    if not knowledge_manager:
        return jsonify({"success": False, "message": "项目或知识库不存在"})
    try:
        if request.method == "GET":
            # 获取知识库详情
            kb = knowledge_manager.get_or_create_knowledge_base("kb_documents")
            if not kb:
                return jsonify({"success": False, "message": "知识库不存在"})

            return jsonify({"success": True, "knowledge_base": kb.to_dict()})
        elif request.method == "DELETE":
            # 删除知识库
            success = knowledge_manager.delete_knowledge_base()
            if success:
                return jsonify({"success": True, "message": "知识库删除成功"})
            else:
                return jsonify({"success": False, "message": "知识库删除失败"})
    except Exception as e:
        return jsonify({"success": False, "message": str(e)})


@knowledge_bp.route(
    "/aicode/api/knowledge_bases/<project_id>/documents", methods=["GET", "POST", "DELETE"]
)
def api_knowledge_base_documents(project_id):
    """知识库文档管理API"""
    knowledge_manager = project_manager.get_knowledge_manager(project_id)
    if not knowledge_manager:
        return jsonify({"success": False, "message": "项目或知识库不存在"})

    if request.method == "GET":
        # 获取文档列表
        try:
            documents = knowledge_manager.get_documents()
            return jsonify({"success": True, "documents": documents})
        except Exception as e:
            return jsonify({"success": False, "message": str(e)})

    elif request.method == "POST":
        # 添加文档到知识库
        try:
            # 检查是否是文件上传
            if "file" in request.files:
                # 处理文件上传
                file = request.files["file"]
                if file.filename == "":
                    return jsonify({"success": False, "message": "未选择文件"})

                # 检查文件大小
                file.seek(0, os.SEEK_END)
                file_length = file.tell()
                file.seek(0)
                
                if file_length > Config.MAX_CONTENT_LENGTH:
                    max_size_mb = Config.MAX_CONTENT_LENGTH / (1024 * 1024)
                    return jsonify({
                        "success": False, 
                        "message": f"文件大小超过限制，最大允许 {max_size_mb} MB"
                    })

                # 获取其他表单数据
                title = request.form.get("title", "").strip() or file.filename
                metadata_str = request.form.get("metadata", "{}")
                try:
                    metadata = json.loads(metadata_str)
                except json.JSONDecodeError:
                    metadata = {}

                # 准备额外的元数据
                additional_meta = {
                    "title": title,
                    "metadata": metadata,
                }

                # 如果是JSON文件且有JSON处理选项，添加到元数据中
                if file.filename.lower().endswith('.json'):
                    additional_meta["type"] = metadata.get("type", "json_data")

                # 先保存原始文件，传递额外的元数据
                file_path = knowledge_manager.save_document_file(file, file.filename, additional_meta)
                if not file_path:
                    return jsonify({"success": False, "message": "保存文件失败"})

                knowledge_manager.process_document(file.filename)

                return jsonify(
                    {
                        "success": True,
                        "doc_name": file.filename,
                        "message": "文件上传成功，正在后台处理...",
                        "status": "processing",
                    }
                )

            else:
                # 处理JSON数据（直接文本输入）
                data = request.get_json()
                if not data:
                    return jsonify({"success": False, "message": "请求数据格式错误"})

                title = data.get("title", "").strip()
                content = data.get("content", "").strip()
                metadata = data.get("metadata", {})

                if not title or not content:
                    return jsonify({"success": False, "message": "标题和内容不能为空"})

                doc_meta = knowledge_manager.add_document(title, content, metadata)
                return jsonify(
                    {"success": True, "doc_name": doc_meta.get("name"), "message": "文档添加成功"}
                )
        except Exception as e:
            logging.error(f"添加文档失败: {e}")
            return jsonify({"success": False, "message": str(e)})

    elif request.method == "DELETE":
        # 清空知识库所有文档
        try:
            success = knowledge_manager.clear_knowledge_base()            
            # 继续清空代码知识库
            success = knowledge_manager.clear_code_knowledge_base()
            if success:
                return jsonify({"success": True, "message": "知识库已清空"})
            else:
                return jsonify({"success": False, "message": "清空知识库失败"})
        except Exception as e:
            return jsonify({"success": False, "message": str(e)})


@knowledge_bp.route(
    "/aicode/api/knowledge_bases/<project_id>/documents/<doc_id>", methods=["DELETE"]
)
def api_delete_document(project_id, doc_id):
    """删除单个文档"""
    knowledge_manager = project_manager.get_knowledge_manager(project_id)
    if not knowledge_manager:
        return jsonify({"success": False, "message": "项目或知识库不存在"})
    try:
        # 对文档ID进行URL解码（处理中文文件名）
        import urllib.parse
        decoded_doc_id = urllib.parse.unquote(doc_id)

        success = knowledge_manager.delete_document(decoded_doc_id)
        if success:
            return jsonify({"success": True, "message": "文档删除成功"})
        else:
            return jsonify({"success": False, "message": "文档删除失败"})
    except Exception as e:
        return jsonify({"success": False, "message": str(e)})


@knowledge_bp.route(
    "/aicode/api/knowledge_bases/<project_id>/documents/<doc_id>/rebuild", methods=["POST"]
)
def api_rebuild_document(project_id, doc_id):
    """重建单个文档"""
    knowledge_manager = project_manager.get_knowledge_manager(project_id)
    if not knowledge_manager:
        return jsonify({"success": False, "message": "项目或知识库不存在"})
    try:
        # 对文档ID进行URL解码（处理中文文件名）
        import urllib.parse
        decoded_doc_id = urllib.parse.unquote(doc_id)

        print(f"Rebuilding document: {decoded_doc_id} (raw: {doc_id})")

        # 获取文档元数据
        doc_meta = knowledge_manager.get_document(decoded_doc_id)
        if not doc_meta:
            return jsonify({"success": False, "message": "文档不存在"})

        # 保存原始分块数量，因为删除时需要用到
        original_chunk_count = doc_meta.get("chunk_count", 0)

        # 保存文档元数据副本，因为删除操作会清除元数据
        doc_meta_copy = doc_meta.copy()

        # 重置文档状态和分块数量为重建状态
        doc_meta_copy["status"] = "processing"
        doc_meta_copy["chunk_count"] = 0  # 重置分块数量

        # 保存重置后的状态到元数据
        knowledge_manager.documents_meta[decoded_doc_id] = doc_meta_copy
        knowledge_manager._save_documents_meta()

        # 先删除向量库中的文档数据（不删除物理文件，但要更新知识库统计）
        success = knowledge_manager.delete_document_with_stats(decoded_doc_id, original_chunk_count, delete_physical_file=False)
        if not success:
            return jsonify({"success": False, "message": "删除原文档数据失败"})

        # 重新处理文档（使用保存的元数据副本）
        knowledge_manager.process_document_by_metadata(doc_meta_copy, is_rebuild=True)

        return jsonify({"success": True, "message": "文档重建已开始，正在后台处理"})
    except Exception as e:
        print(f"Rebuild document error: {e}")
        return jsonify({"success": False, "message": str(e)})


@knowledge_bp.route("/aicode/api/knowledge_bases/<project_id>/search", methods=["POST"])
def api_search_documents(project_id):
    """在文档知识库中搜索文档"""
    knowledge_manager = project_manager.get_knowledge_manager(project_id)
    if not knowledge_manager:
        return jsonify({"success": False, "message": "项目或知识库不存在"})
    try:
        data = request.get_json()
        query = data.get("query", "").strip()
        limit = data.get("limit", 10)

        if not query:
            return jsonify({"success": False, "message": "搜索查询不能为空"})

        documents = knowledge_manager.search_documents(query, limit, "kb_documents")
        return jsonify(
            {"success": True, "documents": documents, "total": len(documents)}
        )
    except Exception as e:
        return jsonify({"success": False, "message": str(e)})


@knowledge_bp.route("/aicode/api/knowledge_bases/<project_id>/search_code", methods=["POST"])
def api_search_code(project_id):
    """在代码知识库中搜索代码"""
    knowledge_manager = project_manager.get_knowledge_manager(project_id)
    if not knowledge_manager:
        return jsonify({"success": False, "message": "项目或知识库不存在"})
    try:
        data = request.get_json()
        query = data.get("query", "").strip()
        limit = data.get("limit", 10)

        if not query:
            return jsonify({"success": False, "message": "搜索查询不能为空"})

        documents = knowledge_manager.search_code(query, limit)
        return jsonify(
            {"success": True, "documents": documents, "total": len(documents)}
        )
    except Exception as e:
        return jsonify({"success": False, "message": str(e)})


@knowledge_bp.route("/aicode/api/knowledge_bases/<project_id>/rebuild_code", methods=["POST"])
def api_rebuild_code_knowledge(project_id):
    """重建代码知识库"""
    knowledge_manager = project_manager.get_knowledge_manager(project_id)
    if not knowledge_manager:
        return jsonify({"success": False, "message": "项目或知识库不存在"})
    try:
        result = knowledge_manager.rebuild_code_knowledge_base()
        if result.get("success"):
            return jsonify({"success": True, **result})
        else:
            return jsonify({"success": False, "message": result.get("error", "重建失败")})
    except Exception as e:
        return jsonify({"success": False, "message": str(e)})