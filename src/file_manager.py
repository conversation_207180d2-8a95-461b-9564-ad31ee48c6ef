"""
文件管理器模块
提供项目文件的浏览、预览、下载等功能
"""

import os
import json
import mimetypes
import base64
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional


class FileInfo:
    """文件信息类"""
    
    def __init__(self, path: str, project_root: str):
        self.path = path
        self.project_root = project_root
        self.full_path = os.path.join(project_root, path) if path else project_root
        
        # 基本信息
        self.name = os.path.basename(self.full_path) if path else "根目录"
        self.is_directory = os.path.isdir(self.full_path)
        self.exists = os.path.exists(self.full_path)
        
        if self.exists:
            stat = os.stat(self.full_path)
            self.size = stat.st_size if not self.is_directory else 0
            self.modified_time = datetime.fromtimestamp(stat.st_mtime).isoformat()
        else:
            self.size = 0
            self.modified_time = None
        
        # 文件类型
        self.extension = os.path.splitext(self.name)[1].lower() if not self.is_directory else ""
        self.mime_type = mimetypes.guess_type(self.full_path)[0] if not self.is_directory else None
        
        # AI生成标记（简单判断）
        self.is_ai_generated = self._check_ai_generated()
    
    def _check_ai_generated(self) -> bool:
        """检查是否为AI生成的文件"""
        # 简单的启发式判断
        ai_indicators = [
            'ai_generated',
            'auto_generated',
            'generated_by_ai',
            '.ai.',
            '_ai_'
        ]
        
        path_lower = self.path.lower()
        return any(indicator in path_lower for indicator in ai_indicators)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'path': self.path,
            'name': self.name,
            'is_directory': self.is_directory,
            'size': self.size,
            'modified_time': self.modified_time,
            'extension': self.extension,
            'mime_type': self.mime_type,
            'is_ai_generated': self.is_ai_generated,
            'type': self._get_file_type()
        }
    
    def _get_file_type(self) -> str:
        """获取文件类型分类"""
        if self.is_directory:
            return 'directory'
        
        ext = self.extension.lower()
        
        # 代码文件
        code_extensions = [
            '.py', '.js', '.jsx', '.ts', '.tsx', '.html', '.htm', '.css', '.scss', '.sass', '.less',
            '.java', '.cpp', '.c', '.cc', '.cxx', '.h', '.hpp', '.hxx', '.php', '.rb', '.go',
            '.rs', '.swift', '.kt', '.scala', '.clj', '.hs', '.ml', '.fs', '.vb', '.cs',
            '.sh', '.bash', '.zsh', '.fish', '.ps1', '.bat', '.cmd',
            '.sql', '.pl', '.lua', '.r', '.m', '.mm', '.vue', '.svelte'
        ]
        if ext in code_extensions:
            return 'code'
        
        # 图片文件
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg']
        if ext in image_extensions:
            return 'image'
        
        # 文档文件
        doc_extensions = ['.pdf', '.doc', '.docx', '.txt', '.md']
        if ext in doc_extensions:
            return 'document'
        
        # 数据文件
        data_extensions = ['.json', '.xml', '.csv', '.xlsx']
        if ext in data_extensions:
            return 'data'
        
        return 'other'


class FileManager:
    """文件管理器"""
    
    def __init__(self, project_root: str):
        self.project_root = os.path.abspath(project_root)
        
        # 确保项目根目录存在
        if not os.path.exists(self.project_root):
            os.makedirs(self.project_root, exist_ok=True)
    
    def list_files(self, path: str = "") -> List[FileInfo]:
        """列出指定路径下的文件和文件夹"""
        try:
            target_path = os.path.join(self.project_root, path) if path else self.project_root
            
            # 安全检查：确保路径在项目根目录内
            if not self._is_safe_path(target_path):
                raise ValueError("路径不安全")
            
            if not os.path.exists(target_path):
                return []
            
            if not os.path.isdir(target_path):
                return []
            
            files = []
            for item in os.listdir(target_path):
                # 跳过隐藏文件和特殊目录
                if item.startswith('.') and item not in ['.gitignore', '.env']:
                    continue
                
                item_path = os.path.join(path, item) if path else item
                file_info = FileInfo(item_path, self.project_root)
                files.append(file_info)
            
            # 排序：文件夹在前，然后按名称排序
            files.sort(key=lambda x: (not x.is_directory, x.name.lower()))
            
            return files
            
        except Exception as e:
            print(f"列出文件失败: {e}")
            return []
    
    def get_file_content(self, path: str) -> Optional[Dict[str, Any]]:
        """获取文件内容用于预览"""
        try:
            file_info = FileInfo(path, self.project_root)
            
            if not file_info.exists:
                return {
                    'success': False,
                    'message': '文件不存在',
                    'file_info': None
                }

            if file_info.is_directory:
                return {
                    'success': False,
                    'message': '无法读取目录内容',
                    'file_info': file_info.to_dict()
                }

            # 安全检查
            if not self._is_safe_path(file_info.full_path):
                return {
                    'success': False,
                    'message': '路径不安全',
                    'file_info': file_info.to_dict()
                }
            
            # 检查文件大小（限制预览大文件）
            max_preview_size = 1024 * 1024  # 1MB
            if file_info.size > max_preview_size:
                return {
                    'success': False,
                    'message': '文件过大，无法预览',
                    'file_info': file_info.to_dict()
                }
            
            # 根据文件类型读取内容
            content = None
            file_type = file_info._get_file_type()
            
            if file_type == 'image':
                # 图片文件转为base64
                with open(file_info.full_path, 'rb') as f:
                    content = base64.b64encode(f.read()).decode('utf-8')
            elif file_type in ['code', 'document', 'data']:
                # 文本文件
                try:
                    with open(file_info.full_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                except UnicodeDecodeError:
                    # 尝试其他编码
                    try:
                        with open(file_info.full_path, 'r', encoding='gbk') as f:
                            content = f.read()
                    except UnicodeDecodeError:
                        return {
                            'success': False,
                            'message': '文件编码不支持',
                            'file_info': file_info.to_dict()
                        }
            else:
                # 对于不支持的文件类型，尝试作为文本文件读取
                try:
                    with open(file_info.full_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    file_type = 'document'  # 作为文档类型处理，前端会用markdown模式
                except UnicodeDecodeError:
                    try:
                        with open(file_info.full_path, 'r', encoding='gbk') as f:
                            content = f.read()
                        file_type = 'document'
                    except UnicodeDecodeError:
                        return {
                            'success': False,
                            'message': '文件编码不支持',
                            'file_info': file_info.to_dict()
                        }
                except Exception:
                    return {
                        'success': False,
                        'message': '文件类型不支持预览',
                        'file_info': file_info.to_dict()
                    }
            
            return {
                'success': True,
                'content': content,
                'file_type': file_type,
                'file_info': file_info.to_dict()
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'读取文件失败: {str(e)}',
                'file_info': None
            }
    
    def get_file_path(self, path: str) -> Optional[str]:
        """获取文件的完整路径用于下载"""
        try:
            file_info = FileInfo(path, self.project_root)
            
            if not file_info.exists or file_info.is_directory:
                return None
            
            if not self._is_safe_path(file_info.full_path):
                return None
            
            return file_info.full_path
            
        except Exception:
            return None
    
    def get_project_stats(self) -> Dict[str, Any]:
        """获取项目文件统计信息"""
        try:
            stats = {
                'total_files': 0,
                'total_folders': 0,
                'total_size': 0,
                'ai_generated_files': 0,
                'file_types': {}
            }
            
            def scan_directory(path: str):
                try:
                    for item in os.listdir(path):
                        if item.startswith('.'):
                            continue
                        
                        item_path = os.path.join(path, item)
                        
                        if os.path.isdir(item_path):
                            stats['total_folders'] += 1
                            scan_directory(item_path)
                        else:
                            stats['total_files'] += 1
                            
                            # 文件大小
                            try:
                                size = os.path.getsize(item_path)
                                stats['total_size'] += size
                            except:
                                pass
                            
                            # 文件类型统计
                            ext = os.path.splitext(item)[1].lower()
                            if ext:
                                stats['file_types'][ext] = stats['file_types'].get(ext, 0) + 1
                            
                            # AI生成文件检查
                            rel_path = os.path.relpath(item_path, self.project_root)
                            file_info = FileInfo(rel_path, self.project_root)
                            if file_info.is_ai_generated:
                                stats['ai_generated_files'] += 1
                                
                except Exception:
                    pass
            
            scan_directory(self.project_root)
            return stats
            
        except Exception as e:
            return {
                'total_files': 0,
                'total_folders': 0,
                'total_size': 0,
                'ai_generated_files': 0,
                'file_types': {},
                'error': str(e)
            }
    
    def _is_safe_path(self, path: str) -> bool:
        """检查路径是否安全（在项目根目录内）"""
        try:
            abs_path = os.path.abspath(path)
            return abs_path.startswith(self.project_root)
        except Exception:
            return False
    
    def save_file(self, path: str, content: str) -> Dict[str, Any]:
        """保存文件内容"""
        try:
            file_info = FileInfo(path, self.project_root)

            # 安全检查
            if not self._is_safe_path(file_info.full_path):
                return {
                    'success': False,
                    'message': '路径不安全'
                }

            # 确保目录存在
            dir_path = os.path.dirname(file_info.full_path)
            if dir_path and not os.path.exists(dir_path):
                os.makedirs(dir_path, exist_ok=True)

            # 保存文件
            with open(file_info.full_path, 'w', encoding='utf-8') as f:
                f.write(content)

            return {
                'success': True,
                'message': '文件保存成功',
                'file_info': FileInfo(path, self.project_root).to_dict()
            }

        except Exception as e:
            return {
                'success': False,
                'message': f'保存文件失败: {str(e)}'
            }

    def create_file(self, path: str, content: str = "") -> Dict[str, Any]:
        """创建新文件"""
        try:
            file_info = FileInfo(path, self.project_root)

            # 安全检查
            if not self._is_safe_path(file_info.full_path):
                return {
                    'success': False,
                    'message': '路径不安全'
                }

            # 检查文件是否已存在
            if file_info.exists:
                return {
                    'success': False,
                    'message': '文件已存在'
                }

            # 确保目录存在
            dir_path = os.path.dirname(file_info.full_path)
            if dir_path and not os.path.exists(dir_path):
                os.makedirs(dir_path, exist_ok=True)

            # 创建文件
            with open(file_info.full_path, 'w', encoding='utf-8') as f:
                f.write(content)

            return {
                'success': True,
                'message': '文件创建成功',
                'file_info': FileInfo(path, self.project_root).to_dict()
            }

        except Exception as e:
            return {
                'success': False,
                'message': f'创建文件失败: {str(e)}'
            }

    def create_directory(self, path: str) -> Dict[str, Any]:
        """创建新目录"""
        try:
            target_path = os.path.join(self.project_root, path) if path else self.project_root

            # 安全检查
            if not self._is_safe_path(target_path):
                return {
                    'success': False,
                    'message': '路径不安全'
                }

            # 检查目录是否已存在
            if os.path.exists(target_path):
                return {
                    'success': False,
                    'message': '目录已存在'
                }

            # 创建目录
            os.makedirs(target_path, exist_ok=True)

            return {
                'success': True,
                'message': '目录创建成功',
                'file_info': FileInfo(path, self.project_root).to_dict()
            }

        except Exception as e:
            return {
                'success': False,
                'message': f'创建目录失败: {str(e)}'
            }

    def delete_file(self, path: str) -> Dict[str, Any]:
        """删除文件或目录"""
        try:
            file_info = FileInfo(path, self.project_root)

            # 安全检查
            if not self._is_safe_path(file_info.full_path):
                return {
                    'success': False,
                    'message': '路径不安全'
                }

            if not file_info.exists:
                return {
                    'success': False,
                    'message': '文件或目录不存在'
                }

            # 删除文件或目录
            if file_info.is_directory:
                import shutil
                shutil.rmtree(file_info.full_path)
            else:
                os.remove(file_info.full_path)

            return {
                'success': True,
                'message': '删除成功'
            }

        except Exception as e:
            return {
                'success': False,
                'message': f'删除失败: {str(e)}'
            }

    def rename_file(self, old_path: str, new_path: str) -> Dict[str, Any]:
        """重命名文件或目录"""
        try:
            old_file_info = FileInfo(old_path, self.project_root)
            new_file_info = FileInfo(new_path, self.project_root)

            # 安全检查
            if not self._is_safe_path(old_file_info.full_path) or not self._is_safe_path(new_file_info.full_path):
                return {
                    'success': False,
                    'message': '路径不安全'
                }

            if not old_file_info.exists:
                return {
                    'success': False,
                    'message': '源文件不存在'
                }

            if new_file_info.exists:
                return {
                    'success': False,
                    'message': '目标文件已存在'
                }

            # 确保目标目录存在
            new_dir = os.path.dirname(new_file_info.full_path)
            if new_dir and not os.path.exists(new_dir):
                os.makedirs(new_dir, exist_ok=True)

            # 重命名
            os.rename(old_file_info.full_path, new_file_info.full_path)

            return {
                'success': True,
                'message': '重命名成功',
                'file_info': FileInfo(new_path, self.project_root).to_dict()
            }

        except Exception as e:
            return {
                'success': False,
                'message': f'重命名失败: {str(e)}'
            }

    def create_ai_file_marker(self, file_path: str):
        """为AI生成的文件添加标记"""
        try:
            # 在文件名中添加AI标记或在文件内容中添加注释
            # 这里可以根据需要实现具体的标记逻辑
            pass
        except Exception:
            pass
