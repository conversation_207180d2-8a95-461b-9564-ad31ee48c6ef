#!/usr/bin/env python3
"""
Go 语言分块器
"""
import uuid
from typing import List, Optional, Dict, Any
from tree_sitter import Language, Parser
import tree_sitter_go as tsg
from code_util import CodeChunk

# ---------- 工具 ----------
def _read_lines(path: str) -> List[str]:
    with open(path, encoding="utf-8") as f:
        return f.readlines()

def _node_lines(node, lines: List[str]) -> int:
    start, end = node.start_point[0], node.end_point[0]
    return end - start + 1

def _node_text(node, lines: List[str]) -> str:
    start, end = node.start_point[0], node.end_point[0]
    return "".join(lines[start: end + 1])

def _sliding_window(lines: List[str], start: int, end: int, win: int, overlap: int):
    i = start
    while i <= end:
        chunk_end = min(i + win - 1, end)
        yield i, chunk_end, "".join(lines[i: chunk_end + 1])
        i += win - overlap
        if i + win - overlap > end and i < end:  # 最后一段
            yield i, end, "".join(lines[i: end + 1])
            break

# ---------- 主解析器 ----------
class CodeParserGo:
    """Go 语言分块器类"""
    def __init__(self, code_chunk_size: int = 500):
        self.win_size = code_chunk_size
        self.overlap = int(self.win_size * 0.1)

    def extract_code_chunks(self, file_path: str, content: str, language: str) -> List[CodeChunk]:
        lang = Language(tsg.language())
        parser = Parser(lang)
        lines = _read_lines(file_path)
        tree = parser.parse("".join(lines).encode("utf8"))
        root = tree.root_node

        chunks: List[CodeChunk] = []
        # 累加器
        acc_start, acc_end, acc_text, acc_node = None, None, [], None

        def _get_node_name(node):
            """提取函数名或类型名"""
            type_str = "function" if node.type in ("function_declaration", "method_declaration", "func_literal") else \
                       "class" if node.type in ("type_declaration", "type_spec") else "other"
            name = None
            # Go AST：函数/方法第一个 identifier 就是名字
            if type_str == "function":
                for ch in node.children:
                    if ch.type == "identifier":
                        name = ch.text.decode()
                        break
            # 类型声明：type Foo struct/interface...
            if type_str == "class":
                for ch in node.children:
                    if ch.type == "type_identifier":
                        name = ch.text.decode()
                        break
            return type_str, name

        def _flush_acc(force_type="other"):
            nonlocal acc_end, acc_text, acc_start, acc_node
            if not acc_text or not acc_node or len(acc_text) <= 1:
                return
            type_str, name = _get_node_name(acc_node)
            chunks.append(CodeChunk(
                chunk_id=str(uuid.uuid4()),
                file_path=file_path,
                content="".join(acc_text),
                chunk_type=force_type,
                language="go",
                start_line=acc_start,
                end_line=acc_end,
                class_name=name if type_str == "class" else None,
                function_name=name if type_str == "function" else None,
            ))
            acc_start, acc_end, acc_text, acc_node = None, None, [], None

        # 遍历一级 child
        for node in root.children:
            node_line_cnt = _node_lines(node, lines)
            # 1) 大节点 → 滑动窗口
            if node_line_cnt >= self.win_size:
                _flush_acc()  # 先打包前面累加
                start, end = node.start_point[0], node.end_point[0]
                type_str, name = _get_node_name(node)
                for s, e, text in _sliding_window(lines, start, end, self.win_size, self.overlap):
                    chunks.append(CodeChunk(
                        chunk_id=str(uuid.uuid4()),
                        file_path=file_path,
                        content=text,
                        chunk_type=type_str,
                        language="go",
                        start_line=s,
                        end_line=e,
                        class_name=name if type_str == "class" else None,
                        function_name=name if type_str == "function" else None,
                    ))
                continue
            # 2) 小节点 → 累加
            if acc_start is None:
                acc_start = node.start_point[0]
                acc_node = node
            acc_end = node.end_point[0]
            acc_text.append(_node_text(node, lines))
            # 累加满窗口就切
            if (acc_end - acc_start + 1) >= self.win_size:
                _flush_acc()
        # 尾部剩余
        _flush_acc()
        return chunks