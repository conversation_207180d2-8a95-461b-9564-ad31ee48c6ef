#!/usr/bin/env python3
"""
代码知识库管理器
用于管理项目代码的知识库，支持代码解析、切块和向量存储
"""

import os
import json
import uuid
import hashlib
import re
import logging
import fnmatch
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple

# Tree-sitter相关导入
from tree_sitter import Language, Parser

# 导入代码工具类
from code_util import CodeSymbol, CodeChunk, KnowledgeBase


class KnowledgeCode:
    """代码知识库管理器"""

    def __init__(self, project, embedding_client, milvus_client):
        self.project = project
        self.work_dir = project.work_dir
        self.embedding_client = embedding_client
        self.milvus_client = milvus_client

        # 配置参数
        self.code_chunk_size = 300  # 代码块大小
        self.chunk_overlap = int(self.code_chunk_size * 0.1)  # 滑动窗口重叠

        # 支持的编程语言
        self.supported_languages = {
            ".py": "python",
            ".java": "java",
            ".c": "c",
            ".cpp": "cpp",
            ".cc": "cpp",
            ".cxx": "cpp",
            ".h": "c",
            ".hpp": "cpp",
            ".go": "go",
            ".js": "javascript",
            ".ts": "typescript",
        }

        # 默认忽略的目录和文件
        self.default_ignore_patterns = [
            ".*",  # 隐藏目录和文件
            "node_modules",
            "target",
            "build",
            "dist",
            "__pycache__",
            ".git",
            ".svn",
            ".hg",
            "vendor",
            "deps",
            "third_party",
            "*.pyc",
            "*.class",
            "*.o",
            "*.so",
            "*.dll",
            "*.exe",
            "*.jar",
            "*.war",
            "*.zip",
            "*.tar.gz",
            "*.rar",
            "*.7z",
            "*.pdf",
            "*.doc",
            "*.docx",
            "*.xls",
            "*.xlsx",
            "*.ppt",
            "*.pptx",
            "*.jpg",
            "*.jpeg",
            "*.png",
            "*.gif",
            "*.bmp",
            "*.svg",
            "*.ico",
            "*.mp3",
            "*.mp4",
            "*.avi",
            "*.mov",
            "*.wmv",
            "*.flv",
            "*.mkv",
        ]

        # 加载.gitignore规则
        self.ignore_patterns = self._load_gitignore()

    def _load_gitignore(self) -> List[str]:
        """加载.gitignore文件中的忽略规则"""
        gitignore_path = os.path.join(self.work_dir, ".gitignore")
        patterns = self.default_ignore_patterns.copy()

        if os.path.exists(gitignore_path):
            try:
                with open(gitignore_path, "r", encoding="utf-8") as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith("#"):
                            patterns.append(line)
            except Exception as e:
                print(f"⚠️ 读取.gitignore文件失败: {e}")

        return patterns

    def _should_ignore(self, path: str) -> bool:
        """检查文件或目录是否应该被忽略"""
        # 获取相对路径
        rel_path = os.path.relpath(path, self.work_dir)

        # 判断是否为目录
        is_directory = os.path.isdir(path)

        # 如果设置了包含模式，则先检查是否符合包含模式
        if hasattr(self.project, "include_patterns") and self.project.include_patterns:
            include_patterns = [
                pattern.strip()
                for pattern in self.project.include_patterns.split(",")
                if pattern.strip()
            ]
            included = False

            for pattern in include_patterns:
                # 处理目录模式
                if pattern.endswith("/"):
                    if fnmatch.fnmatch(rel_path + "/", pattern) or fnmatch.fnmatch(
                        os.path.basename(path) + "/", pattern
                    ):
                        included = True
                        break
                else:
                    # 处理文件模式
                    if fnmatch.fnmatch(rel_path, pattern) or fnmatch.fnmatch(
                        os.path.basename(path), pattern
                    ):
                        included = True
                        break

                    # 对于目录，检查是否可能包含匹配的子文件
                    if is_directory:
                        # 检查包含模式是否以当前目录开头
                        if pattern.startswith(rel_path + "/"):
                            included = True
                            break

                    # 检查路径中的任何部分是否匹配
                    path_parts = rel_path.split(os.sep)
                    for part in path_parts:
                        if fnmatch.fnmatch(part, pattern):
                            included = True
                            break
                    if included:
                        break

            # 如果不符合包含模式，则忽略
            if not included:
                return True

        # 合并默认忽略模式和项目特定的排除模式
        all_patterns = self.ignore_patterns[:]
        if hasattr(self.project, "exclude_patterns") and self.project.exclude_patterns:
            # 将逗号分隔的字符串转换为模式列表
            project_patterns = [
                pattern.strip()
                for pattern in self.project.exclude_patterns.split(",")
                if pattern.strip()
            ]
            all_patterns.extend(project_patterns)

        # 检查每个忽略模式
        for pattern in all_patterns:
            # 处理目录模式
            if pattern.endswith("/"):
                # 检查完整路径是否匹配目录模式
                if fnmatch.fnmatch(rel_path + "/", pattern):
                    return True
                if fnmatch.fnmatch(os.path.basename(path) + "/", pattern):
                    return True
                # 检查路径中的任何目录部分是否匹配
                path_parts = rel_path.split(os.sep)
                for part in path_parts:
                    if fnmatch.fnmatch(part + "/", pattern):
                        return True
            else:
                # 处理文件模式
                if fnmatch.fnmatch(rel_path, pattern) or fnmatch.fnmatch(
                    os.path.basename(path), pattern
                ):
                    return True
                # 检查路径中的任何部分是否匹配
                path_parts = rel_path.split(os.sep)
                for part in path_parts:
                    if fnmatch.fnmatch(part, pattern):
                        return True

        return False

    def _get_language_from_extension(self, file_path: str) -> Optional[str]:
        """根据文件扩展名获取编程语言"""
        _, ext = os.path.splitext(file_path)
        return self.supported_languages.get(ext.lower())

    def _is_code_file(self, file_path: str) -> bool:
        """检查是否为代码文件"""
        return self._get_language_from_extension(file_path) is not None

    def _scan_code_files(self) -> List[str]:
        """扫描工作目录中的所有代码文件"""
        code_files = []

        for root, dirs, files in os.walk(self.work_dir):
            # 过滤忽略的目录
            dirs[:] = [
                d for d in dirs if not self._should_ignore(os.path.join(root, d))
            ]

            for file in files:
                file_path = os.path.join(root, file)

                # 检查是否应该忽略
                if self._should_ignore(file_path):
                    continue

                # 检查是否为代码文件
                if self._is_code_file(file_path):
                    code_files.append(file_path)

        return code_files

    def _read_file_content(self, file_path: str) -> Optional[str]:
        """读取文件内容"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                return f.read()
        except UnicodeDecodeError:
            try:
                with open(file_path, "r", encoding="gbk") as f:
                    return f.read()
            except Exception as e:
                print(f"⚠️ 无法读取文件 {file_path}: {e}")
                return None
        except Exception as e:
            print(f"⚠️ 读取文件失败 {file_path}: {e}")
            return None

    def _count_lines(self, content: str) -> int:
        """计算内容的行数"""
        return len(content.split("\n"))

    def _generate_chunk_id(
        self, file_path: str, chunk_type: str, start_line: int
    ) -> str:
        """生成代码块ID"""
        rel_path = os.path.relpath(file_path, self.work_dir)
        return f"{rel_path}_{chunk_type}_{start_line}"

    def build(self, knowledge_base: KnowledgeBase, build_progress=None) -> Dict[str, Any]:
        """构建代码知识库"""
        try:
            collection_name = knowledge_base.collection_name
            print("🔍 开始扫描代码文件...")
            code_files = self._scan_code_files()
            print(f"📁 发现 {len(code_files)} 个代码文件")

            total_chunks = 0
            processed_files = 0

            for file_path in code_files:
                try:
                    chunks = self._process_file(file_path)
                    if chunks:
                        self._store_chunks(chunks, collection_name)
                        total_chunks += len(chunks)
                        processed_files += 1

                        
                        # 构建进度的回调函数
                        if build_progress:
                            build_progress(file_path, total_chunks)
                            
                except Exception as e:
                    print(f"⚠️ 处理文件失败 {file_path}: {e}")
                    continue

            print(
                f"🎉 代码知识库构建完成！处理了 {processed_files} 个文件，生成了 {total_chunks} 个代码块"
            )

            return {
                "success": True,
                "processed_files": processed_files,
                "total_chunks": total_chunks,
                "total_files": len(code_files),
            }

        except Exception as e:
            print(f"❌ 代码知识库构建失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "processed_files": 0,
                "total_chunks": 0,
            }

    def rebuild(self, knowledge_base: KnowledgeBase, build_progress=None) -> Dict[str, Any]:
        """重建代码知识库（先清理再构建）"""
        print("🧹 清理现有代码知识库...")
        if self._clear_code_knowledge_base(knowledge_base.collection_name):
            print("✅ 清理完成，开始重新构建...")
            return self.build(knowledge_base, knowledge_base, build_progress)
        else:
            print("❌ 清理失败")
            return {
                "success": False,
                "error": "清理代码知识库失败",
                "processed_files": 0,
                "total_chunks": 0,
            }

    def _clear_code_knowledge_base(self, collection_name: str = "kb_code") -> bool:
        """清理代码知识库中的所有数据"""
        try:
            # 删除collection中的所有数据
            if self.milvus_client and self.milvus_client.has_collection(
                collection_name
            ):
                self.milvus_client.delete(
                    collection_name=collection_name,
                    filter="id != ''",  # 匹配所有记录
                )
                print(f"✅ 已清理代码知识库 {collection_name}")
                return True
            else:
                print(f"⚠️ 代码知识库 {collection_name} 不存在")
                return True
        except Exception as e:
            print(f"⚠️ 清理代码知识库失败: {e}")
            return False

    def _process_file(self, file_path: str) -> List[CodeChunk]:
        """处理单个文件，生成代码块"""
        content = self._read_file_content(file_path)
        if not content:
            return []

        language = self._get_language_from_extension(file_path)
        if not language:
            return []

        line_count = self._count_lines(content)
        rel_path = os.path.relpath(file_path, self.work_dir)

        # 小文件直接作为一个块
        if line_count <= self.code_chunk_size:
            chunk_id = self._generate_chunk_id(file_path, "file", 1)
            chunk = CodeChunk(
                chunk_id=chunk_id,
                file_path=rel_path,
                content=content,
                chunk_type="file",
                language=language,
                start_line=1,
                end_line=line_count,
                metadata={"file_size": line_count},
            )
            return [chunk]

        # 接口文件或大文件按函数/方法切分
        return self._parse_with_tree_sitter(file_path, content, language)

    def _parse_with_tree_sitter(
        self, file_path: str, content: str, language: str
    ) -> List[CodeChunk]:
        """使用tree-sitter解析代码"""
        try:
            # 获取对应语言的解析器
            parser = self._get_parser(language)
            if not parser:
                print(f"⚠️ 不支持的语言: {language}，使用正则解析")
                return self._parse_with_lines(file_path, content, language)
            rel_path = os.path.relpath(file_path, self.work_dir)
            return parser.extract_code_chunks(file_path, content, language)
        except Exception as e:
            print(f"⚠️ tree-sitter解析失败 {file_path}: {e}")
            return self._parse_with_lines(file_path, content, language)

    def _get_parser(self, language_name: str) -> Optional[Any]:
        """获取指定语言的tree-sitter解析器"""
        try:
            parser = None
            if language_name == "c" or language_name == "cpp":
                from code_parser_c import CodeParserC

                parser = CodeParserC(self.code_chunk_size)
            elif language_name == "java":
                from code_parser_java import CodeParserJava

                parser = CodeParserJava(self.code_chunk_size)
            elif language_name == "go":
                from code_parser_go import CodeParserGo

                parser = CodeParserGo(self.code_chunk_size)
            elif language_name == "python":
                from code_parser_python import CodeParserPython

                parser = CodeParserPython(self.code_chunk_size)
            return parser
        except Exception as e:
            logging.error(f"⚠️ 无法创建{language_name}解析器: {e}")
            return None

    def _parse_with_lines(
        self, file_path: str, content: str, language: str
    ) -> List[CodeChunk]:
        """按行拆分代码块"""
        start_line = 0
        chunks = []
        lines = content.split("\n")
        rel_path = os.path.relpath(file_path, self.work_dir)

        window_start = 0
        chunk_index = 0

        while window_start < len(lines):
            window_end = min(window_start + self.code_chunk_size, len(lines))
            window_content = "\n".join(lines[window_start:window_end])

            chunk_id = self._generate_chunk_id(
                file_path, f"function_part_{chunk_index}", start_line + window_start
            )
            chunk = CodeChunk(
                chunk_id=chunk_id,
                file_path=rel_path,
                content=window_content,
                chunk_type="function_part",
                language=language,
                start_line=start_line + window_start,
                end_line=start_line + window_end - 1,
                function_name="other",
                metadata={
                    "is_partial": True,
                    "part_index": chunk_index,
                    "window_size": window_end - window_start,
                },
            )
            chunks.append(chunk)

            # 移动窗口
            window_start += self.code_chunk_size - self.chunk_overlap
            chunk_index += 1

        return chunks

    def _store_chunks(
        self, chunks: List[CodeChunk], collection_name: str = "kb_code"
    ) -> bool:
        """存储代码块到向量数据库"""
        try:
            for chunk in chunks:
                # 生成向量
                embedding = self._generate_embedding(chunk.file_path, chunk.content)
                if not embedding:
                    continue

                # 准备数据
                data = [
                    {
                        "id": chunk.chunk_id,
                        "title": f"{chunk.file_path}:{chunk.function_name or 'file'}",
                        "language": chunk.language,
                        "content": chunk.content,
                        "embedding": embedding,
                        "metadata": json.dumps(
                            {
                                "file_path": chunk.file_path,
                                "chunk_type": chunk.chunk_type,
                                "language": chunk.language,
                                "start_line": chunk.start_line,
                                "end_line": chunk.end_line,
                                "function_name": chunk.function_name,
                                "class_name": chunk.class_name,
                                # **chunk.metadata ,
                            },
                            ensure_ascii=False,
                        ),
                    }
                ]

                # 存储到Milvus
                self.milvus_client.insert(collection_name=collection_name, data=data)

            return True
        except Exception as e:
            logging.error(f"⚠️ 存储代码块失败: {e}")
            return False

    def _generate_embedding(self, file_path: str, text: str) -> List[float]:
        """生成文本的向量表示"""
        try:
            # 导入配置
            try:
                from .config import Config
            except ImportError:
                from config import Config

            response = self.embedding_client.embeddings.create(
                model=Config.EMBEDDING_MODEL, input=text  # 使用配置中的模型
            )
            return response.data[0].embedding
        except Exception as e:
            logging.error(f"⚠️ {file_path} - 生成向量失败: {e}")
            return []
