import asyncio
import json
import logging
import os
from pyexpat.errors import messages
import subprocess
import sqlite3
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable, Union
import uuid
from asgiref.sync import async_to_sync

# 导入配置
try:
    from ..config import Config
    from ..knowledge_manager import KnowledgeManager
except ImportError:
    from config import Config
    from knowledge_manager import KnowledgeManager

try:
    from .llm_client import LLMClient
    from .tool_manager import Tool, ToolCall, ToolManager
except ImportError:
    from llm_client import LLMClient
    from tool_manager import Tool, ToolCall, ToolManager

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ModelType(Enum):
    """模型类型枚举"""

    OPUS = "claude-3-opus"
    SONNET = "claude-3-sonnet"
    HAIKU = "claude-3-haiku"


class SessionStatus(Enum):
    """会话状态枚举"""

    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    ERROR = "error"


@dataclass
class Message:
    """消息数据结构"""

    id: str
    role: str  # "user", "assistant", "system"
    content: str
    timestamp: datetime
    metadata: Dict[str, Any] = None

    def to_dict(self):
        return {
            #"id": self.id,
            "role": self.role,
            "content": self.content,
            #"timestamp": self.timestamp.isoformat(),
            #"metadata": self.metadata or {},
        }


class SessionManager:
    """会话管理器"""

    def __init__(
        self, enable_session: bool = True, db_path: str = "data/llm_sessions.db"
    ):
        self.enable_session = enable_session
        self.db_path = db_path
        if self.enable_session:
            self._init_database()

    def _init_database(self):
        """初始化数据库"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute(
                """
                CREATE TABLE IF NOT EXISTS sessions (
                    id TEXT PRIMARY KEY,
                    created_at TIMESTAMP,
                    updated_at TIMESTAMP,
                    status TEXT,
                    metadata TEXT
                )
            """
            )

            conn.execute(
                """
                CREATE TABLE IF NOT EXISTS messages (
                    id TEXT PRIMARY KEY,
                    session_id TEXT,
                    role TEXT,
                    content TEXT,
                    timestamp TIMESTAMP,
                    metadata TEXT,
                    FOREIGN KEY (session_id) REFERENCES sessions (id)
                )
            """
            )

    def create_session(self, session_id=None) -> str:
        """创建新会话"""
        if session_id is None:
            session_id = str(uuid.uuid4())
        else:
            session_id = session_id
        if not self.enable_session:
            return session_id
        now = datetime.now()

        with sqlite3.connect(self.db_path) as conn:
            conn.execute(
                """
                INSERT INTO sessions (id, created_at, updated_at, status, metadata)
                VALUES (?, ?, ?, ?, ?)            """,
                (session_id, now, now, SessionStatus.ACTIVE.value, "{}"),
            )

        logger.info(f"Created new session: {session_id}")
        return session_id

    def resume_session(self, session_id: str) -> List[Message]:
        """恢复会话"""
        if not self.enable_session:
            return []
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 检查会话是否存在
                cursor = conn.execute(
                    "SELECT * FROM sessions WHERE id = ?", (session_id,)
                )
                result = cursor.fetchone()
                if not result:
                    # 创建会话
                    self.create_session(session_id)
                    return []

                # 加载消息历史
                cursor = conn.execute(
                    """
                    SELECT id, role, content, timestamp, metadata
                    FROM messages WHERE session_id = ? 
                    ORDER BY timestamp
                """,
                    (session_id,),
                )

                messages = []
                for row in cursor.fetchall():
                    message = Message(
                        id=row[0],
                        role=row[1],
                        content=row[2],
                        timestamp=datetime.fromisoformat(row[3]),
                        metadata=json.loads(row[4]) if row[4] else None,
                    )
                    messages.append(message)

                logger.info(f"Resumed session: {session_id}")
                return [{"role": msg.role, "content": msg.content} for msg in messages]

        except Exception as e:
            logger.error(f"Error resuming session: {e}")
            return []

    def add_message(
        self,
        session_id: str,
        role: str,
        content: str,
        messages=None,
        metadata: Dict[str, Any] = None,
    ) -> List[Dict]:
        """添加消息到当前会话"""
        if self.enable_session and not messages:
            messages = self.resume_session(session_id)
        if not messages:
            messages = []

        message = Message(
            id=str(uuid.uuid4()),
            role=role,
            content=content,
            timestamp=datetime.now(),
            metadata=metadata,
        )

        if self.enable_session:
            # 保存到数据库
            with sqlite3.connect(self.db_path) as conn:
                conn.execute(
                    """
                    INSERT INTO messages (id, session_id, role, content, timestamp, metadata)
                    VALUES (?, ?, ?, ?, ?, ?)
                """,
                    (
                        message.id,
                        session_id,
                        message.role,
                        message.content,
                        message.timestamp.isoformat(),
                        json.dumps(message.metadata) if message.metadata else None,
                    ),
                )

                # 更新会话时间戳
                conn.execute(
                    """
                    UPDATE sessions SET updated_at = ? WHERE id = ?
                """,
                    (datetime.now(), session_id),
                )

        messages.append(message)

        # 检查是否需要压缩对话
        # if (self.settings.app_gen.auto_compact and
        #     len(messages) > self.settings.app_gen.compact_threshold):
        #     self.compact_conversation(session_id, messages)

        return messages

    def get_conversation_history(
        self, session_id: str, limit: Optional[int] = None
    ) -> List[Message]:
        """获取对话历史"""
        return self.resume_session(session_id)

    def compact_conversation(
        self, session_id: str, messages: list[Message], target_length: int = 10
    ) -> List[Message]:
        """压缩对话历史"""
        if len(messages) <= target_length:
            return messages

        # 保留系统消息和最近的消息
        system_messages = [m for m in messages if m.role == "system"]
        recent_messages = messages[-target_length:]

        messages = system_messages + recent_messages
        # 同步删除数据库中的消息: 根据保留消息ID，删除数据库
        reserved_message_ids = [m.id for m in messages]
        if self.enable_session:
            with sqlite3.connect(self.db_path) as conn:
                # 修复IN操作符的参数绑定问题
                if reserved_message_ids:
                    placeholders = ",".join("?" * len(reserved_message_ids))
                    conn.execute(
                        f"""
                        DELETE FROM messages WHERE id NOT IN ({placeholders})
                    """,
                        reserved_message_ids,
                    )
                else:
                    conn.execute(
                        """
                        DELETE FROM messages
                    """
                    )

        logger.info(f"Compacted conversation to {len(messages)} messages")
        return messages

    def get_session_info(self, session_id) -> Dict[str, Any]:
        """获取会话信息"""
        messages = self.resume_session(session_id)
        return {
            "session_id": session_id,
            "message_count": len(messages),
            "created_at": self.session_manager.session_metadata.get("created_at"),
            "last_updated": self.session_manager.session_metadata.get("last_updated"),
        }


class LLMAgent:
    """代码生成智能体主类"""

    def __init__(
        self,
        system_prompt: str,
        knowledge_manager: KnowledgeManager = None,
        enable_session: bool = False,
    ):
        self.system_prompt = system_prompt
        self.llm_client = LLMClient()
        self.knowledge_manager = knowledge_manager
        self.session_manager = SessionManager(enable_session)

        self.tool_manager = ToolManager()
        class DocRagTool(Tool):
            """文档知识库检索工具"""

            @property
            def name(self) -> str:
                return "search_documents"

            @property
            def description(self) -> str:
                return f"""检索项目文档知识库的工具。
                    当您需要了解项目知识、优化需求内容、生成设计、生成任务时，可以调用此工具搜索与项目相关的知识。
                    返回值说明:
                        List[Dict[str, Any]]: 多个文档块，每个文档块包含以下字段：
                            - id: 块ID
                            - content: 块内容
                            - score : 块内容的匹配得分
                            - metadata : 块的元数据"""

            def _get_parameters_schema(self) -> Dict[str, Any]:
                return {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "检索关键词"},
                        "limit": {"type": "integer", "description": "可返回的文档块数"},
                    },
                    "required": ["query"],
                }

            async def execute(self, args: Dict[str, Any]) -> str:
                # logging.info(f"调用search_documents工具:{args}")
                result = knowledge_manager.search_documents(
                    args.get("query"), args.get("limit", 5)
                )
                if result:
                    return str(result)
                else:
                    return "无相关内容"
        
        class CodeRagTool(Tool):
            """代码知识库检索工具"""

            @property
            def name(self) -> str:
                return "search_codes"

            @property
            def description(self) -> str:
                return f"""检索项目代码库的工具。
            当您需要了解项目各功能的代码实现、参考/复用已有代码的功能实现、编写详细设计方案等场景时，可以调用此工具搜索与项目相关的代码。
            返回值说明:
                List[Dict[str, Any]]: 多个代码块，每个代码块包含以下字段：
                    - id: 块ID
                    - content: 块内容
                    - score : 块内容的匹配得分
                    - metadata : 块的元数据"""

            def _get_parameters_schema(self) -> Dict[str, Any]:
                return {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "检索关键词"},
                        "limit": {"type": "integer", "description": "可返回的代码块数"},
                    },
                    "required": ["query"],
                }

            async def execute(self, args: Dict[str, Any]) -> str:
                # logging.info(f"调用search_documents工具:{args}")
                result = knowledge_manager.search_documents(
                    args.get("query"), args.get("limit", 5)
                )
                if result:
                    return str(result)
                else:
                    return "无相关内容"
        
        if knowledge_manager and knowledge_manager.get_or_create_knowledge_base("kb_code").chunk_count > 0:
            self.tool_manager.add_tool(CodeRagTool())
        if knowledge_manager and knowledge_manager.get_or_create_knowledge_base("kb_documents").chunk_count > 0:
            self.tool_manager.add_tool(DocRagTool())

        #logger.info("LLMAgent initialized")

    def run_agent_sync(
        self, work_dir, user_req, session_id=None, nothink="", log_callback=None
    ) -> Dict[str, Any]:
        run_sync = async_to_sync(self.run_agent)
        return run_sync(work_dir, user_req, session_id, nothink, log_callback)
    async def run_agent(
        self, work_dir, user_req, session_id=None, nothink="", log_callback=None
    ) -> Dict[str, str]:
        """处理用户消息"""
        messages = None
        if not self.session_manager.resume_session(session_id):
            # 新会话， 系统提示词在第1个
            messages = self.session_manager.add_message(
                session_id, "system", self.system_prompt, messages
            )
        # 添加用户消息
        messages = self.session_manager.add_message(
            session_id, "user", user_req + nothink, messages
        )

        log_count = 0
        # 获取工具schemas
        tools = self.tool_manager.list_tools()        
        content = ""
        def stream_callback(chunk):
            nonlocal content, log_count
            #logging.info(f"{content}")
            if not log_callback:
                return
            if chunk == "<end></end>":
                log_callback(log_count, "💬", content)
                log_count += 1
            content += chunk

        # 开始对话循环
        max_iterations = 10
        iteration = 0
        while iteration < max_iterations:
            iteration += 1
            # 生成AI响应
            response = await self.llm_client.chat_completion(
                messages,
                tools=tools,
                tool_choice="auto",
                is_stream=True,
                stream_callback=stream_callback,
            )
            if "error" in response:
                return {"success": False, "message": response.get("error", "未知错误")}

            # 检查是否有工具调用
            tool_calls = response.get("tool_calls", [])

            # 处理工具调用
            tool_results = []
            if tool_calls:
                for tool_call_data in tool_calls:
                    arguments_str = tool_call_data["function"]["arguments"]
                    tool_call = ToolCall(
                        id=tool_call_data.get("id", ""),
                        name=tool_call_data["function"]["name"],
                        parameters=arguments_str,
                    )
                    #logger.info(f"调用工具: {tool_call.name}: 参数：{arguments_str}")
                    result = await self.tool_manager.execute_tool(tool_call)
                    if log_callback:
                        log_callback(log_count, "🔧", f" 调用工具: {tool_call.name} 的结果: {result[:50]}")
                        log_count += 1
                    # self.session_manager.add_message(session_id, "assistant", content=result)
                    messages = self.session_manager.add_message(
                        session_id,
                        "assistant",
                        f'已调用工具:tool_call_id={tool_call_data.get("id")}, 结果: {result}',
                        messages,
                    )
                    # tool_results.append(f"Tool: {tool_call.name}\nResult: {result}")
            else:
                if not response.get("content", ""):
                    # 未返回有效内容，让大模型继续执行
                    logger.warning("未返回有效内容，让大模型继续执行")
                    messages = self.session_manager.add_message(
                        session_id, "assistant", "未返回有效内容，继续执行", messages
                    )
                    continue
                else:
                    messages = self.session_manager.add_message(
                        session_id, "assistant", response["content"], messages
                    )
                    return {"success": True, "message": response.get("content", "")}

        return {"success": False, "message": "工具交互已达到最大轮次，请重新开始。"}

    def get_session_info(self, session_id) -> Dict[str, Any]:
        """获取会话信息"""
        return self.session_manager.get_session_info(session_id)
