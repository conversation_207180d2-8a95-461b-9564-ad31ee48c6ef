# WARP.md

This file provides guidance to WARP (warp.dev) when working with code in this repository.

## System Overview

This is an AI-powered task management system built with Python Flask that integrates with Claude AI agents for automated task generation and execution. The system provides a web interface for managing projects, requirements, designs, tasks, and knowledge bases.

### Key Components
- **Flask Web Application**: Main web interface with HTML frontend
- **Project Manager**: Handles project CRUD and metadata storage
- **Task Manager**: Manages task generation, execution, and monitoring using Claude AI
- **Knowledge Manager**: Vector database integration with Milvus for RAG capabilities
- **Claude Agent Integration**: Uses Claude Agent SDK for intelligent code generation and task execution
- **File System Management**: Handles project file operations with permission controls

### Architecture Pattern
The system follows a modular architecture with managers for different concerns:
- Data persistence via JSON files in configurable data directory
- Task execution through Claude Agent SDK with streaming responses
- Knowledge base integration for contextual task generation
- Multi-provider LLM support (local, zhipu, claude) with fallback capabilities

## Development Commands

### Installation and Setup
```bash
# Install Python dependencies
pip install -r requirements.txt

# Install Node.js dependencies (for <PERSON> SDK)
npm install

# Copy environment template
cp .env.example .env
# Edit .env with your API keys and provider configurations
```

### Running the Application
```bash
# Start the development server
python run_app.py

# With custom configuration
python run_app.py --host 127.0.0.1 --port 8000 --debug --data-dir ./custom_data

# View all available options
python run_app.py --help
```

### Testing
```bash
# Run project manager tests
python tests/test_project_manager.py

# Run knowledge manager tests  
python tests/test_knowledge_code.py

# Run Claude integration test
python tests/claude_test.py
```

### Development Tasks
```bash
# Run single test file
python -m pytest tests/test_project_manager.py -v

# Test a specific component in isolation
cd src && python -c "from project_manager import ProjectManager; pm = ProjectManager('test_data'); print('OK')"

# Manual testing of task generation
cd tests && python claude_test.py
```

## Configuration

### Environment Variables (.env)
The system uses dynamic provider configuration through environment variables:

**Provider Configuration Pattern:**
```
PROVIDER_{PROVIDER_NAME}_{CONFIG_KEY}=value
```

**Required Variables:**
- `PROVIDER_LOCAL_BASE_URL`: API endpoint for local LLM
- `PROVIDER_LOCAL_AUTH_TOKEN`: Authentication token  
- `PROVIDER_LOCAL_MODEL`: Model name (e.g., "glm-4.5-air")
- `EMBEDDING_API_KEY`: API key for embedding service
- `EMBEDDING_BASE_URL`: Embedding service endpoint

### Claude Integration Setup
The system automatically creates Claude configuration files for each project:
- `.claude/settings.local.json`: Claude SDK settings with token limits
- `CLAUDE.md`: Project-specific rules and constraints for Claude agents

## Data Structure

### Project Data Storage
- **Location**: Configurable via `--data-dir` (default: `./data`)
- **Projects**: `data/projects.json` - Project metadata and configurations
- **Tasks**: `{project_dir}/.taskai/task.json` - Per-project task definitions
- **Requirements**: `{project_dir}/.taskai/requirement.md` - Project requirements
- **Designs**: `{project_dir}/.taskai/design.md` - Design specifications
- **Logs**: `data/logs/` - Execution and LLM interaction logs

### Knowledge Base Integration
- **Vector DB**: Milvus Lite for document embeddings
- **Storage**: `{AI_WORK_DIR}/vector_db/` directory
- **Supported Formats**: PDF, Word, Markdown, text files
- **Chunking**: Configurable size (default: 1000 chars, 200 overlap)

## Key APIs and Endpoints

### Core Project Management
- `POST /api/projects` - Create new project
- `GET /api/projects/<id>` - Get project details
- `PUT /api/projects/<id>/requirement` - Update requirements
- `POST /api/projects/<id>/generate_tasks` - AI task generation

### Task Execution
- `POST /api/projects/<id>/run_tasks` - Execute all project tasks
- `POST /api/projects/<id>/tasks/<task_id>/run` - Run single task
- `POST /api/projects/<id>/tasks/<task_id>/continue` - Continue task execution
- `POST /api/projects/<id>/stop_execution` - Stop task execution

### Configuration Management  
- `GET /api/config/providers` - List available LLM providers
- `POST /api/config/save` - Save environment configuration

## Development Guidelines

### Task Types and System Prompts
The system supports different task types with specialized prompts:
- **代码重构** (Code Refactoring): Architecture and refactoring expert
- **PMO需求** (PMO Requirements): Project management expert  
- **新功能** (New Features): Product development expert
- **代码分析** (Code Analysis): Code review and analysis expert
- **其他** (Other): Full-stack development expert

### Claude Agent Integration
- Uses `claude-agent-sdk` for streaming interactions
- Implements file permission controls restricting access to project directories
- Supports session resumption for continued task execution
- Configurable timeouts (default: 10 minutes)

### Error Handling and Fallbacks
- Multi-provider fallback support with configurable sleep intervals
- Comprehensive logging via `loguru` 
- Graceful degradation when secondary providers fail

### File Management Patterns
- AI-generated files are marked and tracked
- Project-specific `.claude/` configuration directories
- Automatic `.taskai/` workspace creation for each project