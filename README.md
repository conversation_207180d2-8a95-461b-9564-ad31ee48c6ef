# AI任务管理系统（全栈助手）

一个完整的AI任务管理系统，支持项目管理、需求管理、设计管理、规则管理、任务管理、知识库管理和文件预览等功能。该系统集成了多种LLM服务，支持代码生成、文档优化、任务自动化等AI驱动的开发流程。

## 🚀 快速开始

### 1. 系统要求

- Python 3.7+
- Docker (可选，用于容器化部署)
- Git (用于代码管理功能)
- Node.js (用于Claude工具集成)

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 环境配置

复制并配置环境变量文件：

```bash
cp .env.example .env
```

编辑`.env`文件，配置LLM Providers、Embedding服务和其他相关配置。

### 4. 启动系统

```bash
python run_app.py
```

默认访问地址：http://localhost:5005

### 5. 启动参数

```bash
python run_app.py --help
```

可用参数：
- `--host`: 服务器主机地址（默认：0.0.0.0）
- `--port`: 主应用端口（默认：5005）
- `--xterm-port`: 终端服务端口（默认：5006）
- `--debug`: 启用调试模式

## 📚 文档资源

- [用户使用手册](docs/用户使用手册.md) - 完整的系统使用指南
- [快速入门指南](docs/快速入门指南.md) - 快速上手教程
- [API接口文档](docs/API接口文档.md) - 开发者接口文档

## 📋 功能特性

### 🏗️ 项目管理
- ✅ 创建、查看、编辑、删除项目
- ✅ 项目目录管理
- ✅ 项目统计信息
- ✅ 项目文件浏览
- ✅ 多种LLM Provider支持（内网、智谱、Claude等）
- ✅ 多种项目类型支持（产品迭代、BUG修复、PMO需求、代码分析等）
- ✅ Git集成管理（支持克隆、提交、推送代码）

### 📝 需求管理
- ✅ 创建、查看、编辑需求
- ✅ 需求内容优化（AI自动优化需求文档）
- ✅ 需求与任务关联
- ✅ Markdown格式编辑器（Cherry Markdown）
- ✅ 需求文档模板支持

### 🎨 设计管理
- ✅ 设计文档创建和管理
- ✅ 根据需求自动生成设计文档（AI驱动）
- ✅ Markdown格式编辑器
- ✅ 设计文档模板支持

### 🛡️ 规则管理
- ✅ 项目规则约束定义
- ✅ 规则内容管理
- ✅ Markdown格式编辑器

### ⚡ 任务管理
- ✅ AI自动任务生成（基于需求或设计）
- ✅ 任务依赖关系管理
- ✅ 任务优先级设置
- ✅ 任务状态管理（待处理、运行中、已完成、失败、禁用）
- ✅ 批量任务操作
- ✅ 单个任务执行和继续执行
- ✅ 任务启用/禁用切换
- ✅ 子任务管理（支持智能分解和手动创建）
- ✅ 任务会话保持功能

### 🧠 知识库管理
- ✅ 项目知识库创建和管理
- ✅ 文档上传（支持PDF、Word、Markdown、文本文件）
- ✅ 向量存储和检索（基于Milvus）
- ✅ 知识库搜索功能
- ✅ 代码知识库（基于Tree-sitter的多语言代码解析）

### 🎯 执行控制
- ✅ 并行/顺序执行模式
- ✅ 任务执行控制（启动/停止）
- ✅ 实时进度监控
- ✅ 执行参数配置
- ✅ 任务重置功能
- ✅ 模型降级机制（当主模型失败时自动切换到备用模型）

### 📊 日志管理
- ✅ 全程执行日志记录
- ✅ LLM交互日志记录
- ✅ 日志级别筛选
- ✅ 日志搜索功能
- ✅ 实时日志显示
- ✅ 日志自动清理机制

### 📁 文件预览
- ✅ 项目文件浏览
- ✅ 多格式文件预览（代码、文档、图片等）
- ✅ AI生成文件标记
- ✅ 文件下载功能
- ✅ 文件统计信息

### 💻 终端管理
- ✅ Web终端（基于xterm.js）
- ✅ 项目隔离的终端会话
- ✅ AI命令集成（通过/ai命令直接调用AI）
- ✅ 终端会话自动清理

### 🔐 用户认证
- ✅ 用户登录/登出
- ✅ 密码修改
- ✅ 会话管理

## 🏗️ 系统架构

```
AI任务管理系统/
├── src/                    # 核心源码
│   ├── project_manager.py  # 项目管理
│   ├── task_manager.py     # 任务管理
│   ├── log_manager.py      # 日志管理
│   ├── file_manager.py     # 文件管理
│   ├── knowledge_manager.py # 知识库管理
│   ├── document_manager.py # 文档管理（需求优化、设计生成）
│   ├── claude/             # Claude Agent SDK集成
│   ├── config.py           # 配置管理
│   └── web_app.py          # Web应用
├── static/                 # 静态文件和HTML页面
│   ├── index.html          # 首页
│   ├── projects.html       # 项目管理页面
│   ├── project_tasks.html  # 任务管理页面
│   ├── project_files.html  # 文件浏览页面
│   ├── requirement_manager.html # 需求管理页面
│   ├── design_manager.html # 设计管理页面
│   ├── rules_manager.html  # 规则管理页面
│   ├── knowledge_manager.html # 知识库管理页面
│   └── task_llm_logs.html  # LLM日志页面
├── data/                   # 数据存储
└── run_app.py             # 启动脚本
```

## 💻 使用指南

### 1. 创建项目

1. 访问系统首页
2. 点击"新建项目"
3. 填写项目名称、工作目录、LLM Provider和项目类型
4. 可配置Git管理选项（克隆远程仓库）
5. 点击"创建"完成

### 2. 管理需求

1. 在项目列表中选择项目
2. 点击"需求管理"
3. 编辑需求内容
4. 可使用"一键优化"功能优化需求
5. 可使用"生成设计"功能根据需求生成设计文档

### 3. 管理设计

1. 在项目列表中选择项目
2. 点击"设计管理"
3. 编辑设计内容
4. 可根据设计生成任务

### 4. 设置规则

1. 在项目列表中选择项目
2. 点击"规则管理"
3. 定义项目的规则约束

### 5. 生成任务

1. 在需求或设计页面点击"生成任务"
2. 设置生成参数（可选）
3. 点击"确认生成"完成

### 6. 执行任务

1. 进入项目任务管理页面
2. 配置执行参数
3. 选择执行模式（并行/顺序）
4. 点击"运行任务"开始执行

### 7. 查看结果

1. 在任务管理页面查看实时进度
2. 查看执行日志和LLM交互日志
3. 在文件浏览器中查看生成的文件
4. 在知识库管理中查看相关知识文档

## 🔧 开发指南

### 运行测试

目前系统测试功能正在开发中。

### 模块说明

#### ProjectManager
- 项目和需求的CRUD操作
- 数据持久化存储
- 项目目录管理
- 项目任务生成和执行控制
- Git集成管理

#### TaskManager
- 任务的CRUD操作
- 依赖关系管理
- 任务执行控制
- 与LLM交互生成任务
- 子任务管理

#### LogManager
- 执行日志记录
- LLM交互日志记录
- 日志查询和筛选
- 日志持久化存储

#### FileManager
- 文件系统操作
- 文件预览功能
- AI文件检测
- 文件统计信息

#### KnowledgeManager
- 知识库管理
- 文档向量化存储
- 知识检索功能
- 代码知识库管理

#### DocumentManager
- 需求优化
- 设计文档生成
- 模板管理

#### ClaudeAgent
- Claude SDK集成
- AI能力封装
- 工具调用管理

### API接口

#### 项目管理API
```
GET  /aicode/api/projects              # 获取项目列表
POST /aicode/api/projects              # 创建项目
GET  /aicode/api/projects/<id>         # 获取项目详情
PUT  /aicode/api/projects/<id>         # 更新项目
DELETE /aicode/api/projects/<id>       # 删除项目
```

#### 项目需求API
```
GET  /aicode/api/projects/<id>/requirement  # 获取项目需求
PUT  /aicode/api/projects/<id>/requirement  # 更新项目需求
POST /aicode/api/projects/<id>/optimize_requirement # 优化需求内容
```

#### 项目设计API
```
GET  /aicode/api/projects/<id>/design  # 获取项目设计
PUT  /aicode/api/projects/<id>/design  # 更新项目设计
POST /aicode/api/projects/<id>/gen_design # 生成设计内容
```

#### 项目规则API
```
GET  /aicode/api/projects/<id>/rules   # 获取项目规则
PUT  /aicode/api/projects/<id>/rules   # 更新项目规则
```

#### 任务管理API
```
POST /aicode/api/projects/<id>/generate_tasks  # 生成任务
GET  /aicode/api/projects/<id>/tasks          # 获取任务列表
POST /aicode/api/projects/<id>/run_tasks      # 执行任务
POST /aicode/api/projects/<id>/stop_execution  # 停止任务执行
POST /aicode/api/projects/<id>/tasks/reset    # 重置任务
```

#### 单个任务操作API
```
POST /aicode/api/projects/<id>/tasks          # 添加新任务
PUT  /aicode/api/projects/<id>/tasks/<task_id> # 更新任务
DELETE /aicode/api/projects/<id>/tasks/<task_id> # 删除任务
POST /aicode/api/projects/<id>/tasks/<task_id>/run # 运行单个任务
POST /aicode/api/projects/<id>/tasks/<task_id>/continue # 继续执行任务
POST /aicode/api/projects/<id>/tasks/<task_id>/toggle # 切换任务状态
```

#### 子任务管理API
```
GET  /aicode/api/projects/<id>/tasks/<task_id>/subtasks          # 获取子任务列表
POST /aicode/api/projects/<id>/tasks/<task_id>/subtasks          # 创建子任务
GET  /aicode/api/projects/<id>/tasks/<task_id>/subtasks/<subtask_id> # 获取子任务详情
PUT  /aicode/api/projects/<id>/tasks/<task_id>/subtasks/<subtask_id> # 更新子任务
DELETE /aicode/api/projects/<id>/tasks/<task_id>/subtasks/<subtask_id> # 删除子任务
POST /aicode/api/projects/<id>/tasks/<task_id>/subtasks/smart_split # 智能分解任务
DELETE /aicode/api/projects/<id>/tasks/<task_id>/subtasks/delete_all # 删除所有子任务
```

#### 文件管理API
```
GET /aicode/api/projects/<id>/files               # 获取文件列表
GET /aicode/api/projects/<id>/files/content       # 获取文件内容
GET /aicode/api/projects/<id>/files/stats         # 获取文件统计信息
```

#### 知识库管理API
```
GET /aicode/api/projects/<id>/knowledge_bases     # 获取项目知识库
GET /aicode/api/knowledge_bases/<kb_id>           # 获取知识库详情
DELETE /aicode/api/knowledge_bases/<kb_id>        # 删除知识库
GET /aicode/api/knowledge_bases/<kb_id>/documents # 获取知识库文档列表
POST /aicode/api/knowledge_bases/<kb_id>/documents # 添加文档到知识库
DELETE /aicode/api/knowledge_bases/<kb_id>/documents # 清空知识库文档
```

#### 终端管理API
```
GET /aiterm/xterm.html?project_id=<id>  # 打开项目终端
```

#### 用户认证API
```
POST /aicode/api/auth/login             # 用户登录
POST /aicode/api/auth/logout            # 用户登出
GET  /aicode/api/auth/current_user      # 获取当前用户信息
POST /aicode/api/auth/change_password   # 修改密码
```

## 🐳 Docker部署

系统提供了Dockerfile用于容器化部署：

```bash
# 构建镜像
docker build -t aicode:latest .

# 运行容器
docker run -d --name aicode \
  -v /opt/aicode/data:/opt/data \
  -p 5005:5005 -p 5006:5006 \
  --add-host ai.secsign.online:*********** \
  aicode:latest
```

## 🛠️ 技术栈

- **后端**: Python Flask, Claude Agent SDK
- **前端**: Bootstrap 5, jQuery, xterm.js
- **数据库**: Milvus Lite (向量数据库)
- **AI服务**: 支持多种LLM Providers (Claude兼容API, OpenAI等)
- **代码解析**: Tree-sitter (多语言支持)
- **文件处理**: PyPDF2, python-docx
- **实时通信**: Socket.IO

## 🐛 故障排除

### 常见问题

1. **导入错误**
   - 检查Python版本（需要3.7+）
   - 安装必要依赖：`pip install -r requirements.txt`

2. **文件权限错误**
   - 确保数据目录有写入权限
   - 检查项目目录权限

3. **端口占用**
   - 使用不同端口：`python run_app.py --port 8000`
   - 检查端口占用：`netstat -an | grep 5005`

4. **模块找不到**
   - 确保在项目根目录运行
   - 检查文件结构完整性

### 日志查看

系统日志保存在 `data/logs/` 目录下，可以查看详细的错误信息。

### 重置系统

删除 `data/` 目录可以重置所有数据：

```bash
rm -rf data/
```

## 📄 许可证

本项目采用MIT许可证。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📞 支持

如果遇到问题，请：

1. 查看系统日志
2. 提交Issue描述问题

---

**AI任务管理系统** - 让AI任务管理更简单、更高效！