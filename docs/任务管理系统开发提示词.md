完成以下全部需求，不用询问，直接依次完成:

# 完善本AI任务管理系统
1. 完成代码中的TODO的任务
2. 自动化顺序执行任务过程中，可以停止任务执行。
3. 增加任务执行的日志管理功能，可通过progress_callback记录日志
4. 增加项目管理功能，包括：项目名称、工作目录、需求等
  - 4.1 每个项目可以有多个需求，每个需求会调用新TaskManager实现AI任务管理功能。
  
5. 增加UI界面管理，可实现项目管理、需求管理、任务管理。
  - 5.1 项目管理：新增项目、查看项目、删除项目(删除关联需求)
  - 5.2 需求管理：新增需求、查看需求、删除需求(删除关联任务)
  - 5.3 任务管理：任务拆解、查看任务、修改任务、删除任务（需要检查依赖）
  - 5.3.1 任务运行管理：启动任务（可选择自动化执行还是顺序执行）、停止任务、查看任务进度、查看任务日志等
  - 5.3.2 任务可以重置状态，然后可以再次执行
  - 5.3.3.只有pending状态的任务可以修改
  - 5.3.4.任务拆解后，也可以增加新的任务
  - 5.3.5.可以实现快速任务，一句话描述需求，系统自动生成任务并启动运行。
  - 5.3.6.任务可以重新开始，会使用新的会话ID，并总结之前的结果。
6. 可在线预览项目目录的各类文件，包括AI生成的文件。

# 迭代1
完成以下全部功能，不用询问，直接依次完成:

1. "增加项目"的界面使用表单界面，而不是一个一个对话框输入属性。[text](../.claude)
2. TaskManager init方法session_id从self.task_file_name的meta.session_id中获取。
4. 修改当前项目和需求的逻辑：
   - 删除需求管理功能
   - 每个项目只管理一个需求，不需要独立的需求管理功能，把需求直接作为项目属性即可
   - 项目需求以markdown渲染和编辑，编辑框尽量大
5. 项目列表界面改为“列表”形式，而不用表框：
   - 可显示：项目名称、创建时间、更新时间（检查项目目录/.aitest/tasks.json文件的meta数据-可参考/mnt/d/agent/auto-claude-tasks/demo/.taskai/task.json）、任务数量、已完成任务数
   - 选中一个项目可以：查看编辑项目的详情，包括项目的需求，可以选中生成/重新生成任务，可以跳转到任务列表。
   - 选中项目生成任务时，如果已经有任务，提示：已经有任务是否重新生成？如果任务在运行中，则需要中断任务运行。
6. 任务列表是与一个项目关联的：
   - 可显示项目目录/.aitest/tasks.json文件中的各任务详情
   - pending状态的任务可编辑、删除（需检查依赖）
7. 修复错误：
   - AttributeError: 'TaskManager' object has no attribute 'get_task_logs'
   - "GET /requirement/85242ce3-c852-49f4-8ec3-8a2cc1cf8fd6 HTTP/1.1" 500

完成以下全部功能，不用询问:
# 迭代2
1. 项目属性里要包括：工作目录，并可以在界面修改、查看详情。
2. 不要再修改TaskManager的init函数，不要自己生成session_id
3. TaskManager增加listTasks方法，返回所有任务(加载self.task_file_name文件的内容)，项目的任务列表调用这个方法查看任务。
4. 生成任务对话框，增加一个任务数量的输入框，可空。
5. 任务列表页面，点击任务，编辑和查看详情都提示：任务不存在
6. 任务列表页面，点击某个任务，可以运行（需要检查依赖）
7. 任务列表页面，点击某个任务，可查看这个任务的运行日志（可通过progress_callback捕获任务的运行日志）
8. 任务执行、完成等流程后，需要修改任务的状态
9. 解决错误：项目删除时 AttributeError: 'ProjectManager' object has no attribute 'requirements'
10. 任务列表可增加任务，弹出任务框输入新的任务，并可选是否立即执行。

完成以下全部功能，不用询问:
# 迭代3
1. 项目列表增加一个项目工作目录文件预览功能
  - 左侧树型结构的展示项目的文件目录
  - 点击文件，可在右侧预览文件内容，支持java/c/json/markdown/python等类型文件的优化展示
2. 在任何代码里都不要用uuid生成session_id，只能从task_file_name变量的文件里获取
  - task_file_name = 项目工作目录的 "/.taskai/task.json"文件，不要修改这个路径。
3. task_manager.py中任务属性的名称请以文件中/mnt/d/agent/auto-claude-tasks/demo/.taskai/task.json的为准进行修改。
4. 前端UI选中任务，查看日志错误： "GET /api/projects/fd23027e-aaf1-4d2b-ac63-ce93c0d5a354/tasks//logs HTTP/1.1" 405。
  - 通过progress_callback捕获任务的运行日志-大模型的返回消息，参考/mnt/d/agent/auto-claude-tasks/src/claude/claude_agent.py中调用progress_callback输出的内容。
  - 日志存储到data目录，可在界面查看运行日志

# 迭代4
完成以下全部功能，不用询问:

1. 优化任务的LLM交互日志管理功能：
  - LLM日志文件的写入过程参见：task_manager.py的_run_single_task方法
  - 当前所有log_manager.log_event()的日志写入都改用logging的info、warning、error、debug方法写入
  - LLM交互日志的格式可参考文件: data/logs/csdk项目C语言重构/task_1.log
  - UI界面的日志查看以友好格式显示LLM的交互日志：分左右，左侧是LLM的各种类型的输出（Assistant/User/Result/Stream），右侧是用户请求(Request)
  - 任务日志的展示列表中每一条最多显示3行(换行符标识为 "↵ ")，超过3行则显示"...还有n行"，然后点击查看所有行。展开所有行也可以收起。
  - 日志查看页面能需要实时刷新
  - LLM日志管理的功能可以都抽取到log_manager中（不要保留当前log_manager功能，当前log_manager功能都直接使用logging即可）。  

2. 项目的任务列表增加：重置功能，可以把项目的所有任务状态修改为pending状态，并去除result属性，设置meta的session_id属性=None。
3. 任务列表UI增加：任务的更新时间
4. 任务列表UI的任务修改功能，优化为与新增任务类似：表单样式展示任务的全部内容，可修改并保存。
5. 前端UI不要使用render_template渲染，修改为前端UI是纯静态页面，通过ajax请求后端REST API交互数据。
6. 解决以下错误：
  - 文件预览功能错误：   加载文件列表失败: 未知错误  "GET /api/projects/1758977931726/files?path= HTTP/1.1" 500

  问题1：
  1. 界面的重构基于当前的templates目录下的所有html文件，不是在static目录下另外创建新的UI界面。
  2. 完善之前的功能要求，通过浏览器检查功能是否正常。

  改进1:
  完成以下全部功能，不用询问:
  1. templates/project_tasks.html的日志查看修改为查看任务的LLM交互日志（src/log_manager.py的LLMLogManager）
  2. LLM交互日志采用独立的页面，日志格式参考文件: data/logs/俄罗斯方块/task_1.log
     - LLM日志页面，每条日志支持多行展示：把换行符↵ 替换为 在界面可以换行的样式
     - 优化展示样式，根据不同的类型(Request/Assistant/User/Result/Stream)显示不同的颜色(浅色系)
     - 每一条日志默认最多显示3行(换行符标识为 "↵ ")，超过3行则显示"...还有n行"，然后点击查看所有行。展开所有行也可以收起。
     - 在LLM交互页面，运行状态的任务支持实时刷新日志：只获取最新更新的日志，不要拉取整个日志。
  3. templates/project_tasks.html任务列表页面，编辑任务应该弹出任务编辑的表单进行编辑，可参考任务详情。
  4. 完成以上功能后，通过浏览器检查功能是否正常。

   改进2：
  完成以下全部功能，不用询问:
   1.重构templates目录下的所有html和src/web_app.py，不使用render_template渲染页面，修改为前后端分离模式：前端页面通过ajax调用后端api的方式，所有js脚本放在static目录下,可以让各html复用。
   2.保留render_template模式下的所有功能，web_app.py中已有/api的功能需要修改为与render_template模式相同的功能。 
   3.重构完毕，删除web_app.py中render_template模式的方法

# 迭代5
完成以下全部功能，不用询问:
1. 如图（docs/界面示意.png）所示重构界面, 左上角可下拉选择项目后作为当前项目，左边菜单的操作都是与当前这个项目相关的。
  - 项目管理列表，也可以选择一个项目切换为当前项目
  - 左侧菜单可以收起、展开
2. 需求管理菜单：对当前项目的需求(requirement)进行markdown格式编辑、保存，并支持预览功能。
3. 设计管理菜单：对当前项目的设计进行markdown格式编辑、保存，并支持预览功能。
  - src/project_manager.py中Project增加设计文档的属性
4. 规则管理：对当前项目的约束规则（rules_constraint）进行markdown格式编辑、保存，并支持预览功能。
5. 需求和设计管理的makrdown编辑功能，可复用一套编辑功能。
6. makrdown编辑功能示意如图："docs/markdown编辑区.png"
   - 顶部的操作栏窄一些，留出最大的编辑区域
   - 右上角的切换支持：仅编辑、仅预览、编辑+预览混合
   - 设计管理时：没有“生成设计”按钮
   - 规则管理时：只有“保存”按钮
7. 任务管理菜单保持现在static/project_tasks.html的功能不变。

 问题： 回退之前的修改，docs/界面示意.png只是一个示意图，就是在左上部增加一个当前项目的下拉框，可选择当前项目，然后左侧菜单的：需求管理、设计管理、规则管理、任务管理都是该项目的。
 继续完成以下任务，不用询问:
2. 需求管理菜单：对当前项目的需求(requirement)进行markdown格式编辑、保存，并支持预览功能。
3. 设计管理菜单：对当前项目的设计进行markdown格式编辑、保存，并支持预览功能。
  - src/project_manager.py中Project增加设计文档的属性
4. 规则管理：对当前项目的约束规则（rules_constraint）进行markdown格式编辑、保存，并支持预览功能。
5. 需求和设计管理的makrdown编辑功能，可复用一套编辑功能。
6. makrdown编辑功能示意如图："docs/markdown编辑区.png"
   - 顶部的操作栏窄一些，留出最大的编辑区域
   - 右上角的切换支持：仅编辑、仅预览、编辑+预览混合
   - 设计管理时：没有“生成设计”按钮
   - 规则管理时：只有“保存”按钮
7. 任务管理菜单保持现在static/project_tasks.html的功能不变。

解决以下问题，不用询问:：
1.点击左侧的需求管理,发送api调用的网络请求，返回：
{requirement: "使用html生成一个俄罗斯方块游戏"}，但右侧区域是空白。
项目管理菜单、设计管理菜单、规则管理菜单、任务管理菜单右侧区域也都是空白。
2.记住当前项目，访问主页是自动切换到当前项目，不要每次都下拉选择项目。
3.解决全部问题后，通过浏览器检查功能是否正常。

解决以下问题，不用询问：
1.如图所示，左侧菜单点击项目管理，右侧里又套了一层菜单，应该只显示项目列表。
2.左侧任务管理菜单，右侧显示的与"项目管理"菜单，应该修改为static/project_tasks.html的任务列表，但是要移除这个页面里的多余菜单(<!-- 侧边栏 -->部分)，只保留任务列表及任务列表上方的各类任务操作按钮。




# 迭代6
实现以下功能，不用询问：
增加知识库管理功能，关联到项目
   - 使用Milvus Lite的python包：pip install milvus-lite pymilvus
   - 左侧菜单增加知识库管理菜单（与当前项目关联），点击后打开一个页面，页面上显示当前项目的知识库列表，每个列表项可点击进入知识库详情页面。
   - 界面支持知识库检索测试

 解决以下问题，无限询问：
 1. 点击左侧知识库管理菜单，右侧区域（static/knowledge_manager.html）空白，控制台错误：
 
2. 请解决这个错误，并检查知识库的功能
- 知识库是与 static/index.html中下拉选择的项目关联的
- static/knowledge_manager.html显示当前项目的知识库列表，每个列表项是一个已经加到知识库里的文档，可删除
- 知识库列表显示文档的统计信息：大小、分块数量等
- static/knowledge_manager.html页面可上传文档到知识库
- 知识库界面支持检索功能，可检索当前知识库的信息

问题：
1. static/knowledge_manager.html中不需要新建知识库这些操作，每个项目默认都是自动创建一个知识库，这个界面管理的就是index.html中下拉选择项目的知识库。

问题：
按照static/knowledge.html的UI设计，修改static/knowledge_manager.html，并与当前后端服务对接，确保功能正常

问题：
1.static/knowledge.html上传文档，浏览器控制台错误：
jquery-3.6.0.min.js:2  POST http://127.0.0.1:5005/api/knowledge_bases/51b3e164-77a4-4147-ba83-3aacf4425051/documents net::ERR_CONNECTION_RESET
2.请解决这个错误，并检查知识库的功能是否真实实现，而不是模拟数据

问题：
root - ERROR - 添加文档失败: Milvus not available

问题:
1.上传文档弹出框没有消失，控制台错误：

2.上传文档并没有真实添加到Milvus中
3.文档添加到向量数据库时，可以使用如下openai 兼容的模型：
BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
model="text-embedding-v4"
api_key=sk-1d06e75d7fd94338b5b32cf8f9099651

问题
static/knowledge_manager.html界面的知识库搜索后的弹出框没有消失，控制台错误：

# 迭代7
完成以下任务，无需询问：
1. 优化文档上传到知识库的处理逻辑(src/knowledge_ui.py)：首先上传文档的原始文件存储到src/knowledge_manager.py的documents目录下，然后再后台任务解析文档内容、分块加到milvus向量数据库中
2. 文本分块的逻辑参考debug/knowledge_example.py
  - 注意1：只参考knowledge_example的分块处理逻辑，不要使用它的向量数据库。
  - 注意2：KnowledgeManager是单个项目的知识库，参考的knowledge_example是管理多个项目的知识库.
3. 分块的分隔符MARKDOWN_SEPARATOR在.env中配置，由src/config.py管理
4. 根据官网说明https://pypi.org/project/milvus-lite/2.5.1/，检查milvus向量数据库的使用方式，并修正完善
5. 检查知识库前端UI的逻辑是否正确（src/knowledge_ui.py、static/js/knowledge.js、static/knowledge_manager.html）

# 迭代8
完成以下任务，无需询问：
需求：src/knowledge_manager.py增加项目代码的知识库构建功能，具体要求：
1. KnowledgeManager使用新的collection名称:kb_code做为代码知识库的名称：
  - vector_db/knowledge_bases.json文件支持多个KnowledgeBase的管理
  - 在get_or_create_knowledge_base方法默认创建kb_code知识库的collection
2. 代码知识库的构建、搜索、清理等功能使用独立的knowledge_code.py文件，并由KnowledgeManager管理
  - KnowledgeCode实例的初始化参数包括：project、embedding_client、milvus_client
  - 其中embedding_client、milvus_client的初始化在KnowledgeManager中完成
  - KnowledgeCode提供build方法，用于代码知识库的构建
  - 支持rebuild方法，用于清理向量数据库中的数据，然后调用build方法重新构建向量数据库
  - KnowledgeManager提供代码库的构建、搜索、清理方法，并调用KnowledgeCode方法
3. KnowledgeCode的代码知识库构建逻辑：
  - 遍历project.work_dir的代码文件，使用tree-sitte语法树解析库，切分代码块，并添加到milvus向量数据库中
  - 扫描文件时需检查work_dir下的.gitignore目录，忽略这些文件和目录的扫描。如果没有.gitignore文件，则默认忽略非代码文件，如：使用.前缀的目录、node_modules、target、build目录、其他目录（可补充）及图片、视频、代码库、文档等所有非代码文件。
  - 代码切块的逻辑：a.接口文件和小于100行（可配置）的小文件做为独立的一个块，b.大文件按照函数/方法切分，每个函数/方法作为一个块 c.超大函数（大于1000行-可配置）采用滑动窗口拆分为多个块。
  - 支持java、c/c++、go语言、python语言的代码解析(使用不同的tree-sitte库)
4. tests目录下增加一个测试用例，使用：/mnt/d/aicode/csdkc/做为项目work_dir，实际测试代码库的构建、搜索功能。

# 迭代9
1. 参考src/document_manager.py及askAI方法，增加一个shell_manager, 用于处理shell命令中的ai请求，不需要深度思考。
2. shell_manager的ai，只处理如下场景：用户想执行一个shell操作，但不知道具体的命令，可以让AI返回shell命令。比如：用户想查询主板型号，ai可以返回：cat /sys/devices/virtual/dmi/id/board_name。
3. web_app.py中截获 /ai 开头的请求，并调用shell_manager，把当前操作系统及版本信息和请求发给shell_manager，让AI只返回具体的命令（包括参数），不要返回其他信息。
4. web_app.py在把ai的返回结果返回到浏览器前端。

# 迭代10

完成以下任务，无需询问：
1. 实现文件管理功能,可以对当前项目的文件进行增删改查。
2. 在index.html的左侧增加文件管理菜单，右侧显示文件树及文件编辑页面。
3. 右侧区域的总体样式类似vscode这类IDE编辑器，左边是文件的资源管理，右侧是选中文件后的编辑区。
4. 文件编辑区支持代码语法编辑高亮，可以采用pyodide.js+codemirror.css，请网络搜索使用方法，并把依赖的js和css下载到本项目的static/external,使用本地链接。
5. 后端提供file_manager_ui.py和file_manager.py，实现文件管理功能，可对选中项目的各类文件操作。file_manager_ui.py和file_manager.py的实现参考 document_manager.py和 document_ui.py：每个FileManager实例管理一个项目下的所有文件，ProjectManager实例管理多个FileManager实例。
6. 文件管理前端UI的总体组织方式、与后端的交互方式参考已有功能。

错误1：
文件管理页面选中文件后，编辑区域没有任何显示，浏览器控制台错误：codemirror.js:1 Uncaught TypeError: Ar[n.options.scrollbarStyle] is not a constructor
    at Dr (codemirror.js:1:59468)
    at new cl (codemirror.js:1:130518)
    at cl (codemirror.js:1:130158)
    at cl.fromTextArea (codemirror.js:1:169501)
错误2：
1.c语言、java语言等文件没有语法高亮，.h文件提示：文件类型不支持预览。要求：支持c语言、java语言、python语言、go语言、shell语言的代码文件。不支持的文件用markdown文件格式打开。
2.树型文件展示有错乱，如：/mnt/d/aicode/zentaopms/javapms/zentao-java/src目录，点击src后，该目录下的main子目录缩进位置比src还更靠前，继续点击main下的java，与main同样的缩进位置，继续点击java及下层的各目录，也是同样的缩进位置。
3.打开文件编辑窗口后浏览器控制台偶尔又错误：VM176 content_script.js:7202 Uncaught (in promise) fetchError: Request timeout after 30000ms
    at q8 (VM176 content_script.js:7202:119658)
    at Yf.sendMessage (VM176 content_script.js:7202:119087)
    at async content_script.js:7224:16607
4.文件树形UI的新建功能没有图标展示    

错误3：
1. static/file_manager.html树型文件的展示，在到了/mnt/d/aicode/zentaopms/javapms/zentao-java/src/main/java/com/easycorp这个层级再往下打开子目录，都没有缩进，与easycorp在一个缩进位置，请修复这个问题。
2. 浏览器控制台还是会有错误：chrome-extension://b…tent_script.js:7202 Uncaught (in promise) fetchError: Request timeout after 30000ms
    at q8 (VM431 content_script.js:7202:119658)
    at Yf.sendMessage (VM431 content_script.js:7202:119087)
    at async VM431 content_script.js:7224:16607
    请搜索网络查找原因和解决方案。
3.



# 迭代11
完成以下任务，无需询问：
1. task_manager.py的Task增加子任务管理，子任务属性包括：子任务id、名称、描述、检查项、状态。
2. 前端界面project_tasks.html的任务列表项，增加子任务的管理按钮，可以弹出子任务列表页面，实现子任务的增删改查
3. 子任务列表页，提供“智能分解”的功能，可以由后台服务对将任务进行智能拆解。
3. 任务列表页, 当任务有子任务时，点击任务标题也可以弹出子任务列表页面。

# 迭代12
1. 增加用户管理和登录权限管理功能。
2. 系统只支持一个用户的登录，用户名密码存储在.env文件中。
3. 在index.html的配置管理菜单下增加一个用户信息菜单，可以修改密码。
4. 用户登录的会话超时时间可以在.env中配置，默认24小时。
5. 在超时时间内不需要再次登录，即使系统重启也不需要登录。

# 迭代13-交互式AI
1. 项目work_dir目录下的CLAUDE.md包括了该项目的介绍和一些规则，在chatAI()方法可以把这个文件内容放在系统提示词，帮助llm更好的理解用户的需求。
2. 前端static/js/markdown-editor.js的showAiChat方法新增加了后10行文本，把这个上下文补充到后端的chatAI()方法

# 迭代14 - 规则初始化
1. static/rules_manager.html中定制一个"初始化"菜单，调用后台src/document_manager.py的初始化规则服务。
2. 初始化规则服务，调用ClaudeAgent的方法，用户请求是： /init，将生成的规则返回到前端。


# 迭代14-文档模板管理
1. src/task_manager.py中使用的gen_task.md、src/document_manager.py中_load_template使用的模板，都修改为根据项目类型从prompts/{项目类型目录}下加载。
2. 示例：项目类型=PMO，则生成任务的模板、需求文档模板、设计文档模板都从prompts/PMO目录下加载，其中生成任务的模板文件=gen_task.md，需求文档模板=requirement.md，设计文档模板=design.md。
3. 如果prompts目录下没有这个项目类型的子目录，则从prompts/default目录下加载。

4. 优化prompts/新产品/gen_task.md的提示词，在拆解需求时，每个需求点都要有任务能实现，可以一个任务处理1个或多个需求，也可以多个任务处理一个需求。需求功能模板可以参考prompts/新产品/requirement.md
  。对于各功能通用的需求，可以在"meta"中体现，并在执行任务时，从meta中提取出来附加到项目work_dir的CLAUDE.md中。 

# 迭代15-git管理
1. Project增加git地址、access token、分支名称，前端UI(static/projects.html)新增项目时, 如果有git管理，可以输入这些属性。
2. git管理的项目，默认工作目录是：当前系统的工作目录/data/{git地址项目名}
3. git管理的项目，在新建项目后，会检查工作目录是否为空，非空提示错误。如果为空则自动从git仓库及指定的分支拉取最新代码，到项目的工作目录，并切换到新的分支名：用户的输入的分支_{当前日期时间戳}，同时也会修改项目的分支名称。
4. 新建项目UI跳转布局(修改、查看同步调整)：
  -第1行： 项目名称、项目类型、git管理选项
  -第2行： 项目描述(占据3行高度)
  -第3行： 如果有git输入：git地址、access token、分支名称
  -第4行： 工作目录、排除模式、包含模式
  -第5行： LLM Provider

5. src目录下ProjectManager的数据目录、LLMLogManager的数据目录，使用.env中的DATA_DIR变量(通过config.py加载)，默认为:./data

# 迭代16-HTML应用访问
1. 前端static/js/projects.js的项目列表，增加一个访问项目HTML应用的按钮，点击按钮后，打开一个新窗口，访问项目HTML应用。
2. 后端增加新的project_app.py，增加访问项目应用的路由，可以路由访问这个项目work_dir下的静态文件，包括html、js、css等。并且需要自动搜索这个项目work_dir下的首页html，用于打开这个项目的HTML应用。
3. project_app.py的路由需要在web_app.py以blueprint方式注册，参考src/knowledge_ui.py的注册方式。

# 迭代17-交互式AI
1. 参考static/task_llm_logs.html实现一个交互式AI界面，任务列表增加一个操作，可以进入与这个任务会话的交互界面。
2. 初始进入交互界面，可以显示最近的5条日志，向上点击“显示更多”可以再显示10条，直到所有日志显示完毕。
3. 用户可以输入新的问题，发送到后端src/task_manager.py的run_chat()方法，可以继续与AI的交互，AI的回复即时输出到前端UI。
4. 根据即时响应的要求，可优化run_chat方法