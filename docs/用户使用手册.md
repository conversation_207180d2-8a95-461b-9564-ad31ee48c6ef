# AI任务管理系统用户使用手册

## 目录

1. [系统概述](#系统概述)
2. [系统要求](#系统要求)
3. [安装与部署](#安装与部署)
4. [系统功能详解](#系统功能详解)
   - [用户认证](#用户认证)
   - [项目管理](#项目管理)
   - [需求管理](#需求管理)
   - [设计管理](#设计管理)
   - [规则管理](#规则管理)
   - [任务管理](#任务管理)
   - [知识库管理](#知识库管理)
   - [文件管理](#文件管理)
   - [终端管理](#终端管理)
   - [日志管理](#日志管理)
5. [使用流程](#使用流程)
6. [常见问题与故障排除](#常见问题与故障排除)
7. [技术支持](#技术支持)

## 系统概述

AI任务管理系统是一个集成AI能力的全栈项目管理平台，旨在帮助开发团队自动化项目开发流程。系统集成了多种大语言模型(LLM)服务，支持代码生成、文档优化、任务自动化等AI驱动的开发功能。

### 核心功能

1. **项目管理** - 创建和管理项目，支持多种项目类型
2. **需求管理** - 编写和优化需求文档
3. **设计管理** - 自动生成系统设计文档
4. **规则管理** - 定义项目约束和规范
5. **任务管理** - AI自动生成和执行开发任务
6. **知识库管理** - 管理项目文档和代码知识
7. **文件管理** - 浏览和预览项目文件
8. **终端管理** - Web终端集成
9. **日志管理** - 全程执行日志记录

## 系统要求

### 硬件要求

- CPU: 2核以上
- 内存: 4GB以上
- 硬盘: 10GB以上可用空间

### 软件要求

- 操作系统: Linux (推荐Ubuntu 22.04)、Windows 10/11、macOS 10.15+
- Python版本: 3.7+
- Docker (可选，用于容器化部署)
- Git (用于代码管理功能)
- Node.js (用于Claude工具集成)

## 安装与部署

### 方法一：直接安装运行

1. 克隆项目代码：
   ```bash
   git clone <项目地址>
   cd auto-claude-tasks
   ```

2. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```

3. 配置环境变量：
   ```bash
   cp .env.example .env
   # 编辑.env文件，配置LLM Providers、Embedding服务等
   ```

4. 启动系统：
   ```bash
   python run_app.py
   ```

### 方法二：Docker容器化部署

1. 构建镜像：
   ```bash
   docker build -t aicode:latest .
   ```

2. 运行容器：
   ```bash
   docker run -d --name aicode \
     -v /opt/aicode/data:/opt/data \
     -p 5005:5005 -p 5006:5006 \
     --add-host ai.secsign.online:*********** \
     aicode:latest
   ```

### 访问系统

启动后，默认访问地址为：http://localhost:5005

默认用户名：admin
默认密码：123456

## 系统功能详解

### 用户认证

#### 登录系统

1. 打开浏览器访问系统地址
2. 输入用户名和密码（默认：admin/123456）
3. 点击"登录"按钮

#### 修改密码

1. 登录系统后，点击右上角用户头像
2. 选择"用户信息"
3. 输入原密码和新密码
4. 点击"修改密码"

#### 退出登录

点击右上角用户头像，选择"退出登录"

### 项目管理

#### 创建项目

1. 登录系统后，在左侧菜单点击"项目管理"
2. 点击"新建项目"按钮
3. 填写项目信息：
   - 项目名称（必填）
   - 项目类型（产品迭代、BUG修复、PMO、代码分析、其他）
   - 项目描述
   - Git管理（无Git管理、提交代码、提交代码并推送）
   - 工作目录
   - 排除模式和包含模式
   - LLM Provider
4. 点击"创建项目"

#### 编辑项目

1. 在项目列表中找到要编辑的项目
2. 点击项目行的"编辑"按钮
3. 修改项目信息
4. 点击"保存"

#### 删除项目

1. 在项目列表中找到要删除的项目
2. 点击项目行的"删除"按钮
3. 确认删除操作

### 需求管理

#### 编写需求

1. 在项目列表中点击要管理的项目
2. 点击"需求管理"
3. 在编辑器中编写需求内容

#### 优化需求

1. 在需求管理页面，点击工具栏的"AI"按钮
2. 选择"一键优化"
3. 系统将自动优化需求文档

#### 生成设计

1. 在需求管理页面，点击工具栏的"AI"按钮
2. 选择"生成设计"
3. 系统将根据需求自动生成设计文档

#### 生成任务

1. 在需求管理页面，点击工具栏的"AI"按钮
2. 选择"生成任务"
3. 设置任务数量和特殊说明
4. 点击"确认生成"

### 设计管理

#### 编写设计

1. 在项目列表中点击要管理的项目
2. 点击"设计管理"
3. 在编辑器中编写设计内容

#### 生成任务

1. 在设计管理页面，点击工具栏的"AI"按钮
2. 选择"生成任务"
3. 设置任务数量和特殊说明
4. 点击"确认生成"

### 规则管理

#### 设置规则

1. 在项目列表中点击要管理的项目
2. 点击"规则管理"
3. 在编辑器中编写项目规则约束
4. 点击"保存"

### 任务管理

#### 查看任务

1. 在项目列表中点击要管理的项目
2. 点击"任务管理"
3. 系统将显示所有生成的任务

#### 运行任务

1. 在任务管理页面，点击"运行任务"按钮
2. 选择执行模式（并行/顺序）
3. 点击"确认运行"

#### 添加任务

1. 在任务管理页面，点击"添加任务"按钮
2. 填写任务信息：
   - 任务标题（必填）
   - 任务描述
   - 测试策略
   - 依赖任务
   - 保持会话
3. 点击"添加任务"

#### 编辑任务

1. 在任务列表中找到要编辑的任务
2. 点击任务行的"编辑"按钮
3. 修改任务信息
4. 点击"保存"

#### 删除任务

1. 在任务列表中找到要删除的任务
2. 点击任务行的"删除"按钮
3. 确认删除操作

#### 重置任务

1. 在任务管理页面，点击"重置任务"按钮
2. 确认重置操作

#### 停止运行

1. 在任务管理页面，点击"停止运行"按钮
2. 确认停止操作

### 知识库管理

#### 上传文档

1. 在项目列表中点击要管理的项目
2. 点击"知识库管理"
3. 点击"上传文档"按钮
4. 选择要上传的文件（支持PDF、Word、Markdown、文本文件）
5. 点击"上传"

#### 搜索知识

1. 在知识库管理页面的搜索框中输入关键词
2. 点击"搜索"按钮
3. 系统将显示相关知识文档

#### 删除知识库

1. 在知识库管理页面，点击"删除知识库"按钮
2. 确认删除操作

### 文件管理

#### 浏览文件

1. 在项目列表中点击要管理的项目
2. 点击"文件管理"
3. 系统将显示项目文件结构

#### 预览文件

1. 在文件列表中点击要预览的文件
2. 系统将在右侧显示文件内容

#### 下载文件

1. 在文件列表中找到要下载的文件
2. 点击文件行的"下载"按钮

### 终端管理

#### 使用终端

1. 在项目列表中点击要管理的项目
2. 点击"终端管理"
3. 系统将打开Web终端
4. 可以在终端中执行命令

#### AI命令

在终端中输入`/ai`命令可以直接调用AI功能

### 日志管理

#### 查看日志

1. 在项目列表中点击要管理的项目
2. 点击"LLM日志"
3. 系统将显示任务执行日志

#### 搜索日志

1. 在日志页面的搜索框中输入关键词
2. 点击"搜索"按钮

## 使用流程

### 新项目开发流程

1. **创建项目**
   - 登录系统
   - 创建新项目，填写项目信息

2. **编写需求**
   - 进入项目需求管理页面
   - 编写详细需求文档
   - 使用AI优化需求

3. **生成设计**
   - 在需求管理页面生成设计文档
   - 完善设计内容

4. **生成任务**
   - 根据需求或设计生成开发任务
   - 调整任务依赖关系

5. **执行任务**
   - 运行任务
   - 监控任务执行进度
   - 查看执行日志

6. **查看结果**
   - 在文件管理中查看生成的文件
   - 在知识库中查看相关文档

### BUG修复流程

1. **创建项目**
   - 选择项目类型为"BUG修复"
   - 填写项目信息

2. **描述问题**
   - 在需求管理中详细描述BUG现象和修复要求

3. **生成任务**
   - 根据需求生成修复任务

4. **执行修复**
   - 运行任务完成BUG修复

5. **验证结果**
   - 检查修复结果
   - 查看执行日志

## 常见问题与故障排除

### 登录问题

**问题：无法登录系统**
解决方法：
1. 检查用户名和密码是否正确
2. 确认系统是否正常启动
3. 查看系统日志获取错误信息

### 项目创建问题

**问题：创建项目时提示"工作目录不能为空"**
解决方法：
1. 确保填写了工作目录路径
2. 检查目录权限是否正确

**问题：Git管理项目创建失败**
解决方法：
1. 检查Git地址是否正确
2. 确认访问令牌是否有权限
3. 检查网络连接是否正常

### 任务执行问题

**问题：任务执行失败**
解决方法：
1. 查看任务执行日志
2. 检查LLM服务是否正常
3. 确认项目配置是否正确

### 文件访问问题

**问题：无法访问项目文件**
解决方法：
1. 检查项目工作目录是否存在
2. 确认文件权限设置
3. 查看系统日志获取错误信息

### 知识库问题

**问题：文档上传失败**
解决方法：
1. 检查文件格式是否支持
2. 确认文件大小是否超出限制
3. 检查系统存储空间是否充足

## 技术支持

如遇到无法解决的问题，请：

1. 查看系统日志获取详细错误信息
2. 联系系统管理员
3. 提交Issue到项目仓库

### 系统日志位置

- 任务执行日志：`data/logs/`
- LLM交互日志：`data/logs/{项目名}/`

### 重置系统

如需重置所有数据，可删除`data/`目录：
```bash
rm -rf data/
```

---
**AI任务管理系统** - 让AI任务管理更简单、更高效！