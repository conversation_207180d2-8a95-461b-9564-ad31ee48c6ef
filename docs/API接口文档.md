# AI任务管理系统API接口文档

## 概述

本文档描述了AI任务管理系统的RESTful API接口，开发者可以通过这些接口与系统进行交互。

## 认证方式

系统采用Session认证机制，用户登录后会获得一个Session ID，后续请求需要在Cookie中携带该Session ID。

## 基础URL

```
http://localhost:5005/aicode/api
```

## 状态码

- 200: 请求成功
- 400: 请求参数错误
- 401: 未认证
- 403: 权限不足
- 500: 服务器内部错误

## 认证相关接口

### 用户登录

```
POST /auth/login
```

**请求参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | string | 是 | 用户名 |
| password | string | 是 | 密码 |

**响应示例**

```json
{
  "success": true,
  "message": "登录成功"
}
```

### 用户登出

```
POST /auth/logout
```

**响应示例**

```json
{
  "success": true,
  "message": "已退出登录"
}
```

### 获取当前用户信息

```
GET /auth/current_user
```

**响应示例**

```json
{
  "username": "admin",
  "logged_in": true
}
```

### 修改密码

```
POST /auth/change_password
```

**请求参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| old_password | string | 是 | 原密码 |
| new_password | string | 是 | 新密码 |

**响应示例**

```json
{
  "success": true,
  "message": "密码修改成功"
}
```

## 项目管理接口

### 获取项目列表

```
GET /projects
```

**响应示例**

```json
[
  {
    "project_id": "1632456789012",
    "name": "示例项目",
    "work_dir": "/data/projects/example",
    "description": "项目描述",
    "provider": "local",
    "project_type": "产品迭代",
    "created_at": "2021-09-24T10:00:00",
    "updated_at": "2021-09-24T10:00:00"
  }
]
```

### 创建项目

```
POST /projects
```

**请求参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 是 | 项目名称 |
| work_dir | string | 是 | 工作目录 |
| description | string | 否 | 项目描述 |
| provider | string | 否 | LLM Provider |
| project_type | string | 否 | 项目类型 |
| git_managed | integer | 否 | Git管理 |
| git_url | string | 否 | Git地址 |
| git_access_token | string | 否 | Git访问令牌 |
| git_branch | string | 否 | Git分支 |

**响应示例**

```json
{
  "success": true,
  "project_id": "1632456789012"
}
```

### 获取项目详情

```
GET /projects/{project_id}
```

**响应示例**

```json
{
  "project_id": "1632456789012",
  "name": "示例项目",
  "work_dir": "/data/projects/example",
  "description": "项目描述",
  "requirement": "需求内容",
  "design": "设计内容",
  "provider": "local",
  "rules_constraint": "规则约束",
  "project_type": "产品迭代",
  "git_managed": 0,
  "git_url": "",
  "git_access_token": "",
  "git_branch": "",
  "created_at": "2021-09-24T10:00:00",
  "updated_at": "2021-09-24T10:00:00"
}
```

### 更新项目

```
PUT /projects/{project_id}
```

**请求参数**

与创建项目相同

**响应示例**

```json
{
  "success": true,
  "message": "项目更新成功"
}
```

### 删除项目

```
DELETE /projects/{project_id}
```

**响应示例**

```json
{
  "success": true,
  "message": "项目删除成功"
}
```

## 需求管理接口

### 获取项目需求

```
GET /projects/{project_id}/requirement
```

**响应示例**

```json
{
  "requirement": "需求内容"
}
```

### 更新项目需求

```
PUT /projects/{project_id}/requirement
```

**请求参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| requirement | string | 是 | 需求内容 |

**响应示例**

```json
{
  "success": true,
  "message": "需求更新成功"
}
```

### 优化需求内容

```
POST /projects/{project_id}/optimize_requirement
```

**请求参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| enable_kb | boolean | 否 | 是否启用知识库 |

**响应示例**

```json
{
  "success": true,
  "message": "需求优化成功"
}
```

## 设计管理接口

### 获取项目设计

```
GET /projects/{project_id}/design
```

**响应示例**

```json
{
  "design": "设计内容"
}
```

### 更新项目设计

```
PUT /projects/{project_id}/design
```

**请求参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| design | string | 是 | 设计内容 |

**响应示例**

```json
{
  "success": true,
  "message": "设计更新成功"
}
```

### 生成设计内容

```
POST /projects/{project_id}/gen_design
```

**请求参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| enable_kb | boolean | 否 | 是否启用知识库 |

**响应示例**

```json
{
  "success": true,
  "message": "设计生成成功"
}
```

## 规则管理接口

### 获取项目规则

```
GET /projects/{project_id}/rules
```

**响应示例**

```json
{
  "rules_constraint": "规则约束内容"
}
```

### 更新项目规则

```
PUT /projects/{project_id}/rules
```

**请求参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| rules_constraint | string | 是 | 规则约束内容 |

**响应示例**

```json
{
  "success": true,
  "message": "规则更新成功"
}
```

## 任务管理接口

### 生成任务

```
POST /projects/{project_id}/generate_tasks
```

**请求参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| doc_type | string | 是 | 文档类型(requirement/design) |
| task_count | integer | 否 | 任务数量 |
| special_instruction | string | 否 | 特殊说明 |

**响应示例**

```json
{
  "success": true,
  "message": "任务生成成功"
}
```

### 获取任务列表

```
GET /projects/{project_id}/tasks
```

**响应示例**

```json
[
  {
    "id": 1,
    "title": "任务标题",
    "description": "任务描述",
    "priority": "medium",
    "dependencies": [],
    "status": "pending",
    "details": "任务详情",
    "testStrategy": "测试策略",
    "created_at": "2021-09-24T10:00:00",
    "updated_at": "2021-09-24T10:00:00"
  }
]
```

### 执行任务

```
POST /projects/{project_id}/run_tasks
```

**请求参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| execution_mode | string | 否 | 执行模式(parallel/sequential) |

**响应示例**

```json
{
  "success": true,
  "message": "任务执行已启动"
}
```

### 停止任务执行

```
POST /projects/{project_id}/stop_execution
```

**响应示例**

```json
{
  "success": true,
  "message": "任务执行已停止"
}
```

### 重置任务

```
POST /projects/{project_id}/tasks/reset
```

**响应示例**

```json
{
  "success": true,
  "message": "任务已重置"
}
```

## 单个任务操作接口

### 添加新任务

```
POST /projects/{project_id}/tasks
```

**请求参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| title | string | 是 | 任务标题 |
| description | string | 否 | 任务描述 |
| priority | string | 否 | 优先级 |
| dependencies | array | 否 | 依赖任务ID列表 |
| details | string | 否 | 任务详情 |
| testStrategy | string | 否 | 测试策略 |

**响应示例**

```json
{
  "success": true,
  "task_id": 1,
  "message": "任务添加成功"
}
```

### 更新任务

```
PUT /projects/{project_id}/tasks/{task_id}
```

**请求参数**

与添加任务相同

**响应示例**

```json
{
  "success": true,
  "message": "任务更新成功"
}
```

### 删除任务

```
DELETE /projects/{project_id}/tasks/{task_id}
```

**响应示例**

```json
{
  "success": true,
  "message": "任务删除成功"
}
```

### 运行单个任务

```
POST /projects/{project_id}/tasks/{task_id}/run
```

**响应示例**

```json
{
  "success": true,
  "message": "任务执行已启动"
}
```

### 继续执行任务

```
POST /projects/{project_id}/tasks/{task_id}/continue
```

**响应示例**

```json
{
  "success": true,
  "message": "任务继续执行已启动"
}
```

### 切换任务状态

```
POST /projects/{project_id}/tasks/{task_id}/toggle
```

**响应示例**

```json
{
  "success": true,
  "message": "任务状态已切换"
}
```

## 子任务管理接口

### 获取子任务列表

```
GET /projects/{project_id}/tasks/{task_id}/subtasks
```

**响应示例**

```json
[
  {
    "id": "subtask_1",
    "name": "子任务名称",
    "description": "子任务描述",
    "checklist": ["检查项1", "检查项2"],
    "status": "pending",
    "created_at": "2021-09-24T10:00:00",
    "updated_at": "2021-09-24T10:00:00"
  }
]
```

### 创建子任务

```
POST /projects/{project_id}/tasks/{task_id}/subtasks
```

**请求参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 是 | 子任务名称 |
| description | string | 否 | 子任务描述 |
| checklist | array | 否 | 检查项列表 |

**响应示例**

```json
{
  "success": true,
  "subtask_id": "subtask_1",
  "message": "子任务创建成功"
}
```

### 获取子任务详情

```
GET /projects/{project_id}/tasks/{task_id}/subtasks/{subtask_id}
```

**响应示例**

```json
{
  "id": "subtask_1",
  "name": "子任务名称",
  "description": "子任务描述",
  "checklist": ["检查项1", "检查项2"],
  "status": "pending",
  "created_at": "2021-09-24T10:00:00",
  "updated_at": "2021-09-24T10:00:00"
}
```

### 更新子任务

```
PUT /projects/{project_id}/tasks/{task_id}/subtasks/{subtask_id}
```

**请求参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 否 | 子任务名称 |
| description | string | 否 | 子任务描述 |
| checklist | array | 否 | 检查项列表 |
| status | string | 否 | 状态 |

**响应示例**

```json
{
  "success": true,
  "message": "子任务更新成功"
}
```

### 删除子任务

```
DELETE /projects/{project_id}/tasks/{task_id}/subtasks/{subtask_id}
```

**响应示例**

```json
{
  "success": true,
  "message": "子任务删除成功"
}
```

### 智能分解任务

```
POST /projects/{project_id}/tasks/{task_id}/subtasks/smart_split
```

**响应示例**

```json
{
  "success": true,
  "message": "任务智能分解成功"
}
```

### 删除所有子任务

```
DELETE /projects/{project_id}/tasks/{task_id}/subtasks/delete_all
```

**响应示例**

```json
{
  "success": true,
  "message": "所有子任务已删除"
}
```

## 文件管理接口

### 获取文件列表

```
GET /projects/{project_id}/files
```

**响应示例**

```json
[
  {
    "path": "src/main.py",
    "name": "main.py",
    "is_directory": false,
    "size": 1024,
    "modified_time": "2021-09-24T10:00:00",
    "extension": ".py",
    "mime_type": "text/x-python",
    "is_ai_generated": false,
    "type": "code"
  }
]
```

### 获取文件内容

```
GET /projects/{project_id}/files/content
```

**请求参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| path | string | 是 | 文件路径 |

**响应示例**

```json
{
  "success": true,
  "content": "文件内容",
  "file_info": {
    "path": "src/main.py",
    "name": "main.py",
    "is_directory": false,
    "size": 1024,
    "modified_time": "2021-09-24T10:00:00",
    "extension": ".py",
    "mime_type": "text/x-python",
    "is_ai_generated": false,
    "type": "code"
  }
}
```

### 获取文件统计信息

```
GET /projects/{project_id}/files/stats
```

**响应示例**

```json
{
  "total_files": 42,
  "total_size": 102400,
  "file_types": {
    "code": 25,
    "document": 10,
    "image": 5,
    "other": 2
  },
  "ai_generated_files": 15
}
```

## 知识库管理接口

### 获取项目知识库

```
GET /projects/{project_id}/knowledge_bases
```

**响应示例**

```json
[
  {
    "id": "kb_1",
    "name": "项目文档知识库",
    "description": "项目相关文档",
    "type": "documents",
    "created_at": "2021-09-24T10:00:00",
    "updated_at": "2021-09-24T10:00:00",
    "chunk_count": 100
  }
]
```

### 获取知识库详情

```
GET /knowledge_bases/{kb_id}
```

**响应示例**

```json
{
  "id": "kb_1",
  "name": "项目文档知识库",
  "description": "项目相关文档",
  "type": "documents",
  "created_at": "2021-09-24T10:00:00",
  "updated_at": "2021-09-24T10:00:00",
  "chunk_count": 100
}
```

### 删除知识库

```
DELETE /knowledge_bases/{kb_id}
```

**响应示例**

```json
{
  "success": true,
  "message": "知识库删除成功"
}
```

### 获取知识库文档列表

```
GET /knowledge_bases/{kb_id}/documents
```

**响应示例**

```json
[
  {
    "id": "doc_1",
    "filename": "需求文档.md",
    "content": "文档内容",
    "created_at": "2021-09-24T10:00:00",
    "updated_at": "2021-09-24T10:00:00"
  }
]
```

### 添加文档到知识库

```
POST /knowledge_bases/{kb_id}/documents
```

**请求参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| file | file | 是 | 上传的文件 |

**响应示例**

```json
{
  "success": true,
  "message": "文档添加成功"
}
```

### 清空知识库文档

```
DELETE /knowledge_bases/{kb_id}/documents
```

**响应示例**

```json
{
  "success": true,
  "message": "知识库文档已清空"
}
```

## 终端管理接口

### 打开项目终端

```
GET /aiterm/xterm.html?project_id={project_id}
```

## 配置管理接口

### 获取LLM Providers配置

```
GET /config/providers
```

**请求参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| api_type | string | 否 | API类型 |

**响应示例**

```json
[
  {
    "key": "local",
    "name": "内网",
    "api_type": "claude"
  }
]
```

### 获取数据目录配置

```
GET /config/data-dir
```

**响应示例**

```json
{
  "data_dir": "/opt/data"
}
```

### 获取所有配置

```
GET /config/all
```

**响应示例**

```json
{
  "data_dir": "/opt/data",
  "providers": [
    {
      "key": "local",
      "name": "内网",
      "api_type": "claude"
    }
  ]
}
```

---
**AI任务管理系统** - 让AI任务管理更简单、更高效！