# Shell AI 命令助手

## 功能概述

在终端中使用 `/ai` 命令，让 AI 根据你的需求自动生成 Shell 命令，无需记忆复杂的命令语法。

## 使用方法

### 基本语法

```bash
/ai <你想执行的操作描述>
```

### 使用示例

#### 示例 1: 查询系统信息

```bash
[proj...] work $ /ai 查询主板型号
cat /sys/devices/virtual/dmi/id/board_name
```

AI 会生成命令但不执行，你可以：
- 按回车执行命令
- 修改命令后执行
- 或者忽略该命令

#### 示例 2: 文件操作

```bash
[proj...] work $ /ai 查找当前目录下所有的 Python 文件
find . -name "*.py"

[proj...] work $ /ai 统计当前目录下所有代码文件的行数
find . -name "*.py" -o -name "*.js" | xargs wc -l

[proj...] work $ /ai 删除所有 .pyc 文件
find . -name "*.pyc" -delete
```

#### 示例 3: 进程管理

```bash
[proj...] work $ /ai 查看占用 5005 端口的进程
lsof -i :5005

[proj...] work $ /ai 杀死占用 8080 端口的进程
kill -9 $(lsof -t -i:8080)
```

#### 示例 4: 网络操作

```bash
[proj...] work $ /ai 测试能否访问百度
ping -c 4 baidu.com

[proj...] work $ /ai 下载一个文件
curl -O <URL>

[proj...] work $ /ai 查看本机 IP 地址
hostname -I | awk '{print $1}'
```

#### 示例 5: 日志查询

```bash
[proj...] work $ /ai 查找包含 error 的日志
grep -r "error" /var/log/

[proj...] work $ /ai 查看最近 100 行系统日志
tail -n 100 /var/log/syslog
```

#### 示例 6: 压缩解压

```bash
[proj...] work $ /ai 压缩当前目录为 tar.gz
tar -czf archive.tar.gz .

[proj...] work $ /ai 解压 zip 文件
unzip file.zip
```

## 工作原理

### 1. 命令截获

当你在终端输入以 `/ai ` 开头的命令时，系统会：

```
用户输入: /ai 查询主板型号
    ↓
web_app.py 截获输入
    ↓
提取查询内容: "查询主板型号"
    ↓
调用 shell_manager
```

### 2. AI 处理

ShellManager 会：

1. **获取系统信息**：
   - 操作系统类型（Linux/Windows/macOS）
   - 发行版信息（Ubuntu 22.04 LTS 等）
   - 架构信息（x86_64/arm64 等）

2. **构建提示词**：
   ```
   你是一个 Shell 命令专家。
   当前操作系统: Ubuntu 22.04.5 LTS (x86_64)

   只返回可以直接执行的命令，不要返回任何解释或说明。
   ```

3. **调用 LLM**：
   - 使用项目配置的 provider
   - 不使用深度思考模式（快速响应）
   - 不查询知识库

4. **清理输出**：
   - 移除 markdown 代码块标记
   - 移除 bash/sh 前缀
   - 只保留纯命令

### 3. 返回结果

```
AI 生成命令: cat /sys/devices/virtual/dmi/id/board_name
    ↓
web_app.py 写入终端
    ↓
浏览器显示命令（不执行）
    ↓
用户按回车执行（或修改后执行）
```

## 架构设计

### 文件结构

```
src/
├── shell_manager.py          # Shell 命令管理器
│   ├── ShellManager         # 主类
│   │   ├── get_system_info()       # 获取系统信息
│   │   ├── ask_shell_command()     # 异步生成命令
│   │   └── ask_shell_command_sync() # 同步生成命令
│   └── get_shell_manager()  # 单例获取函数
│
└── web_app.py               # Web 应用
    └── pty_input()          # 终端输入处理
        └── 检测 /ai 命令并调用 shell_manager
```

### 类图

```
┌──────────────────────┐
│   ShellManager       │
├──────────────────────┤
│ - provider           │
├──────────────────────┤
│ + get_system_info()  │
│ + ask_shell_command()│
└──────────────────────┘
         ↑
         │ 使用
         │
┌──────────────────────┐
│    LLMAgent          │
├──────────────────────┤
│ + run_agent()        │
└──────────────────────┘
```

### 数据流

```
终端输入: /ai 查询主板型号
    ↓
┌─────────────────────────────────┐
│  pty_input() @ web_app.py       │
│  - 检测 /ai 前缀                 │
│  - 提取查询: "查询主板型号"      │
└─────────────────────────────────┘
    ↓
┌─────────────────────────────────┐
│  ShellManager                   │
│  - 获取系统信息                  │
│  - 构建 prompt                   │
│  - 调用 LLMAgent                 │
└─────────────────────────────────┘
    ↓
┌─────────────────────────────────┐
│  LLMAgent                       │
│  - 发送请求到 LLM                │
│  - 获取响应                      │
└─────────────────────────────────┘
    ↓
┌─────────────────────────────────┐
│  ShellManager                   │
│  - 清理输出                      │
│  - 返回纯命令                    │
└─────────────────────────────────┘
    ↓
┌─────────────────────────────────┐
│  pty_input()                    │
│  - 写入终端显示                  │
│  - 不执行，等待用户确认          │
└─────────────────────────────────┘
```

## 特性

### 1. 智能系统适配

根据当前操作系统生成适配的命令：

- **Linux**: 使用 `apt`, `systemctl` 等
- **macOS**: 使用 `brew`, `launchctl` 等
- **Windows**: 使用 `powershell` 命令

### 2. 安全性

- 不自动执行命令，只显示给用户
- 不默认使用 `sudo`（除非用户明确要求）
- 用户可以在执行前查看和修改命令

### 3. 快速响应

- 使用 LLMAgent（快速模式）
- 不使用深度思考
- 不查询知识库
- 平均响应时间：2-5 秒

### 4. 多项目支持

- 每个项目使用自己配置的 provider
- 环境变量自动适配
- 命令生成考虑项目工作目录

## 配置

### Provider 配置

在项目设置中指定 provider，影响 AI 命令生成质量：

```python
# 推荐使用较快的模型
PROVIDERS = {
    "fast": {
        "model": "claude-3-haiku",  # 快速响应
        "max_tokens": "2000"        # 命令通常很短
    }
}
```

### 系统信息

ShellManager 自动检测：

```python
# Linux
"Ubuntu 22.04.5 LTS (x86_64)"

# macOS
"Darwin 23.1.0 (arm64)"

# Windows
"Windows 11 (AMD64)"
```

## 限制和注意事项

### 1. 命令准确性

- AI 生成的命令可能不完全准确
- 建议在执行前仔细检查
- 对于系统级操作，建议手动确认

### 2. 复杂操作

对于复杂的多步骤操作，AI 可能：
- 生成用 `&&` 或 `;` 连接的多个命令
- 或建议使用脚本

### 3. 网络依赖

- 需要连接到配置的 LLM 服务
- 网络问题会导致命令生成失败

### 4. 提示词质量

命令质量取决于你的描述：

**好的描述**：
```bash
/ai 查找当前目录下所有大于 100MB 的文件
/ai 统计 Python 文件的总行数
```

**不好的描述**：
```bash
/ai 文件  # 太模糊
/ai 怎么办  # 没有明确需求
```

## 故障排查

### 命令生成失败

**现象**：显示 "# AI 生成失败: xxx"

**原因**：
1. LLM 服务不可用
2. Provider 配置错误
3. 网络问题

**解决**：
```bash
# 检查 provider 配置
curl http://localhost:5005/api/config/providers

# 查看服务器日志
# 应该看到: "Shell AI: 处理请求 - xxx"
```

### 命令不适配系统

**现象**：生成的命令在当前系统无法执行

**原因**：
1. 系统信息检测失败
2. AI 模型知识不足

**解决**：
- 手动修改命令
- 或使用更具体的描述

### 响应慢

**现象**：等待时间超过 10 秒

**原因**：
1. 使用了较慢的模型
2. LLM 服务负载高

**解决**：
- 切换到更快的 provider
- 简化查询描述

## API 参考

### ShellManager

```python
from shell_manager import ShellManager

manager = ShellManager(provider="local")

# 获取系统信息
system_info = manager.get_system_info()
# 返回: "Ubuntu 22.04.5 LTS (x86_64)"

# 生成命令（同步）
result = manager.ask_shell_command_sync("查询主板型号")
# 返回: {
#   "success": True,
#   "command": "cat /sys/devices/virtual/dmi/id/board_name",
#   "message": "命令生成成功"
# }

# 生成命令（异步）
import asyncio
result = asyncio.run(manager.ask_shell_command("查询主板型号"))
```

### 单例模式

```python
from shell_manager import get_shell_manager

# 获取全局单例
manager = get_shell_manager(provider="local")
```

## 扩展开发

### 自定义提示词

修改 `shell_manager.py` 中的 prompt：

```python
prompt = f"""你是一个 Shell 命令专家。
当前操作系统: {system_info}

# 添加自定义约束
- 优先使用简洁的命令
- 避免使用管道
- ...
"""
```

### 添加命令验证

```python
def validate_command(command: str) -> bool:
    """验证命令安全性"""
    dangerous_commands = ['rm -rf /', 'dd if=', 'mkfs']
    return not any(cmd in command for cmd in dangerous_commands)
```

### 集成到其他工具

```python
# 在你的脚本中使用
from shell_manager import get_shell_manager

manager = get_shell_manager()
result = manager.ask_shell_command_sync("你的需求")

if result['success']:
    print(f"生成的命令: {result['command']}")
```

## 最佳实践

1. **清晰描述需求**：说明具体要做什么，而不是怎么做
2. **检查后执行**：生成的命令不自动执行，务必检查
3. **渐进式使用**：先从简单命令开始，熟悉后处理复杂任务
4. **保存常用命令**：对于经常使用的命令，建议保存到脚本中
5. **学习命令**：通过 AI 生成学习 Shell 命令的用法

## 示例集合

参考 [Shell 命令示例](shell_command_examples.md) 了解更多用例。
