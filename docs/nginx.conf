    location /aicode/ {
        proxy_pass http://host.docker.internal:5005;
        # 修复重定向问题，确保重定向URL包含/aicode前缀
        proxy_redirect ~^http://host.docker.internal:5005/(.*)$ $scheme://$host:$server_port/aicode/$1;

        proxy_set_header Host $host:$server_port;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_connect_timeout 1800s;
        proxy_send_timeout 1800s;
        proxy_read_timeout 1800s;
    }
    location /aiterm/ {
        proxy_pass http://host.docker.internal:5006;
        # 修复重定向问题，确保重定向URL包含/aicode前缀
        proxy_redirect ~^http://host.docker.internal:5006/(.*)$ $scheme://$host:$server_port/aicode/$1;

        proxy_set_header Host $host:$server_port;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_connect_timeout 1800s;
        proxy_send_timeout 1800s;
        proxy_read_timeout 1800s;
    }
    location /socket.io/ {
        proxy_pass http://host.docker.internal:5006;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host:$server_port;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }