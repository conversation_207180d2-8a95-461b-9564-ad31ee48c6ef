{"文档信息": {"文档名称": "V10签名验签服务器接口设计V0.6", "文档格式": "Word文档", "解析时间": "2025-09-07 15:47:26", "文档路径": "docs/V10签名验签服务器接口设计V0.6.docx", "表格数量": 256}, "统计信息": {"总接口数": 231, "分类统计": {"SDK初始化": 16, "证书管理": 91, "密钥管理": 60, "密码运算": 14, "消息签名验签": 32, "条形码": 5, "监控": 3, "工具类": 4, "其他": 6}, "算法统计": {"SHA512": 84, "SHA384": 84, "SHA1": 84, "SM3": 84, "SHA256": 84, "SHA224": 82, "SM2": 77, "SHA256WithECDSA": 71, "SHA384WithRSA": 71, "SHA512WithRSA": 71, "SHA1WithRSA": 71, "SM3WithSM2": 71, "SHA256WithRSA": 71, "SHA224WithECDSA": 69, "SHA1WithECDSA": 69, "SHA224WithRSA": 69, "NoneWithECDSA": 69, "SHA512WithECDSA": 69, "SHA384WithECDSA": 69, "ED448": 69, "ED25519": 69, "NoneWithSM2": 50, "NoneWithRSA": 50, "SM4": 22, "SM1": 22, "3DES": 22, "SM7": 22, "DES": 22, "SHA1WithRSA/PSS": 22, "SHA512WithRSA/PSS": 22, "SHA256WithRSA/PSS": 22, "SHA224WithRSA/PSS": 22, "SHA384WithRSA/PSS": 22, "MD5": 10, "SM9": 1}, "参数统计": {"总参数数": 656, "有参数的接口数": 210, "平均参数数": 2.84}}, "分类接口": {"SDK初始化": [{"章节": "SDK初始化", "需求编号": "PR-F-SVS-0001", "需求名称": "", "优先级": "", "接口功能": "通过传入配置文件或配置字符串方式初始化签名验签服务器客户端实例", "接口定义": "public static synchronized SVSClient getInstance(String config) throws SVSException public static synchronized SVSClient getInstance(String config,String clientPfxPwd) throws SVSException", "输入参数": [{"参数名": "config", "类型": "String", "必填": "是", "说明": "配置文件绝对路径或配置内容字符串"}], "输出": "调用接口的实例对象", "异常": "SVSException", "其它说明": "单实例方式初始化，避免重复初始化； 2、配置格式说明： ①注释行以 符号“#” 起始，不支持行内注释 ②配置域以方括号“[”和“]”标识 ③配置项格式为 “键名（Key） = 键值（Value）” ④配置域与键名不区分大小写，但为了直观建议配置域名使用大写 ⑤支持使用空白字符（空格或制表符）等对内容进行对齐操作 ⑥可在接口内拼装字符串传递配置，使用“{”和“}”包括所有内容，使用“;”表示换行", "支持算法": []}, {"章节": "SDK初始化", "需求编号": "PR-F-SVS-0001", "需求名称": "", "优先级": "", "接口功能": "通过传入配置对象信息方式初始化签名验签服务器客户端实例", "接口定义": "public static synchronized SVSClient getInstance(List<HsmInfo> hsmInfoList,PoolInfo poolInfo) throws SVSException", "输入参数": [{"参数名": "hsmInfoList", "类型": "List<HsmInfo>", "必填": "是", "说明": "加密机设备信息"}, {"参数名": "poolInfo", "类型": "PoolInfo", "必填": "是", "说明": "公共配置信息，包括日志/TLS配置/全局配置"}], "输出": "调用接口的实例对象", "异常": "SVSException", "其它说明": "1、单实例方式初始化，避免重复初始化； 2、HsmInfo对象属性信息，对应加密机域，多个设备通过集合方式传入初始化接口 3、PoolInfo对象属性信息 ，包括日志/TLS配置/全局配置", "支持算法": []}, {"章节": "SDK初始化", "需求编号": "PR-F-SVS-0055", "需求名称": "", "优先级": "", "接口功能": "多步哈希运算初始化操作，支持的算法包括MD5、SHA1、SHA256、SHA224、SHA384、SHA512、SM3.其中SM3支持带Id的运算", "接口定义": "public byte[] hashInit( String algorithm， byte[] sm2PublicKey, byte[] sm2UserId ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "哈希算法类型，取值范围： MD5、SHA1、SHA256、SHA224、SHA384、SHA512、SM3."}, {"参数名": "sm2PublicKey", "类型": "byte[]", "必填": "否", "说明": "SM2公钥，哈希算法为SM3时生效。支持X.509格式、GMT0018格式的公钥。程序自适应"}, {"参数名": "sm2UserId", "类型": "byte[]", "必填": "否", "说明": "SM2公钥Id，哈希算法为SM3且sm2PublicKey不为NULL时生效。 长度限制1-128"}], "输出": "哈希句柄，里面存放了中间过程数据", "异常": "SVSException", "其它说明": "sm2PublicKey不为空且algorithm是SM3时，按照计算Z值的SM3哈希算法计算摘要，sm2UserId为空，则使用默认id “1234567812345678” 计算Z值。 sm2PublicKey为空且algorithm是SM3时，不管sm2UserId是否为空，都按照不计算Z值计算SM3摘要。", "支持算法": ["MD5", "SHA512", "SHA224", "SHA1", "SM3", "SHA256", "SHA384"]}, {"章节": "SDK初始化", "需求编号": "PR-F-SVS-0062", "需求名称": "", "优先级": "", "接口功能": "使用指定索引的内部对称密钥多步MAC运算初始化操作，支持计算多种MAC，包括HMAC、CMAC、CBCMAC", "接口定义": "public byte[] macInitByIndex( String algorithm， int keyIndex byte[] iv ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，取值范围： HMAC： HMAC-MD5、HMAC-SHA1、HMAC-SHA224、HMAC-SHA256、HMAC-SHA384、HMAC-SHA512、HMAC-SM3 CBCMAC： DES-CBCMAC、3DES-CBCMAC、 AES-CBCMAC、SM1-CBCMAC、 SM4-CBCMAC、SM7-CBCMAC CMAC： DES-CMAC、3DES-CMAC、 AES-CMAC、SM1-CMAC、 SM4-CMAC、SM7-CMAC"}, {"参数名": "keyIndex", "类型": "int", "必填": "是", "说明": ""}, {"参数名": "iv", "类型": "byte[]", "必填": "否", "说明": ""}], "输出": "MAC句柄，里面存放了中间过程数据", "异常": "SVSException", "其它说明": "", "支持算法": ["SM4", "MD5", "SHA512", "SM1", "SHA224", "3DES", "SHA1", "SM7", "SM3", "DES", "SHA256", "SHA384"]}, {"章节": "SDK初始化", "需求编号": "PR-F-SVS-0063", "需求名称": "", "优先级": "", "接口功能": "使用指定名称的内部对称密钥多步MAC运算初始化操作，支持计算多种MAC，包括HMAC、CMAC、CBCMAC", "接口定义": "public byte[] macInitByName( String algorithm， String name, byte[] iv ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，取值范围： HMAC： HMAC-MD5、HMAC-SHA1、HMAC-SHA224、HMAC-SHA256、HMAC-SHA384、HMAC-SHA512、HMAC-SM3 CBCMAC： DES-CBCMAC、3DES-CBCMAC、 AES-CBCMAC、SM1-CBCMAC、 SM4-CBCMAC、SM7-CBCMAC CMAC： DES-CMAC、3DES-CMAC、 AES-CMAC、SM1-CMAC、 SM4-CMAC、SM7-CMAC"}, {"参数名": "name", "类型": "String", "必填": "是", "说明": ""}, {"参数名": "iv", "类型": "byte[]", "必填": "否", "说明": ""}], "输出": "MAC句柄，里面存放了中间过程数据", "异常": "SVSException", "其它说明": "", "支持算法": ["SM4", "MD5", "SHA512", "SM1", "SHA224", "3DES", "SHA1", "SM7", "SM3", "DES", "SHA256", "SHA384"]}, {"章节": "SDK初始化", "需求编号": "PR-F-SVS-0064", "需求名称": "", "优先级": "", "接口功能": "使用外部明文对称密钥多步MAC运算初始化操作，支持计算多种MAC，包括HMAC、CMAC、CBCMAC", "接口定义": "public byte[] macInitByKey( String algorithm， byte[] key, byte[] iv ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，取值范围： HMAC： HMAC-MD5、HMAC-SHA1、HMAC-SHA224、HMAC-SHA256、HMAC-SHA384、HMAC-SHA512、HMAC-SM3 CBCMAC： DES-CBCMAC、3DES-CBCMAC、 AES-CBCMAC、SM1-CBCMAC、 SM4-CBCMAC、SM7-CBCMAC CMAC： DES-CMAC、3DES-CMAC、 AES-CMAC、SM1-CMAC、 SM4-CMAC、SM7-CMAC"}, {"参数名": "key", "类型": "byte[]", "必填": "是", "说明": "对称密钥"}, {"参数名": "iv", "类型": "byte[]", "必填": "否", "说明": ""}], "输出": "MAC句柄，里面存放了中间过程数据", "异常": "SVSException", "其它说明": "", "支持算法": ["SM4", "MD5", "SHA512", "SM1", "SHA224", "3DES", "SHA1", "SM7", "SM3", "DES", "SHA256", "SHA384"]}, {"章节": "SDK初始化", "需求编号": "PR-F-SVS-0065", "需求名称": "", "优先级": "", "接口功能": "使用LMK加密的外部对称密钥多步MAC运算初始化操作，支持计算多种MAC，包括HMAC、CMAC、CBCMAC", "接口定义": "public byte[] macInitByEncryptedKey( String algorithm， byte[] key, byte[] iv ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，取值范围： HMAC： HMAC-MD5、HMAC-SHA1、HMAC-SHA224、HMAC-SHA256、HMAC-SHA384、HMAC-SHA512、HMAC-SM3 CBCMAC： DES-CBCMAC、3DES-CBCMAC、 AES-CBCMAC、SM1-CBCMAC、 SM4-CBCMAC、SM7-CBCMAC CMAC： DES-CMAC、3DES-CMAC、 AES-CMAC、SM1-CMAC、 SM4-CMAC、SM7-CMAC"}, {"参数名": "key", "类型": "byte[]", "必填": "是", "说明": "LMK加密的对称密钥"}, {"参数名": "iv", "类型": "byte[]", "必填": "否", "说明": ""}], "输出": "MAC句柄，里面存放了中间过程数据", "异常": "SVSException", "其它说明": "", "支持算法": ["SM4", "MD5", "SHA512", "SM1", "SHA224", "3DES", "SHA1", "SM7", "SM3", "DES", "SHA256", "SHA384"]}, {"章节": "SDK初始化", "需求编号": "PR-F-SVS-0072", "需求名称": "", "优先级": "", "接口功能": "使用指定索引的对称密钥实现多步对称加密运算初始化， 支持算法：DES/3DES/AES/SM1/SM4/SM7 支持模式：ECB/CBC/CFB/OFB/CTR/GCM/CCM/XTS/FPE 支持补丁：NoPadding/PKCS5Padding/PKCS7Padding/", "接口定义": "public byte[] encryptInitByIndex( String algorithm， int keyIndex, byte[] aad, byte[] iv ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：算法/模式/补丁"}, {"参数名": "keyIndex", "类型": "int", "必填": "是", "说明": "对称密钥索引"}, {"参数名": "aad", "类型": "byte[]", "必填": "否", "说明": "GCM、CCM模式时使用的额外参数。长度受算法模式控制"}, {"参数名": "iv", "类型": "byte[]", "必填": "否", "说明": "非ECB时生效，长度一般是算法块长"}], "输出": "加密句柄", "异常": "SVSException", "其它说明": "注意： 不是所有的算法都支持全部模式， 除ECB、CBC模式外，其他模式只需要NoPadding", "支持算法": []}, {"章节": "SDK初始化", "需求编号": "PR-F-SVS-0073", "需求名称": "", "优先级": "", "接口功能": "使用指定名称的对称密钥实现多步对称加密运算初始化， 支持算法：DES/3DES/AES/SM1/SM4/SM7 支持模式：ECB/CBC/CFB/OFB/CTR/GCM/CCM/XTS/FPE 支持补丁：NoPadding/PKCS5Padding/PKCS7Padding/", "接口定义": "public byte[] encryptInitByName( String algorithm， String name, byte[] aad, byte[] iv ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：算法/模式/补丁"}, {"参数名": "keyIndex", "类型": "int", "必填": "是", "说明": "对称密钥名称"}, {"参数名": "aad", "类型": "byte[]", "必填": "否", "说明": "GCM、CCM模式时使用的额外参数。长度受算法模式控制"}, {"参数名": "iv", "类型": "byte[]", "必填": "否", "说明": "非ECB时生效，长度一般是算法块长"}], "输出": "加密句柄", "异常": "SVSException", "其它说明": "注意： 不是所有的算法都支持全部模式， 除ECB、CBC模式外，其他模式只需要NoPadding", "支持算法": []}, {"章节": "SDK初始化", "需求编号": "PR-F-SVS-0074", "需求名称": "", "优先级": "", "接口功能": "使用外部明文对称密钥实现多步对称加密运算初始化， 支持算法：DES/3DES/AES/SM1/SM4/SM7 支持模式：ECB/CBC/CFB/OFB/CTR/GCM/CCM/XTS/FPE 支持补丁：NoPadding/PKCS5Padding/PKCS7Padding/", "接口定义": "public byte[] encryptInitByKey( String algorithm， byte[] key, byte[] aad, byte[] iv ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：算法/模式/补丁"}, {"参数名": "key", "类型": "byte[]", "必填": "是", "说明": "对称密钥"}, {"参数名": "aad", "类型": "byte[]", "必填": "否", "说明": "GCM、CCM模式时使用的额外参数。长度受算法模式控制"}, {"参数名": "iv", "类型": "byte[]", "必填": "否", "说明": "非ECB时生效，长度一般是算法块长"}], "输出": "加密句柄", "异常": "SVSException", "其它说明": "注意： 不是所有的算法都支持全部模式， 除ECB、CBC模式外，其他模式只需要NoPadding", "支持算法": []}, {"章节": "SDK初始化", "需求编号": "PR-F-SVS-0075", "需求名称": "", "优先级": "", "接口功能": "使用外部密文对称密钥实现多步对称加密运算初始化， 支持算法：DES/3DES/AES/SM1/SM4/SM7 支持模式：ECB/CBC/CFB/OFB/CTR/GCM/CCM/XTS/FPE 支持补丁：NoPadding/PKCS5Padding/PKCS7Padding/", "接口定义": "public byte[] encryptInitByEncryptedKey( String algorithm， byte[] key, byte[] aad, byte[] iv ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：算法/模式/补丁"}, {"参数名": "key", "类型": "byte[]", "必填": "是", "说明": "LMK加密的对称密钥"}, {"参数名": "aad", "类型": "byte[]", "必填": "否", "说明": "GCM、CCM模式时使用的额外参数。长度受算法模式控制"}, {"参数名": "iv", "类型": "byte[]", "必填": "否", "说明": "非ECB时生效，长度一般是算法块长"}], "输出": "加密句柄", "异常": "SVSException", "其它说明": "注意： 不是所有的算法都支持全部模式， 除ECB、CBC模式外，其他模式只需要NoPadding", "支持算法": []}, {"章节": "SDK初始化", "需求编号": "PR-F-SVS-0082", "需求名称": "", "优先级": "", "接口功能": "使用指定索引的对称密钥实现多步对称解密运算初始化， 支持算法：DES/3DES/AES/SM1/SM4/SM7 支持模式：ECB/CBC/CFB/OFB/CTR/GCM/CCM/XTS/FPE 支持补丁：NoPadding/PKCS5Padding/PKCS7Padding/", "接口定义": "public byte[] decryptInitByIndex( String algorithm， int keyIndex, byte[] aad, byte[] iv ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：算法/模式/补丁"}, {"参数名": "keyIndex", "类型": "int", "必填": "是", "说明": "对称密钥索引"}, {"参数名": "aad", "类型": "byte[]", "必填": "否", "说明": "GCM、CCM模式时使用的额外参数。长度受算法模式控制"}, {"参数名": "iv", "类型": "byte[]", "必填": "否", "说明": "非ECB时生效，长度一般是算法块长"}], "输出": "解密句柄", "异常": "SVSException", "其它说明": "注意： 不是所有的算法都支持全部模式， 除ECB、CBC模式外，其他模式只需要NoPadding", "支持算法": []}, {"章节": "SDK初始化", "需求编号": "PR-F-SVS-0083", "需求名称": "", "优先级": "", "接口功能": "使用指定名称的对称密钥实现多步对称解密运算初始化， 支持算法：DES/3DES/AES/SM1/SM4/SM7 支持模式：ECB/CBC/CFB/OFB/CTR/GCM/CCM/XTS/FPE 支持补丁：NoPadding/PKCS5Padding/PKCS7Padding/", "接口定义": "public byte[] decryptInitByName( String algorithm， String name, byte[] aad, byte[] iv ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：算法/模式/补丁"}, {"参数名": "keyIndex", "类型": "int", "必填": "是", "说明": "对称密钥名称"}, {"参数名": "aad", "类型": "byte[]", "必填": "否", "说明": "GCM、CCM模式时使用的额外参数。长度受算法模式控制"}, {"参数名": "iv", "类型": "byte[]", "必填": "否", "说明": "非ECB时生效，长度一般是算法块长"}], "输出": "解密句柄", "异常": "SVSException", "其它说明": "注意： 不是所有的算法都支持全部模式， 除ECB、CBC模式外，其他模式只需要NoPadding", "支持算法": []}, {"章节": "SDK初始化", "需求编号": "PR-F-SVS-0084", "需求名称": "", "优先级": "", "接口功能": "使用外部明文对称密钥实现多步对称解密运算初始化， 支持算法：DES/3DES/AES/SM1/SM4/SM7 支持模式：ECB/CBC/CFB/OFB/CTR/GCM/CCM/XTS/FPE 支持补丁：NoPadding/PKCS5Padding/PKCS7Padding/", "接口定义": "public byte[] decryptInitByKey( String algorithm， byte[] key, byte[] aad, byte[] iv ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：算法/模式/补丁"}, {"参数名": "key", "类型": "byte[]", "必填": "是", "说明": "对称密钥"}, {"参数名": "aad", "类型": "byte[]", "必填": "否", "说明": "GCM、CCM模式时使用的额外参数。长度受算法模式控制"}, {"参数名": "iv", "类型": "byte[]", "必填": "否", "说明": "非ECB时生效，长度一般是算法块长"}], "输出": "解密句柄", "异常": "SVSException", "其它说明": "注意： 不是所有的算法都支持全部模式， 除ECB、CBC模式外，其他模式只需要NoPadding", "支持算法": []}, {"章节": "SDK初始化", "需求编号": "PR-F-SVS-0085", "需求名称": "", "优先级": "", "接口功能": "使用外部密文对称密钥实现多步对称解密运算初始化， 支持算法：DES/3DES/AES/SM1/SM4/SM7 支持模式：ECB/CBC/CFB/OFB/CTR/GCM/CCM/XTS/FPE 支持补丁：NoPadding/PKCS5Padding/PKCS7Padding/", "接口定义": "public byte[] decryptInitByEncryptedKey( String algorithm， byte[] key, byte[] aad, byte[] iv ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：算法/模式/补丁"}, {"参数名": "key", "类型": "byte[]", "必填": "是", "说明": "LMK加密的对称密钥"}, {"参数名": "aad", "类型": "byte[]", "必填": "否", "说明": "GCM、CCM模式时使用的额外参数。长度受算法模式控制"}, {"参数名": "iv", "类型": "byte[]", "必填": "否", "说明": "非ECB时生效，长度一般是算法块长"}], "输出": "解密句柄", "异常": "SVSException", "其它说明": "注意： 不是所有的算法都支持全部模式， 除ECB、CBC模式外，其他模式只需要NoPadding", "支持算法": []}, {"章节": "SDK初始化", "需求编号": "PR-F-SVS-0234", "需求名称": "", "优先级": "", "接口功能": "设备信息", "接口定义": "public class DeviceInfo { private String version; //软件版本号 private String serialNumber; //设备序列号 private String productVersion; //硬件型号 private String deviceInitMode; //设备状态：设备未初始化(test)，设备已初始化(normal) private String systemTime;//系统时间，格式：2020-12-04 17:01:28 private String runtime;//运行时间，格式：32-01:02:51，表示运行了32天1小时2分钟51秒 //.每个属性自带get/set属性 }", "输入参数": [], "输出": "", "异常": "", "其它说明": "", "支持算法": []}], "证书管理": [{"章节": "证书管理", "需求编号": "PR-F-SVS-0007", "需求名称": "", "优先级": "", "接口功能": "返回所有信任域证书", "接口定义": "public List<TrustUserCertInfo> queryTrustCertList() throws SVSException", "输入参数": [], "输出": "无", "异常": "SVSException", "其它说明": "返回所有信任域证书列表，一般该列表不大", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0008", "需求名称": "", "优先级": "", "接口功能": "根据名称查询信任域证书", "接口定义": "public TrustUserCertInfo queryTrustCertByName(String name) throws SVSException", "输入参数": [{"参数名": "name", "类型": "String", "必填": "是", "说明": "证书名称"}], "输出": "TrustUserCertInfo（1.7.1章节）：可能为空，表示没有指定的证书", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0009", "需求名称": "", "优先级": "", "接口功能": "根据证书序列号查询信任域证书", "接口定义": "public TrustUserCertInfo queryTrustCertBySN(String serialNumber) throws SVSException", "输入参数": [{"参数名": "serialNumber", "类型": "String", "必填": "是", "说明": "证书序列号"}], "输出": "TrustUserCertInfo（1.7.1章节）：可能为空，表示没有指定的证书", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0010", "需求名称": "", "优先级": "", "接口功能": "根据证书主题查询信任域证书", "接口定义": "public TrustUserCertInfo queryTrustCertBySubjectName(String subject) throws SVSException", "输入参数": [{"参数名": "subject", "类型": "String", "必填": "是", "说明": "证书主题"}], "输出": "TrustUserCertInfo（1.7.1章节）：可能为空，表示没有指定的证书", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0011", "需求名称": "", "优先级": "", "接口功能": "新增信任域证书（证书公钥算法支持：RSA、SM2、ECSDA、EdDSA）", "接口定义": "public void addTrustCert(String name，String certificate) throws SVSException", "输入参数": [{"参数名": "name", "类型": "String", "必填": "是", "说明": "名称（信任域范围内唯一）"}], "输出": "无", "异常": "SVSException", "其它说明": "信任域证书不允许重复导入，名称唯一，格式正确，当前处于有效期内", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0012", "需求名称": "", "优先级": "", "接口功能": "设置信任域证书验证业务证书策略，包括不验证、CA验证、CRL验证、OCSP验证、OCSP+CRL验证。 不验证：不验证证书有效性 CA验证：验证证书签名、证书有效期 CRL验证：验证证书签名、证书有效期、利用CRL验证证书是否撤销 OCSP验证：验证证书签名、证书有效期、利用OCSP验证证书是否撤销 OCSP+CRL验证：验证证书签名、证书有效期、利用OCSP验证证书是否撤销，如OCSP无法连接，则利用CRL验证。", "接口定义": "public void setTrustCertStrategy(String name， int strategyType, String crl, String crlUrl, boolean crlUrlFromCert, String ocspUrl )throws SVSException", "输入参数": [{"参数名": "name", "类型": "String", "必填": "是", "说明": "名称（信任域范围内唯一）"}, {"参数名": "strategyType", "类型": "int", "必填": "是", "说明": "验证策略，取值范围： 1：不验证 2：CA验证 3：CRL验证 4：OCSP验证 5：OCSP+CRL验证"}, {"参数名": "crl", "类型": "String", "必填": "否", "说明": "Base64编码的CRL"}, {"参数名": "crlUrl", "类型": "String", "必填": "否", "说明": "CRL发布点，可以是Http和LDAP发布点"}, {"参数名": "crlUrlFromCert", "类型": "boolean", "必填": "否", "说明": "从业务证书CRL发布点扩展指定的地址下载CRL"}, {"参数名": "ocsUrl", "类型": "String", "必填": "否", "说明": "OCSP查询地址"}], "输出": "无", "异常": "SVSException", "其它说明": "strategyType 选择3和5时，crl、crlUrl、crlUrlFromCert不能同时为空或false。", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0013", "需求名称": "", "优先级": "", "接口功能": "删除信任域证书", "接口定义": "public void deleteTrustCert(String name) throws SVSException", "输入参数": [{"参数名": "name", "类型": "String", "必填": "是", "说明": "名称"}], "输出": "无", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0014", "需求名称": "", "优先级": "", "接口功能": "查询业务证书列表，注意，如果服务端证书过多可能返回失败", "接口定义": "public List<UserCertInfo> queryUserCertList() throws SVSException", "输入参数": [], "输出": "List<UserCertInfo> 用户证书列表", "异常": "SVSException", "其它说明": "如服务端业务证书过多不建议调用该接口，该接口设计用于快速查询到所有用户证书，提高易用性。", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0015", "需求名称": "", "优先级": "", "接口功能": "根据名称查询业务证书", "接口定义": "public List<UserCertInfo> queryUserCertByName(String name) throws SVSException", "输入参数": [{"参数名": "name", "类型": "String", "必填": "是", "说明": "证书名称"}], "输出": "List<UserCertInfo>（1.7.2章节）：签名证书和加密证书", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0016", "需求名称": "", "优先级": "", "接口功能": "根据证书序列号查询业务证书", "接口定义": "public UserCertInfo queryUserCertBySN(String serialNumber) throws SVSException", "输入参数": [{"参数名": "serialNumber", "类型": "String", "必填": "是", "说明": "证书序列号"}], "输出": "UserCertInfo（1.7.1章节）", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0017", "需求名称": "", "优先级": "", "接口功能": "根据证书主题查询业务证书", "接口定义": "public List<UserCertInfo> queryUserCertBySubjectName(String subject) throws SVSException", "输入参数": [{"参数名": "subject", "类型": "String", "必填": "是", "说明": "证书主题"}], "输出": "UserCertInfo（1.7.1章节）：签名证书和加密证书", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0018", "需求名称": "", "优先级": "", "接口功能": "生成指定算法的密钥对存储在设备内并生成证书请求文件", "接口定义": "public String generateCSR(String name， int csrType, int keyLength, int eccCurveId, String algorithm, String subject， String challengePassword ) throws SVSException", "输入参数": [{"参数名": "name", "类型": "String", "必填": "是", "说明": "名称（业务证书范围内唯一，包括有私钥和没有私钥的证书）"}, {"参数名": "csrType", "类型": "int", "必填": "是", "说明": "CSR类型，取值范围 1：标准CSR 2：CFCA格式"}, {"参数名": "<PERSON><PERSON><PERSON><PERSON>", "类型": "int", "必填": "是", "说明": "密钥长度，取值范围： RSA：1024、2048、3072、4096 SM2：256 ECDSA：192、256、384、521 EdDSA：256"}, {"参数名": "eccCurveId", "类型": "int", "必填": "否", "说明": "ECDSA算法时生效，取值范围："}, {"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "签名算法，取值范围 SHA1WithRSA SHA256WithRSA SHA384WithRSA SHA512WithRSA SM3WithSM2 SHA256WithECDSA 。。。"}, {"参数名": "subject", "类型": "String", "必填": "是", "说明": "X500Name"}, {"参数名": "challengePassword", "类型": "String", "必填": "否", "说明": "由CA分发的挑战码"}], "输出": "Base64编码PKCS10证书请求文件", "异常": "SVSException", "其它说明": "", "支持算法": ["SHA256WithECDSA", "SM2", "SHA384WithRSA", "SHA512", "SHA512WithRSA", "SHA1WithRSA", "SHA384", "SHA1", "SM3", "SM3WithSM2", "SHA256", "SHA256WithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0019", "需求名称": "", "优先级": "", "接口功能": "", "接口定义": "public void importSignUserCert(String name， String certificate ) throws SVSException", "输入参数": [{"参数名": "name", "类型": "String", "必填": "是", "说明": "名称（证书范围内唯一，包括有私钥和没有私钥的证书）"}, {"参数名": "certificate", "类型": "String", "必填": "是", "说明": "Base64编码的X509证书"}], "输出": "无", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0021", "需求名称": "", "优先级": "", "接口功能": "导入PFX证书到应用证书区。导入前检查对应名称和对应类型证书是否存在，如存在报错，检查PFX证书与私钥是否匹配。", "接口定义": "public void importPFXUserCert(String name， int certType， String certificate， String password ) throws SVSException", "输入参数": [{"参数名": "name", "类型": "String", "必填": "是", "说明": "名称（业务证书范围内唯一，包括有私钥和没有私钥的证书）"}, {"参数名": "certType", "类型": "int", "必填": "是", "说明": "证书类型，取值范围： 1：签名证书 2：加密证书"}, {"参数名": "certificate", "类型": "String", "必填": "是", "说明": "Base64编码的PKCS12证书"}, {"参数名": "password", "类型": "String", "必填": "是", "说明": "PFX证书保护口令"}], "输出": "无", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0022", "需求名称": "", "优先级": "", "接口功能": "修改业务证书的名称", "接口定义": "public void editUserCertName(String oldName， String newName ) throws SVSException", "输入参数": [{"参数名": "old<PERSON>ame", "类型": "String", "必填": "是", "说明": "旧名称"}, {"参数名": "newName", "类型": "String", "必填": "是", "说明": "新名称"}], "输出": "无", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0023", "需求名称": "", "优先级": "", "接口功能": "停用或启用业务证书，停用后，调用该证书提示错误。如证书状态与要修改的状态一致，则返回成功。", "接口定义": "public void enableUserCert(String name， boolean enabled ) throws SVSException", "输入参数": [{"参数名": "name", "类型": "String", "必填": "是", "说明": "名称"}, {"参数名": "enabled", "类型": "boolean", "必填": "是", "说明": "证书状态，取值范围： true：启用 false：停用"}], "输出": "无", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0024", "需求名称": "", "优先级": "", "接口功能": "更新业务证书时，如存在密钥，检查证书与密钥是否匹配。旧证书自动存储到历史证书库，历史证书库的名称与原名称相同。用于验签、解密历史数据。", "接口定义": "public void updateUserCert( String name， int certType， String certificate ) throws SVSException", "输入参数": [{"参数名": "name", "类型": "String", "必填": "是", "说明": "名称"}, {"参数名": "certType", "类型": "int", "必填": "是", "说明": "证书类型，取值范围： 1：签名证书 2：加密证书"}, {"参数名": "certificate", "类型": "String", "必填": "是", "说明": "新证书。"}], "输出": "无", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0025", "需求名称": "", "优先级": "", "接口功能": "删除证书和密钥。如没有密钥时，deleteKey无效", "接口定义": "public void deleteUserCert( String name， int certType， boolean deleteKey ) throws SVSException", "输入参数": [{"参数名": "name", "类型": "String", "必填": "是", "说明": "名称"}, {"参数名": "certType", "类型": "int", "必填": "是", "说明": "证书类型，取值范围： 1：签名证书 2：加密证书"}, {"参数名": "deleteKey", "类型": "boolean", "必填": "是", "说明": "是否删除密钥，取值范围： true：删除密钥 false：不删除密钥"}], "输出": "无", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0090", "需求名称": "", "优先级": "", "接口功能": "使用外部公钥或证书实现加密运算， 支持算法：RSA、SM2、ECDSA 支持补丁：NoPadding/PKCS1Padding/OAEPPadding", "接口定义": "public byte[] encryptByPublicKey ( String algorithm， String publicKey, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：算法/补丁 补丁可省略，省略表示使用NoPadding"}, {"参数名": "public<PERSON>ey", "类型": "String", "必填": "是", "说明": "Base64编码的公钥或证书 公钥支持X509公钥、GMT0018公钥。 证书支持X509V3证书 程序可自识别"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "明文，不能为NULL或空字节数组"}], "输出": "加密结果", "异常": "SVSException", "其它说明": "注意： SM2和ECDSA使用NoPadding", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0097", "需求名称": "", "优先级": "", "接口功能": "使用指定证书序列号的内部私钥对指定数据做摘要、打补丁、裸签，", "接口定义": "public String rawSignBySN( String algorithm， String serialNumber, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SHA1WithRSA/PSS、 SHA256WithRSA/PSS、SHA224WithRSA/PSS、 SHA384WithRSA/PSS SHA512WithRSA/PSS NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "serialNumber", "类型": "String", "必填": "是", "说明": "证书序列号16进制字符串"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "待签名数据。可以是原文或哈希值。根据algorithm确定。"}], "输出": "Base64编码的签名结果", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA1WithRSA/PSS", "SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "SHA512WithRSA/PSS", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SHA256WithRSA", "SM2", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "SHA256WithRSA/PSS", "NoneWithSM2", "SHA224WithRSA/PSS", "ED25519", "SHA384WithRSA", "SHA384WithRSA/PSS", "NoneWithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0098", "需求名称": "", "优先级": "", "接口功能": "使用指定证书主题的内部私钥对指定数据做摘要、打补丁、裸签，", "接口定义": "public String rawSignByDN( String algorithm， int type， String subjectDN, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SHA1WithRSA/PSS、 SHA256WithRSA/PSS、SHA224WithRSA/PSS、 SHA384WithRSA/PSS SHA512WithRSA/PSS NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "subjectDN", "类型": "String", "必填": "是", "说明": "证书主题，X500Name的字符串"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "待签名数据。可以是原文或哈希值。根据algorithm确定。"}], "输出": "Base64编码的签名结果", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA1WithRSA/PSS", "SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "SHA512WithRSA/PSS", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SHA256WithRSA", "SM2", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "SHA256WithRSA/PSS", "NoneWithSM2", "SHA224WithRSA/PSS", "ED25519", "SHA384WithRSA", "SHA384WithRSA/PSS", "NoneWithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0103", "需求名称": "", "优先级": "", "接口功能": "使用指定证书序列号的内部证书验证签名有效性", "接口定义": "public boolean rawVerifyBySN( String algorithm， String serialNumber, byte[] data， String signature ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SHA1WithRSA/PSS、 SHA256WithRSA/PSS、SHA224WithRSA/PSS、 SHA384WithRSA/PSS SHA512WithRSA/PSS NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "serialNumber", "类型": "String", "必填": "是", "说明": "证书序列号"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "待签名数据。可以是原文或哈希值。根据algorithm确定。"}, {"参数名": "signature", "类型": "byte[]", "必填": "是", "说明": "Base64编码的签名结果"}], "输出": "验签结果", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA1WithRSA/PSS", "SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "SHA512WithRSA/PSS", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SHA256WithRSA", "SM2", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "SHA256WithRSA/PSS", "NoneWithSM2", "SHA224WithRSA/PSS", "ED25519", "SHA384WithRSA", "SHA384WithRSA/PSS", "NoneWithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0104", "需求名称": "", "优先级": "", "接口功能": "使用指定证书主题的内部公钥验证签名有效性", "接口定义": "public boolean rawVerifyByDN( String algorithm， int type， String subjectDN, byte[] data， String signature ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SHA1WithRSA/PSS、 SHA256WithRSA/PSS、SHA224WithRSA/PSS、 SHA384WithRSA/PSS SHA512WithRSA/PSS NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "subjectDN", "类型": "String", "必填": "是", "说明": "证书主题"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "待签名数据。可以是原文或哈希值。根据algorithm确定。"}, {"参数名": "signature", "类型": "byte[]", "必填": "是", "说明": "Base64编码的签名结果"}], "输出": "验签结果", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA1WithRSA/PSS", "SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "SHA512WithRSA/PSS", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SHA256WithRSA", "SM2", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "SHA256WithRSA/PSS", "NoneWithSM2", "SHA224WithRSA/PSS", "ED25519", "SHA384WithRSA", "SHA384WithRSA/PSS", "NoneWithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0105", "需求名称": "", "优先级": "", "接口功能": "使用指定证书主题的内部公钥验证签名有效性", "接口定义": "public boolean rawVerifyByPublicKey( String algorithm， String publicKey, byte[] data， String signature ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SHA1WithRSA/PSS、 SHA256WithRSA/PSS、SHA224WithRSA/PSS、 SHA384WithRSA/PSS SHA512WithRSA/PSS NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "public<PERSON>ey", "类型": "String", "必填": "是", "说明": "Base64编码的公钥或X509V3证书 程序自动识别"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "待签名数据。可以是原文或哈希值。根据algorithm确定。"}, {"参数名": "signature", "类型": "String", "必填": "是", "说明": "Base64编码的签名结果"}], "输出": "验签结果", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA1WithRSA/PSS", "SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "SHA512WithRSA/PSS", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SHA256WithRSA", "SM2", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "SHA256WithRSA/PSS", "NoneWithSM2", "SHA224WithRSA/PSS", "ED25519", "SHA384WithRSA", "SHA384WithRSA/PSS", "NoneWithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0108", "需求名称": "", "优先级": "", "接口功能": "使用指定证书序列号的内部私钥对指定文件内容做摘要、打补丁、裸签，", "接口定义": "public String rawSignFileBySN( String algorithm， String serialNumber, String filename ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SHA1WithRSA/PSS、 SHA256WithRSA/PSS、SHA224WithRSA/PSS、 SHA384WithRSA/PSS SHA512WithRSA/PSS NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "serialNumber", "类型": "String", "必填": "是", "说明": "证书序列号16进制字符串"}, {"参数名": "filename", "类型": "String", "必填": "是", "说明": "待签名文件"}], "输出": "Base64编码的签名结果", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA1WithRSA/PSS", "SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "SHA512WithRSA/PSS", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SHA256WithRSA", "SM2", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "SHA256WithRSA/PSS", "NoneWithSM2", "SHA224WithRSA/PSS", "ED25519", "SHA384WithRSA", "SHA384WithRSA/PSS", "NoneWithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0109", "需求名称": "", "优先级": "", "接口功能": "使用指定证书主题的内部私钥对指定文件内容做摘要、打补丁、裸签，", "接口定义": "public String rawSignFileByDN( String algorithm， int type， String subjectDN, String filename ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SHA1WithRSA/PSS、 SHA256WithRSA/PSS、SHA224WithRSA/PSS、 SHA384WithRSA/PSS SHA512WithRSA/PSS NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "subjectDN", "类型": "String", "必填": "是", "说明": "证书主题，X500Name的字符串"}, {"参数名": "filename", "类型": "String", "必填": "是", "说明": "待签名文件"}], "输出": "Base64编码的签名结果", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA1WithRSA/PSS", "SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "SHA512WithRSA/PSS", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SHA256WithRSA", "SM2", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "SHA256WithRSA/PSS", "NoneWithSM2", "SHA224WithRSA/PSS", "ED25519", "SHA384WithRSA", "SHA384WithRSA/PSS", "NoneWithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0114", "需求名称": "", "优先级": "", "接口功能": "使用指定证书序列号的内部证书验证签名有效性", "接口定义": "public boolean rawVerifyFileBySN( String algorithm， String serialNumber, String filename String signature ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SHA1WithRSA/PSS、 SHA256WithRSA/PSS、SHA224WithRSA/PSS、 SHA384WithRSA/PSS SHA512WithRSA/PSS NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "serialNumber", "类型": "String", "必填": "是", "说明": "证书序列号"}, {"参数名": "filename", "类型": "String", "必填": "是", "说明": "待签名文件"}, {"参数名": "signature", "类型": "String", "必填": "是", "说明": "Base64编码的签名结果"}], "输出": "验签结果", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA1WithRSA/PSS", "SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "SHA512WithRSA/PSS", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SHA256WithRSA", "SM2", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "SHA256WithRSA/PSS", "NoneWithSM2", "SHA224WithRSA/PSS", "ED25519", "SHA384WithRSA", "SHA384WithRSA/PSS", "NoneWithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0115", "需求名称": "", "优先级": "", "接口功能": "使用指定证书主题的内部公钥验证签名有效性", "接口定义": "public boolean rawVerifyFileByDN( String algorithm， int type， String subjectDN, String filename String signature ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SHA1WithRSA/PSS、 SHA256WithRSA/PSS、SHA224WithRSA/PSS、 SHA384WithRSA/PSS SHA512WithRSA/PSS NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "subjectDN", "类型": "String", "必填": "是", "说明": "证书主题"}, {"参数名": "signature", "类型": "String", "必填": "是", "说明": "Base64编码的签名结果"}], "输出": "验签结果", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA1WithRSA/PSS", "SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "SHA512WithRSA/PSS", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SHA256WithRSA", "SM2", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "SHA256WithRSA/PSS", "NoneWithSM2", "SHA224WithRSA/PSS", "ED25519", "SHA384WithRSA", "SHA384WithRSA/PSS", "NoneWithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0116", "需求名称": "", "优先级": "", "接口功能": "使用指定证书主题的内部公钥验证签名有效性", "接口定义": "public boolean rawVerifyFileByPublicKey( String algorithm， String publicKey, String filename String signature ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SHA1WithRSA/PSS、 SHA256WithRSA/PSS、SHA224WithRSA/PSS、 SHA384WithRSA/PSS SHA512WithRSA/PSS NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "public<PERSON>ey", "类型": "String", "必填": "是", "说明": "Base64编码的公钥或X509V3证书 程序自动识别"}, {"参数名": "filename", "类型": "String", "必填": "是", "说明": "待签名文件"}, {"参数名": "signature", "类型": "String", "必填": "是", "说明": "Base64编码的签名结果"}], "输出": "验签结果", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA1WithRSA/PSS", "SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "SHA512WithRSA/PSS", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SHA256WithRSA", "SM2", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "SHA256WithRSA/PSS", "NoneWithSM2", "SHA224WithRSA/PSS", "ED25519", "SHA384WithRSA", "SHA384WithRSA/PSS", "NoneWithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0119", "需求名称": "", "优先级": "", "接口功能": "使用证书序列号的内部私钥签发带原文的PKCS#7签名数据", "接口定义": "public String pkcs7AttachSignBySN( String algorithm， String serialNumber, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "serialNumber", "类型": "String", "必填": "是", "说明": "证书序列号16进制字符串"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "待签名数据。"}], "输出": "Base64编码的带原文的PKCS#7签名", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "ED25519", "SHA384WithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0120", "需求名称": "", "优先级": "", "接口功能": "使用指定证书主题的内部私钥签发带原文的PKCS#7签名数据", "接口定义": "public String pkcs7AttachSignByDN( String algorithm， int type， String subjectDN, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "subjectDN", "类型": "String", "必填": "是", "说明": "证书主题，X500Name的字符串"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "待签名数据。"}], "输出": "Base64编码的带原文的PKCS#7签名", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "ED25519", "SHA384WithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0121", "需求名称": "", "优先级": "", "接口功能": "使用外部明文私钥和外部证书签发带原文的PKCS#7签名数据", "接口定义": "public String pkcs7AttachSignByPrivateKey( String algorithm， String privateKey, String certificate, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "privateKey", "类型": "String", "必填": "是", "说明": "Base64编码的明文私钥，支持PKCS#8、GMT0018格式"}, {"参数名": "certificate", "类型": "String", "必填": "是", "说明": "Base64编码的X509V3公钥证书"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "待签名数据。"}], "输出": "Base64编码的带原文的PKCS#7签名", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "ED25519", "SHA384WithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0122", "需求名称": "", "优先级": "", "接口功能": "使用外部明文私钥和外部证书签发带原文的PKCS#7签名数据", "接口定义": "public String pkcs7AttachSignByEncryptedPrivateKey( String algorithm， String privateKey, String certificate, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "privateKey", "类型": "String", "必填": "是", "说明": "Base64编码的LMK加密的密文私钥，加密前的私钥格式PKCS#8"}, {"参数名": "certificate", "类型": "String", "必填": "是", "说明": "Base64编码的X509V3公钥证书"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "待签名数据。"}], "输出": "Base64编码的带原文的PKCS#7签名", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "ED25519", "SHA384WithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0123", "需求名称": "", "优先级": "", "接口功能": "验证PKCS#7签名是否有效，需要验证签名证书是否有效和签名是否有效。 验证签名证书有效性包括证书是否处于有效期、证书签名是否有效、证书是否被撤销。", "接口定义": "public VerifyResult pkcs7AttachVerify ( String pkcs7AttachedSignedData ) throws SVSException", "输入参数": [{"参数名": "pkcs7AttachedSignedData", "类型": "String", "必填": "是", "说明": "Base64编码的带原文的PKCS#7签名"}], "输出": "VerifyResult：验签结果，包含签名是否有效、签名证书、原文", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0124", "需求名称": "", "优先级": "", "接口功能": "验证PKCS#7签名是否有效，需要验证签名证书是否有效和签名是否有效。 验证签名证书有效性包括证书签名是否有效、证书是否被撤销。", "接口定义": "public VerifyResult pkcs7AttachAfterwardsVerify ( String pkcs7AttachedSignedData ) throws SVSException", "输入参数": [{"参数名": "pkcs7AttachedSignedData", "类型": "String", "必填": "是", "说明": "Base64编码的带原文的PKCS#7签名"}], "输出": "VerifyResult：验签结果，包含签名是否有效、签名证书、原文", "异常": "SVSException", "其它说明": "不验证签名证书是否处于有效期", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0127", "需求名称": "", "优先级": "", "接口功能": "使用证书序列号的内部私钥签发不带原文的PKCS#7签名数据", "接口定义": "public String pkcs7DetachSignBySN( String algorithm， String serialNumber, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "serialNumber", "类型": "String", "必填": "是", "说明": "证书序列号16进制字符串"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "待签名数据。"}], "输出": "Base64编码的不带原文的PKCS#7签名", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "ED25519", "SHA384WithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0128", "需求名称": "", "优先级": "", "接口功能": "使用指定证书主题的内部私钥签发不带原文的PKCS#7签名数据", "接口定义": "public String pkcs7DetachSignByDN( String algorithm， int type， String subjectDN, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "subjectDN", "类型": "String", "必填": "是", "说明": "证书主题，X500Name的字符串"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "待签名数据。"}], "输出": "Base64编码的不带原文的PKCS#7签名", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "ED25519", "SHA384WithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0129", "需求名称": "", "优先级": "", "接口功能": "使用外部明文私钥和外部证书签发不带原文的PKCS#7签名数据", "接口定义": "public String pkcs7DetachSignByPrivateKey( String algorithm， String privateKey, String certificate, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "privateKey", "类型": "String", "必填": "是", "说明": "Base64编码的明文私钥，支持PKCS#8、GMT0018格式"}, {"参数名": "certificate", "类型": "String", "必填": "是", "说明": "Base64编码的X509V3公钥证书"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "待签名数据。"}], "输出": "Base64编码的不带原文的PKCS#7签名", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "ED25519", "SHA384WithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0130", "需求名称": "", "优先级": "", "接口功能": "使用外部明文私钥和外部证书签发不带原文的PKCS#7签名数据", "接口定义": "public String pkcs7DetachSignByEncryptedPrivateKey( String algorithm， String privateKey, String certificate, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "privateKey", "类型": "String", "必填": "是", "说明": "Base64编码的LMK加密的密文私钥，加密前的私钥格式PKCS#8"}, {"参数名": "certificate", "类型": "String", "必填": "是", "说明": "Base64编码的X509V3公钥证书"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "待签名数据。"}], "输出": "Base64编码的不带原文的PKCS#7签名", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "ED25519", "SHA384WithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0131", "需求名称": "", "优先级": "", "接口功能": "验证PKCS#7签名是否有效，需要验证签名证书是否有效和签名是否有效。 验证签名证书有效性包括证书是否处于有效期、证书签名是否有效、证书是否被撤销。", "接口定义": "public VerifyResult pkcs7DetachVerify ( String pkcs7DetachedSignedData, byte[] data ) throws SVSException", "输入参数": [{"参数名": "pkcs7DetachedSignedData", "类型": "String", "必填": "是", "说明": "Base64编码的带原文的PKCS#7签名"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "被签名的原文"}], "输出": "VerifyResult：验签结果，包含签名是否有效、签名证书、原文", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0132", "需求名称": "", "优先级": "", "接口功能": "验证PKCS#7签名是否有效，需要验证签名证书是否有效和签名是否有效。 验证签名证书有效性包括证书签名是否有效、证书是否被撤销。", "接口定义": "public VerifyResult pkcs7DetachAfterwardsVerify ( String pkcs7DetachedSignedData, byte[] data ) throws SVSException", "输入参数": [{"参数名": "pkcs7DetachedSignedData", "类型": "String", "必填": "是", "说明": "Base64编码的带原文的PKCS#7签名"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "被签名的原文"}], "输出": "VerifyResult：验签结果，包含签名是否有效、签名证书、原文", "异常": "SVSException", "其它说明": "不验证签名证书是否处于有效期", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0133", "需求名称": "", "优先级": "", "接口功能": "使用内部指定索引公钥对应的证书加密数字信封", "接口定义": "public String pkcs7EncryptEnvelopByIndex( String algorithm， int type， int index, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "对称加密算法，支持 DES_ECB、DES_CBC 3DES_ECB、3DES_CBC AES_ECB、AES_CBC SM1_ECB、SM1_CBC SM4_ECB、SM4_CBC SM7_ECB、SM7_CBC"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "index", "类型": "int", "必填": "是", "说明": "公钥索引"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "明文数据。"}], "输出": "Base64编码的PKCS#7数字信封", "异常": "SVSException", "其它说明": "注意：", "支持算法": ["SM4", "SM1", "3DES", "SM7", "DES"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0134", "需求名称": "", "优先级": "", "接口功能": "使用内部指定名称公钥对应的证书加密数字信封", "接口定义": "public String pkcs7EncryptEnvelopByName( String algorithm， int type， String name, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "对称加密算法，支持 DES_ECB、DES_CBC 3DES_ECB、3DES_CBC AES_ECB、AES_CBC SM1_ECB、SM1_CBC SM4_ECB、SM4_CBC SM7_ECB、SM7_CBC"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "name", "类型": "String", "必填": "是", "说明": "公钥名称"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "明文数据。"}], "输出": "Base64编码的PKCS#7数字信封", "异常": "SVSException", "其它说明": "注意：", "支持算法": ["SM4", "SM1", "3DES", "SM7", "DES"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0135", "需求名称": "", "优先级": "", "接口功能": "使用指定DN的内部公钥证书加密数字信封", "接口定义": "public String pkcs7EncryptEnvelopBySN( String algorithm， String serialNumber, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "对称加密算法，支持 DES_ECB、DES_CBC 3DES_ECB、3DES_CBC AES_ECB、AES_CBC SM1_ECB、SM1_CBC SM4_ECB、SM4_CBC SM7_ECB、SM7_CBC"}, {"参数名": "serialNumber", "类型": "String", "必填": "是", "说明": "证书序列号"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "明文数据。"}], "输出": "Base64编码的PKCS#7数字信封", "异常": "SVSException", "其它说明": "注意：", "支持算法": ["SM4", "SM1", "3DES", "SM7", "DES"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0136", "需求名称": "", "优先级": "", "接口功能": "使用指定证书主题的内部证书加密数字信封", "接口定义": "public String pkcs7EncryptEnvelopByDN( String algorithm， int type， String subjectDN, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "对称加密算法，支持 DES_ECB、DES_CBC 3DES_ECB、3DES_CBC AES_ECB、AES_CBC SM1_ECB、SM1_CBC SM4_ECB、SM4_CBC SM7_ECB、SM7_CBC"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "subjectDN", "类型": "String", "必填": "是", "说明": "证书主题"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "明文数据。"}], "输出": "Base64编码的PKCS#7数字信封", "异常": "SVSException", "其它说明": "注意：", "支持算法": ["SM4", "SM1", "3DES", "SM7", "DES"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0137", "需求名称": "", "优先级": "", "接口功能": "使用外部证书加密数字信封", "接口定义": "public String pkcs7EncryptEnvelopByCertificate( String algorithm， String certificate, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "对称加密算法，支持 DES_ECB、DES_CBC 3DES_ECB、3DES_CBC AES_ECB、AES_CBC SM1_ECB、SM1_CBC SM4_ECB、SM4_CBC SM7_ECB、SM7_CBC"}, {"参数名": "certificate", "类型": "String", "必填": "是", "说明": "Base64编码的X509v3公钥证书"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "明文数据。"}], "输出": "Base64编码的PKCS#7数字信封", "异常": "SVSException", "其它说明": "注意：", "支持算法": ["SM4", "SM1", "3DES", "SM7", "DES"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0140", "需求名称": "", "优先级": "", "接口功能": "使用指定序列号的证书对应内部私钥解密数字信封", "接口定义": "public DecryptResult pkcs7DecryptEnvelopBySN( String serialNumber, String envelop ) throws SVSException", "输入参数": [{"参数名": "serialNumber", "类型": "String", "必填": "是", "说明": "证书序列号"}, {"参数名": "envelop", "类型": "byte[]", "必填": "是", "说明": "Base64编码的数字信封。"}], "输出": "DecryptResult：解密结果，包括加密证书、明文", "异常": "SVSException", "其它说明": "注意：", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0141", "需求名称": "", "优先级": "", "接口功能": "使用指定证书主题对应的内部私钥解密数字信封", "接口定义": "public DecryptResult pkcs7DecryptEnvelopByDN( int type， String subjectDN, String envelop ) throws SVSException", "输入参数": [{"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "subjectDN", "类型": "String", "必填": "是", "说明": "证书主题"}, {"参数名": "envelop", "类型": "byte[]", "必填": "是", "说明": "Base64编码的数字信封。"}], "输出": "DecryptResult：解密结果，包括加密证书、明文", "异常": "SVSException", "其它说明": "注意：", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0144", "需求名称": "", "优先级": "", "接口功能": "使用内部指定索引的私钥签名+内部指定索引公钥对应的证书加密数字信封", "接口定义": "public String pkcs7EncryptSignedEnvelopByIndex( String signAlgorithm， int signerType， int signerIndex, String encryptAlgorithm， int encryptorType， encryptor， byte[] data ) throws SVSException", "输入参数": [{"参数名": "signAlgorithm", "类型": "String", "必填": "是", "说明": "签名算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "signerType", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "signerIndex", "类型": "int", "必填": "是", "说明": "用于签名私钥索引"}, {"参数名": "encryptAlgorithm", "类型": "String", "必填": "是", "说明": "对称加密算法，支持 DES_ECB、DES_CBC 3DES_ECB、3DES_CBC AES_ECB、AES_CBC SM1_ECB、SM1_CBC SM4_ECB、SM4_CBC SM7_ECB、SM7_CBC"}, {"参数名": "encryptorType", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "明文数据。"}], "输出": "Base64编码的PKCS#7带签名的数字信封", "异常": "SVSException", "其它说明": "注意：", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "ED25519", "SHA384WithRSA", "SM4", "SM1", "3DES", "SM7", "DES"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0145", "需求名称": "", "优先级": "", "接口功能": "使用内部指定名称的私钥签名+内部指定名称的公钥对应的证书加密数字信封", "接口定义": "public String pkcs7EncryptSignedEnvelopByName( String signAlgorithm， int signerType， signerName, String encryptAlgorithm， int encryptorType， encryptorName， byte[] data ) throws SVSException", "输入参数": [{"参数名": "signAlgorithm", "类型": "String", "必填": "是", "说明": "签名算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "signerType", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "encryptAlgorithm", "类型": "String", "必填": "是", "说明": "对称加密算法，支持 DES_ECB、DES_CBC 3DES_ECB、3DES_CBC AES_ECB、AES_CBC SM1_ECB、SM1_CBC SM4_ECB、SM4_CBC SM7_ECB、SM7_CBC"}, {"参数名": "encryptorType", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "明文数据。"}], "输出": "Base64编码的PKCS#7带签名的数字信封", "异常": "SVSException", "其它说明": "注意：", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "ED25519", "SHA384WithRSA", "SM4", "SM1", "3DES", "SM7", "DES"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0146", "需求名称": "", "优先级": "", "接口功能": "使用内部指定序列号证书对应私钥签名+内部指定序列号证书加密数字信封", "接口定义": "public String pkcs7EncryptSignedEnvelopBySN( String signAlgorithm， signerSerialNumber, String encryptAlgorithm， encryptor， byte[] data ) throws SVSException", "输入参数": [{"参数名": "signAlgorithm", "类型": "String", "必填": "是", "说明": "签名算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "encryptAlgorithm", "类型": "String", "必填": "是", "说明": "对称加密算法，支持 DES_ECB、DES_CBC 3DES_ECB、3DES_CBC AES_ECB、AES_CBC SM1_ECB、SM1_CBC SM4_ECB、SM4_CBC SM7_ECB、SM7_CBC"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "明文数据。"}], "输出": "Base64编码的PKCS#7带签名的数字信封", "异常": "SVSException", "其它说明": "注意：", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "ED25519", "SHA384WithRSA", "SM4", "SM1", "3DES", "SM7", "DES"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0147", "需求名称": "", "优先级": "", "接口功能": "使用内部指定证书主题对应的私钥签名+内部指定证书主题的公钥对应的证书加密数字信封", "接口定义": "public String pkcs7EncryptSignedEnvelopByDN( String signAlgorithm， int signerType， signerSubjectDN, String encryptAlgorithm， int encryptorType， encryptor， byte[] data ) throws SVSException", "输入参数": [{"参数名": "signAlgorithm", "类型": "String", "必填": "是", "说明": "签名算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "signerType", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "encryptAlgorithm", "类型": "String", "必填": "是", "说明": "对称加密算法，支持 DES_ECB、DES_CBC 3DES_ECB、3DES_CBC AES_ECB、AES_CBC SM1_ECB、SM1_CBC SM4_ECB、SM4_CBC SM7_ECB、SM7_CBC"}, {"参数名": "encryptorType", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "明文数据。"}], "输出": "Base64编码的PKCS#7带签名的数字信封", "异常": "SVSException", "其它说明": "注意：", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "ED25519", "SHA384WithRSA", "SM4", "SM1", "3DES", "SM7", "DES"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0148", "需求名称": "", "优先级": "", "接口功能": "使用外私钥+外部明文私钥签名、外部证书加密数字信封", "接口定义": "public String pkcs7EncryptEnvelopByCertificate( String signAlgorithm， String signerPrivateKey， String signerCertificate String encryptAlgorithm， String encryptorCertificate, byte[] data ) throws SVSException", "输入参数": [{"参数名": "signAlgorithm", "类型": "String", "必填": "是", "说明": "签名算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "signerPrivate<PERSON><PERSON>", "类型": "String", "必填": "是", "说明": "Base64编码的明文签名私钥，支持PCS#8、GMT0018格式"}, {"参数名": "signerCertificate", "类型": "String", "必填": "是", "说明": "Base64编码的签名证书"}, {"参数名": "encryptAlgorithm", "类型": "String", "必填": "是", "说明": "对称加密算法，支持 DES_ECB、DES_CBC 3DES_ECB、3DES_CBC AES_ECB、AES_CBC SM1_ECB、SM1_CBC SM4_ECB、SM4_CBC SM7_ECB、SM7_CBC"}, {"参数名": "enryptorCertificate", "类型": "String", "必填": "是", "说明": "Base64编码的加密证书"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "明文数据。"}], "输出": "Base64编码的PKCS#7带签名的数字信封", "异常": "SVSException", "其它说明": "注意：", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "ED25519", "SHA384WithRSA", "SM4", "SM1", "3DES", "SM7", "DES"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0152", "需求名称": "", "优先级": "", "接口功能": "使用指定序列号的证书对应内部私钥解密数字信封", "接口定义": "public DecryptResult pkcs7DecryptSignedEnvelopBySN( String serialNumber, String envelop ) throws SVSException", "输入参数": [{"参数名": "serialNumber", "类型": "String", "必填": "是", "说明": "证书序列号"}, {"参数名": "envelop", "类型": "byte[]", "必填": "是", "说明": "Base64编码的数字信封。"}], "输出": "DecryptResult：解密结果，包括加密证书、签名证书、明文", "异常": "SVSException", "其它说明": "注意：如验证签名失败，则不返回解密结果", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0153", "需求名称": "", "优先级": "", "接口功能": "使用指定证书主题对应的内部私钥解密数字信封", "接口定义": "public DecryptResult pkcs7DecryptSignedEnvelopByDN( int type， String subjectDN, String envelop ) throws SVSException", "输入参数": [{"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "subjectDN", "类型": "String", "必填": "是", "说明": "证书主题"}, {"参数名": "envelop", "类型": "byte[]", "必填": "是", "说明": "Base64编码的数字信封。"}], "输出": "DecryptResult：解密结果，包括加密证书、签名证书、明文", "异常": "SVSException", "其它说明": "注意：如验证签名失败，则不返回解密结果", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0158", "需求名称": "", "优先级": "", "接口功能": "使用指定证书序列号的内部私钥对XMl封皮签名", "接口定义": "public byte[] xmlSignEnvelopedBySN( String algorithm, String serialNumber, byte[] xmlData ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "serialNumber", "类型": "String", "必填": "是", "说明": "证书序列号"}, {"参数名": "xmlData", "类型": "byte[]", "必填": "是", "说明": "XML数据。"}], "输出": "签名后的XML数据", "异常": "SVSException", "其它说明": "", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "NoneWithSM2", "ED25519", "SHA384WithRSA", "NoneWithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0160", "需求名称": "", "优先级": "", "接口功能": "使用外部私钥+外部证书对XMl封皮签名", "接口定义": "public byte[] xmlSignEnvelopedByPrivateKey( String algorithm, String privateKey， String certificate, byte[] xmlData ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "privateKey", "类型": "String", "必填": "是", "说明": "Base64编码的私钥，支持PKCS#8、GMT0018，程序自适应。"}, {"参数名": "certificate", "类型": "String", "必填": "是", "说明": "Base64编码的公钥证书"}, {"参数名": "xmlData", "类型": "byte[]", "必填": "是", "说明": "XML数据。"}], "输出": "签名后的XML数据", "异常": "SVSException", "其它说明": "", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "NoneWithSM2", "ED25519", "SHA384WithRSA", "NoneWithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0161", "需求名称": "", "优先级": "", "接口功能": "使用LMK加密的外部私钥+外部证书对XMl封皮签名", "接口定义": "public byte[] xmlSignEnvelopedByEncryptedPrivateKey( String algorithm, String privateKey， String certificate, byte[] xmlData ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "privateKey", "类型": "String", "必填": "是", "说明": "Base64编码的LMK加密的私钥，加密签私钥格式支持PKCS#8、GMT0018，程序自适应。"}, {"参数名": "certificate", "类型": "String", "必填": "是", "说明": "Base64编码的公钥证书"}, {"参数名": "xmlData", "类型": "byte[]", "必填": "是", "说明": "XML数据。"}], "输出": "签名后的XML数据", "异常": "SVSException", "其它说明": "", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "NoneWithSM2", "ED25519", "SHA384WithRSA", "NoneWithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0164", "需求名称": "", "优先级": "", "接口功能": "使用指定证书序列号的内部私钥对XMl封内签名", "接口定义": "public byte[] xmlSignEnvelopingBySN( String algorithm, String serialNumber, byte[] xmlData ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "serialNumber", "类型": "String", "必填": "是", "说明": "证书序列号"}, {"参数名": "xmlData", "类型": "byte[]", "必填": "是", "说明": "XML数据。"}], "输出": "签名后的XML数据", "异常": "SVSException", "其它说明": "", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "NoneWithSM2", "ED25519", "SHA384WithRSA", "NoneWithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0166", "需求名称": "", "优先级": "", "接口功能": "使用外部私钥+外部证书对XMl封内签名", "接口定义": "public byte[] xmlSignEnvelopingByPrivateKey( String algorithm, String privateKey， String certificate, byte[] xmlData ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "privateKey", "类型": "String", "必填": "是", "说明": "Base64编码的私钥，支持PKCS#8、GMT0018，程序自适应。"}, {"参数名": "certificate", "类型": "String", "必填": "是", "说明": "Base64编码的公钥证书"}, {"参数名": "xmlData", "类型": "byte[]", "必填": "是", "说明": "XML数据。"}], "输出": "签名后的XML数据", "异常": "SVSException", "其它说明": "", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "NoneWithSM2", "ED25519", "SHA384WithRSA", "NoneWithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0167", "需求名称": "", "优先级": "", "接口功能": "使用LMK加密的外部私钥+外部证书对XMl封内签名", "接口定义": "public byte[] xmlSignEnvelopingByEncryptedPrivateKey( String algorithm, String privateKey， String certificate, byte[] xmlData ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "privateKey", "类型": "String", "必填": "是", "说明": "Base64编码的LMK加密的私钥，加密签私钥格式支持PKCS#8、GMT0018，程序自适应。"}, {"参数名": "certificate", "类型": "String", "必填": "是", "说明": "Base64编码的公钥证书"}, {"参数名": "xmlData", "类型": "byte[]", "必填": "是", "说明": "XML数据。"}], "输出": "签名后的XML数据", "异常": "SVSException", "其它说明": "", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "NoneWithSM2", "ED25519", "SHA384WithRSA", "NoneWithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0170", "需求名称": "", "优先级": "", "接口功能": "使用指定证书序列号的内部私钥对XMl分离签名", "接口定义": "public byte[] xmlSignDetachedBySN( String algorithm, String serialNumber, String uri, ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "serialNumber", "类型": "String", "必填": "是", "说明": "证书序列号"}, {"参数名": "uri", "类型": "String", "必填": "是", "说明": "XML存储位置"}], "输出": "签名后的XML数据", "异常": "SVSException", "其它说明": "", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "NoneWithSM2", "ED25519", "SHA384WithRSA", "NoneWithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0172", "需求名称": "", "优先级": "", "接口功能": "使用外部私钥+外部证书对XMl分离签名", "接口定义": "public byte[] xmlSignDetachedByPrivateKey( String algorithm, String privateKey， String certificate, String uri, ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "privateKey", "类型": "String", "必填": "是", "说明": "Base64编码的私钥，支持PKCS#8、GMT0018，程序自适应。"}, {"参数名": "certificate", "类型": "String", "必填": "是", "说明": "Base64编码的公钥证书"}, {"参数名": "uri", "类型": "String", "必填": "是", "说明": "XML存储位置"}], "输出": "签名后的XML数据", "异常": "SVSException", "其它说明": "", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "NoneWithSM2", "ED25519", "SHA384WithRSA", "NoneWithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0173", "需求名称": "", "优先级": "", "接口功能": "使用LMK加密的外部私钥+外部证书对XMl分离签名", "接口定义": "public byte[] xmlSignDetachedByEncryptedPrivateKey( String algorithm, String privateKey， String certificate, String uri, ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "privateKey", "类型": "String", "必填": "是", "说明": "Base64编码的LMK加密的私钥，加密签私钥格式支持PKCS#8、GMT0018，程序自适应。"}, {"参数名": "certificate", "类型": "String", "必填": "是", "说明": "Base64编码的公钥证书"}, {"参数名": "uri", "类型": "String", "必填": "是", "说明": "XML存储位置"}], "输出": "签名后的XML数据", "异常": "SVSException", "其它说明": "", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "NoneWithSM2", "ED25519", "SHA384WithRSA", "NoneWithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0174", "需求名称": "", "优先级": "", "接口功能": "验证XML签名是否有效并返回签名证书", "接口定义": "public VerifyResult xmlVerify ( byte[] xml ) throws SVSException", "输入参数": [{"参数名": "xml", "类型": "byte[]", "必填": "是", "说明": "签名的XML"}], "输出": "VerifyResult：验证签名结果，包括签名证书、签名是否有效", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0175", "需求名称": "", "优先级": "", "接口功能": "验证XML签名是否有效并返回签名证书，不验证签名证书当前是否处于有效期", "接口定义": "public VerifyResult xmlVerifyAfterwards ( byte[] xml ) throws SVSException", "输入参数": [{"参数名": "xml", "类型": "byte[]", "必填": "是", "说明": "签名的XML"}], "输出": "VerifyResult：验证签名结果，包括签名证书、签名是否有效", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0178", "需求名称": "", "优先级": "", "接口功能": "使用指定证书序列号的内部私钥对PDF签名", "接口定义": "public byte[]pdfSignBySN( String algorithm, String serialNumber， PDFSignParameter pdfParameter ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "serialNumber", "类型": "String", "必填": "是", "说明": "证书序列号"}, {"参数名": "pdfParameter", "类型": "PDFSignParameter", "必填": "是", "说明": "PDF签名参数"}], "输出": "签名后的PDF数据", "异常": "SVSException", "其它说明": "注意：普通签章与骑缝签章参数区别", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "NoneWithSM2", "ED25519", "SHA384WithRSA", "NoneWithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0179", "需求名称": "", "优先级": "", "接口功能": "使用指定证书主题的内部私钥对PDF签名", "接口定义": "public byte[]pdfSignByDN( String algorithm, int type， String subjectDN， PDFSignParameter pdfParameter ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "subjectDN", "类型": "String", "必填": "是", "说明": "证书主题"}, {"参数名": "pdfParameter", "类型": "PDFSignParameter", "必填": "是", "说明": "PDF签名参数"}], "输出": "签名后的PDF数据", "异常": "SVSException", "其它说明": "注意：普通签章与骑缝签章参数区别", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "NoneWithSM2", "ED25519", "SHA384WithRSA", "NoneWithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0180", "需求名称": "", "优先级": "", "接口功能": "使用外部私钥+外部证书对PDF签名", "接口定义": "public byte[]pdfSignByPrivateKey( String algorithm, String privateKey， String certificate， PDFSignParameter pdfParameter ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "privateKey", "类型": "String", "必填": "是", "说明": "Base64编码的私钥，支持PKCS#8、GMT0018，程序自适应。"}, {"参数名": "certificate", "类型": "String", "必填": "是", "说明": "Base64编码的公钥证书"}, {"参数名": "pdfParameter", "类型": "PDFSignParameter", "必填": "是", "说明": "PDF签名参数"}], "输出": "签名后的PDF数据", "异常": "SVSException", "其它说明": "注意：普通签章与骑缝签章参数区别", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "NoneWithSM2", "ED25519", "SHA384WithRSA", "NoneWithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0181", "需求名称": "", "优先级": "", "接口功能": "使用LMK加密的外部私钥+外部证书对PDF签名", "接口定义": "public byte[]pdfSignByEncryptedPrivateKey( String algorithm, String privateKey， String certificate， PDFSignParameter pdfParameter ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "privateKey", "类型": "String", "必填": "是", "说明": "Base64编码的私钥，支持PKCS#8、GMT0018，程序自适应。"}, {"参数名": "certificate", "类型": "String", "必填": "是", "说明": "Base64编码的公钥证书"}, {"参数名": "pdfParameter", "类型": "PDFSignParameter", "必填": "是", "说明": "PDF签名参数"}], "输出": "签名后的PDF数据", "异常": "SVSException", "其它说明": "注意：普通签章与骑缝签章参数区别", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "ED25519", "SHA384WithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0184", "需求名称": "", "优先级": "", "接口功能": "验证PDF签名是否有效并返回签名证书", "接口定义": "public VerifyResult pdfVerify ( byte[] pdf, byte[] pdfPassword ) throws SVSException", "输入参数": [{"参数名": "pdf", "类型": "byte[]", "必填": "是", "说明": "签名的PDF"}, {"参数名": "pdfPassword", "类型": "byte[]", "必填": "否", "说明": "PDF密码"}], "输出": "VerifyResult：验证签名结果", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0185", "需求名称": "", "优先级": "", "接口功能": "验证PDF签名是否有效并返回签名证书，不验证证书是否处于有效期", "接口定义": "public pdfVerifyAfterwards ( byte[] pdf, byte[] pdfPassword ) throws SVSException", "输入参数": [{"参数名": "pdf", "类型": "byte[]", "必填": "是", "说明": "签名的PDF"}, {"参数名": "pdfPassword", "类型": "byte[]", "必填": "否", "说明": "PDF密码"}], "输出": "VerifyResult：验证签名结果，", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0188", "需求名称": "", "优先级": "", "接口功能": "使用指定证书序列号的内部私钥对OFD签名", "接口定义": "public byte[]ofdSignBySN( String algorithm, String serialNumber， OFDSignParameter ofdParameter ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "serialNumber", "类型": "String", "必填": "是", "说明": "证书序列号"}, {"参数名": "ofdParameter", "类型": "OFDSignParameter", "必填": "是", "说明": "OFD签名参数"}], "输出": "签名后的OFD数据", "异常": "SVSException", "其它说明": "注意：普通签章与骑缝签章参数区别", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "NoneWithSM2", "ED25519", "SHA384WithRSA", "NoneWithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0189", "需求名称": "", "优先级": "", "接口功能": "使用指定证书主题的内部私钥对OFD签名", "接口定义": "public byte[]ofdSignByDN( String algorithm, int type， String subjectDN， OFDSignParameter ofdParameter ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "subjectDN", "类型": "String", "必填": "是", "说明": "证书主题"}, {"参数名": "ofdParameter", "类型": "OFDSignParameter", "必填": "是", "说明": "OFD签名参数"}], "输出": "签名后的OFD数据", "异常": "SVSException", "其它说明": "注意：普通签章与骑缝签章参数区别", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "NoneWithSM2", "ED25519", "SHA384WithRSA", "NoneWithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0190", "需求名称": "", "优先级": "", "接口功能": "使用外部私钥+外部证书对OFD签名", "接口定义": "public byte[]ofdSignByPrivateKey( String algorithm, String privateKey， String ， OFDSignParameter ofdParameter ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "privateKey", "类型": "String", "必填": "是", "说明": "Base64编码的私钥，支持PKCS#8、GMT0018，程序自适应。"}, {"参数名": "ofdParameter", "类型": "OFDSignParameter", "必填": "是", "说明": "OFD签名参数"}], "输出": "签名后的OFD数据", "异常": "SVSException", "其它说明": "注意：普通签章与骑缝签章参数区别", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "NoneWithSM2", "ED25519", "SHA384WithRSA", "NoneWithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0191", "需求名称": "", "优先级": "", "接口功能": "使用LMK加密的外部私钥+外部证书对OFD签名", "接口定义": "public byte[]ofdSignByEncryptedPrivateKey( String algorithm, String privateKey， String， OFDSignParameter ofdParameter ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "privateKey", "类型": "String", "必填": "是", "说明": "Base64编码的私钥，支持PKCS#8、GMT0018，程序自适应。"}, {"参数名": "ofdParameter", "类型": "OFDSignParameter", "必填": "是", "说明": "OFD签名参数"}], "输出": "签名后的OFD数据", "异常": "SVSException", "其它说明": "注意：普通签章与骑缝签章参数区别", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "ED25519", "SHA384WithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0192", "需求名称": "", "优先级": "", "接口功能": "验证OFD签名是否有效并返回签名证书", "接口定义": "public VerifyResult ofdVerify ( byte[] ofd ) throws SVSException", "输入参数": [{"参数名": "ofd", "类型": "byte[]", "必填": "是", "说明": "签名的OFD"}], "输出": "VerifyResult：验证签名结果，包括签名证书、签名是否有效", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0193", "需求名称": "", "优先级": "", "接口功能": "验证OFD签名是否有效并返回签名证书，不验证证书是否处于有效期", "接口定义": "public VerifyResult ofdVerifyAfterwards ( byte[] ofd ) throws SVSException", "输入参数": [{"参数名": "ofd", "类型": "byte[]", "必填": "是", "说明": "签名的OFD"}], "输出": "VerifyResult：验证签名结果，包括签名证书、签名是否有效", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0200", "需求名称": "", "优先级": "", "接口功能": "使用指定名称的证书签发时间戳", "接口定义": "public String applyTimestampByName( String hashAlgorithm， int type, String name, byte[] msg, int reqType， byte[] tsExt, ) throws SVSException", "输入参数": [{"参数名": "hashAlgorithm", "类型": "String", "必填": "是", "说明": "哈希算法，支持： SM3、SHA1、SHA224、SHA256、SHA384、SHA512"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "name", "类型": "String", "必填": "是", "说明": "密钥名称"}, {"参数名": "msg", "类型": "byte[]", "必填": "是", "说明": "字符"}, {"参数名": "reqType", "类型": "int", "必填": "是", "说明": "时间戳是否包含时间戳服务器证书，取值： 0：包含 1：不包含"}, {"参数名": "tsExt", "类型": "byte[]", "必填": "否", "说明": "时间戳请求其他扩展DER编码"}], "输出": "Base64编码的时间戳", "异常": "无", "其它说明": "", "支持算法": ["SHA512", "SHA224", "SHA1", "SM3", "SHA256", "SHA384"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0201", "需求名称": "", "优先级": "", "接口功能": "使用指定序列号的证书签发时间戳", "接口定义": "public String applyTimestampBySN( String hashAlgorithm， String serialNumber, byte[] msg, int reqType， byte[] tsExt, ) throws SVSException", "输入参数": [{"参数名": "hashAlgorithm", "类型": "String", "必填": "是", "说明": "哈希算法，支持： SM3、SHA1、SHA224、SHA256、SHA384、SHA512"}, {"参数名": "serialNumber", "类型": "String", "必填": "是", "说明": "证书序列号"}, {"参数名": "msg", "类型": "byte[]", "必填": "是", "说明": "字符"}, {"参数名": "reqType", "类型": "int", "必填": "是", "说明": "时间戳是否包含时间戳服务器证书，取值： 0：包含 1：不包含"}, {"参数名": "tsExt", "类型": "byte[]", "必填": "否", "说明": "时间戳请求其他扩展DER编码"}], "输出": "Base64编码的时间戳", "异常": "无", "其它说明": "", "支持算法": ["SHA512", "SHA224", "SHA1", "SM3", "SHA256", "SHA384"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0202", "需求名称": "", "优先级": "", "接口功能": "使用指定主题的证书签发时间戳", "接口定义": "public String applyTimestampByDN( String hashAlgorithm， int type, String subjectDN, byte[] msg, int reqType， byte[] tsExt, ) throws SVSException", "输入参数": [{"参数名": "hashAlgorithm", "类型": "String", "必填": "是", "说明": "哈希算法，支持： SM3、SHA1、SHA224、SHA256、SHA384、SHA512"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "subjectDN", "类型": "String", "必填": "是", "说明": "证书主题"}, {"参数名": "msg", "类型": "byte[]", "必填": "是", "说明": "字符"}, {"参数名": "reqType", "类型": "int", "必填": "是", "说明": "时间戳是否包含时间戳服务器证书，取值： 0：包含 1：不包含"}, {"参数名": "tsExt", "类型": "byte[]", "必填": "否", "说明": "时间戳请求其他扩展DER编码"}], "输出": "Base64编码的时间戳", "异常": "无", "其它说明": "", "支持算法": ["SHA512", "SHA224", "SHA1", "SM3", "SHA256", "SHA384"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0203", "需求名称": "", "优先级": "", "接口功能": "验证时间戳有效性", "接口定义": "public boolean verifyTimestamp ( String timestamp， String certificate ) throws SVSException", "输入参数": [{"参数名": "timestamp", "类型": "String", "必填": "是", "说明": "Base64编码的时间戳"}, {"参数名": "certificate", "类型": "String", "必填": "否", "说明": "Base64编码的时间戳证书"}], "输出": "时间戳是否有效", "异常": "无", "其它说明": "先验证时间戳证书是否有效，再验证时间戳是否有效 certificate不为空时，优先使用certificate验证", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0218", "需求名称": "", "优先级": "", "接口功能": "生成证书请求", "接口定义": "public String generateCsr( String publicKey， String privateKey， String algorithm， String subjectDN， String challengePassword ) throws SVSException", "输入参数": [{"参数名": "public<PERSON>ey", "类型": "String", "必填": "是", "说明": "Base64编码的公钥，支持GMT0018、X509格式"}, {"参数名": "privateKey", "类型": "String", "必填": "是", "说明": "Base64编码的私钥，支持GMT0018、PKCS#8格式"}, {"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "签名算法，取值范围 SHA1WithRSA SHA256WithRSA SHA384WithRSA SHA512WithRSA SM3WithSM2 SHA256WithECDSA 。。。"}, {"参数名": "subjectDN", "类型": "String", "必填": "是", "说明": "证书主题，X500Name格式"}, {"参数名": "challengePassword", "类型": "String", "必填": "否", "说明": "由CA分发的挑战码"}], "输出": "Base64编码的GMT0018规范的密文", "异常": "SVSException", "其它说明": "", "支持算法": ["SHA256WithECDSA", "SM2", "SHA384WithRSA", "SHA512", "SHA512WithRSA", "SHA1WithRSA", "SHA384", "SHA1", "SM3", "SM3WithSM2", "SHA256", "SHA256WithRSA"]}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0219", "需求名称": "", "优先级": "", "接口功能": "解析证书内容", "接口定义": "public CertificateInfo parseCertificate( String certificate ) throws SVSException", "输入参数": [{"参数名": "certificate", "类型": "String", "必填": "是", "说明": "Base64编码的证书"}], "输出": "CertificateInfo：证书详细信息", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0220", "需求名称": "", "优先级": "", "接口功能": "将Base64编码的证书和Base64编码的PKCS#8私钥合成PKCS#12格式证书", "接口定义": "public String generatePFX ( String certificate， String privateKey ) throws SVSException", "输入参数": [{"参数名": "certificate", "类型": "String", "必填": "是", "说明": "Base64编码的X509v3证书"}, {"参数名": "privateKey", "类型": "String", "必填": "是", "说明": "Base64编码的PKCS#8私钥"}], "输出": "Base64编码的PKCS#12证书", "异常": "SVSException", "其它说明": "注意，合成前需要检查证书与私钥是否匹配", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0221", "需求名称": "", "优先级": "", "接口功能": "解析PFX证书为Base64编码的证书和Base64编码的PKCS#8私钥", "接口定义": "public String[] parsePFX ( String pfx ) throws SVSException", "输入参数": [{"参数名": "pfx", "类型": "String", "必填": "是", "说明": "Base64编码的PKCS#12证书"}], "输出": "String[]： [0] Base64编码的证书 [1] Base64编码的PKCS#8私钥", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0222", "需求名称": "", "优先级": "", "接口功能": "信任域证书信息类", "接口定义": "public class TrustUserCertInfo { private int index; private String name; private String serialNumber; private String subject; private String issuer; private String publicKey; private String algorithm; private String certificate; //.每个属性自带get属性 }", "输入参数": [], "输出": "无", "异常": "无", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0223", "需求名称": "", "优先级": "", "接口功能": "用户证书信息类，", "接口定义": "public class UserCertInfo { private int type； private int status； private int authCode； private int index; private String name; private String serialNumber; private String subject; private String issuer; private String publicKey; private String algorithm; private String certificate; private String trustCertName； //.每个属性自带get属性 }", "输入参数": [], "输出": "无", "异常": "无", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0224", "需求名称": "", "优先级": "", "接口功能": "用户证书信息类，", "接口定义": "public class CertificateInfo { private int version； private String serialNumber; private String issuer; private String subject; private Date notBefore， private Date notAfter， private String publicKey; private String algorithm; //.每个属性自带get属性 }", "输入参数": [], "输出": "无", "异常": "无", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0228", "需求名称": "", "优先级": "", "接口功能": "验证签名返回结果", "接口定义": "public class VerifyResult{ private boolean valid； private String signerCertificate； private byte[] plainData; //.每个属性自带get属性 }", "输入参数": [], "输出": "无", "异常": "无", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0229", "需求名称": "", "优先级": "", "接口功能": "验证签名返回结果", "接口定义": "public class VerifyResult{ private String encryptorCertificate； private String signerCertificate； private byte[] plainData; //.每个属性自带get属性 }", "输入参数": [], "输出": "无", "异常": "无", "其它说明": "", "支持算法": []}], "密钥管理": [{"章节": "密钥管理", "需求编号": "PR-F-SVS-0026", "需求名称": "", "优先级": "", "接口功能": "查询所有对称密钥，在密钥量较大情况下，不适用。这里设计是为了客户易用性。", "接口定义": "public List<SymmKeyInfo> querySymmKeyList() throws SVSException", "输入参数": [], "输出": "List<SymmKeyInfo> 对称密钥列表。SymmKeyInfo见1.7.", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0027", "需求名称": "", "优先级": "", "接口功能": "按照名称查询对称密钥。", "接口定义": "public SymmKeyInfo querySymmKeyByName(String name) throws SVSException", "输入参数": [{"参数名": "name", "类型": "String", "必填": "是", "说明": "对称密钥名称"}], "输出": "SymmKeyInfo：对称密钥信息，可能为NULL", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0028", "需求名称": "", "优先级": "", "接口功能": "按照索引查询对称密钥。", "接口定义": "public SymmKeyInfo querySymmKeyByIndex(int keyIndex) throws SVSException", "输入参数": [{"参数名": "keyIndex", "类型": "int", "必填": "是", "说明": "密钥索引"}], "输出": "SymmKeyInfo：对称密钥信息，可能为NULL", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0029", "需求名称": "", "优先级": "", "接口功能": "生成内部对称密钥，可指定索引。不指定索引时，后台程序查找空闲索引。", "接口定义": "public SymmKeyInfo generateInteranlSymmKey( int bits， int keyIndex， String name， String algorithm ) throws SVSException", "输入参数": [{"参数名": "bits", "类型": "int", "必填": "是", "说明": "密钥长度，取值范围： DES：64 3DES：128、192. SM1、SM4、SM7：128. AES：128、192、256."}, {"参数名": "keyIndex", "类型": "int", "必填": "是", "说明": "密钥索引，取值可包括0，表示不指定密钥索引。"}, {"参数名": "name", "类型": "String", "必填": "是", "说明": "密钥名称"}, {"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "密钥算法，取值范围： DES、3DES、AES、SM1、SM4、SM7"}], "输出": "SymmKeyInfo对象。从该对象可获取密钥存储索引以及KCV", "异常": "SVSException", "其它说明": "", "支持算法": ["SM4", "SM1", "3DES", "SM7", "DES"]}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0030", "需求名称": "", "优先级": "", "接口功能": "生成外部对称密钥，返回明文密钥", "接口定义": "public String generateSymmKey( int bits ) throws SVSException", "输入参数": [{"参数名": "bits", "类型": "int", "必填": "是", "说明": "密钥长度，取值范围： DES：64 3DES：128、192. SM1、SM4、SM7：128. AES：128、192、256."}], "输出": "Base64编码的明文的对称密钥", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0031", "需求名称": "", "优先级": "", "接口功能": "生成外部对称密钥，返回LMK加密的密钥密文", "接口定义": "public String generateSymmKeyByLMK( int bits ) throws SVSException", "输入参数": [{"参数名": "bits", "类型": "int", "必填": "是", "说明": "密钥长度，取值范围： DES：64 3DES：128、192. SM1、SM4、SM7：128. AES：128、192、256."}], "输出": "Base64编码的LMK加密的对称密钥", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0032", "需求名称": "", "优先级": "", "接口功能": "生成外部对称密钥，返回LMK加密的密钥密文", "接口定义": "public byte[] generateSymmKeyByPublicKey( int bits, String publicKey ) throws SVSException", "输入参数": [{"参数名": "bits", "类型": "int", "必填": "是", "说明": "密钥长度，取值范围： DES：64 3DES：128、192. SM1、SM4、SM7：128. AES：128、192、256."}, {"参数名": "public<PERSON>ey", "类型": "String", "必填": "是", "说明": "Base64编码的公钥，公钥格式为PublicKeyInfo（与证书中相同）"}], "输出": "Base64编码的公钥加密的对称密钥", "异常": "SVSException", "其它说明": "公钥支持RSA公钥、SM2公钥。 RSA公钥：先做PKCS1Padding补丁，再公钥加密 SM2公钥：不做补丁直接公钥加密。密文格式为GMT0009", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0033", "需求名称": "", "优先级": "", "接口功能": "导入明文对称密钥保存，可指定索引位置，不指定时由程序自动查找位置存储并返回密钥索引。", "接口定义": "public SymmKeyInfo importSymmKey( int keyIndex， String name， String algorithm， String key ) throws SVSException", "输入参数": [{"参数名": "keyIndex", "类型": "int", "必填": "是", "说明": "密钥索引，取值可包括0，表示不指定密钥索引。"}, {"参数名": "name", "类型": "String", "必填": "是", "说明": "密钥名称"}, {"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "密钥算法，取值范围： DES、3DES、AES、SM1、SM4、SM7"}, {"参数名": "key", "类型": "String", "必填": "是", "说明": "Base64编码的对称密钥明文"}], "输出": "SymmKeyInfo对象。从该对象可获取密钥存储索引以及KCV", "异常": "SVSException", "其它说明": "", "支持算法": ["SM4", "SM1", "3DES", "SM7", "DES"]}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0034", "需求名称": "", "优先级": "", "接口功能": "导入LMK加密的对称密钥保存，可指定索引位置，不指定时由程序自动查找位置存储并返回密钥索引。", "接口定义": "public SymmKeyInfo importSymmKeyByLMK( int keyIndex， String name， String algorithm， String key ) throws SVSException", "输入参数": [{"参数名": "keyIndex", "类型": "int", "必填": "是", "说明": "密钥索引，取值可包括0，表示不指定密钥索引。"}, {"参数名": "name", "类型": "String", "必填": "是", "说明": "密钥名称"}, {"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "密钥算法，取值范围： DES、3DES、AES、SM1、SM4、SM7"}, {"参数名": "key", "类型": "String", "必填": "是", "说明": "Base64编码的LMK加密的对称密钥"}], "输出": "SymmKeyInfo对象。从该对象可获取密钥存储索引以及KCV", "异常": "SVSException", "其它说明": "", "支持算法": ["SM4", "SM1", "3DES", "SM7", "DES"]}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0035", "需求名称": "", "优先级": "", "接口功能": "导入公钥加密的对称密钥保存，可指定存储索引位置，不指定时由程序自动查找位置存储并返回密钥索引。导入前先使用指定索引的对称密钥解密。", "接口定义": "public SymmKeyInfo importSymmKeyByPublicKey( int keyIndex， String name， String algorithm， String key， String decryptKeyAlgorithm， int decryptKeyIndex ) throws SVSException", "输入参数": [{"参数名": "keyIndex", "类型": "int", "必填": "是", "说明": "密钥索引，取值可包括0，表示不指定密钥索引。"}, {"参数名": "name", "类型": "String", "必填": "是", "说明": "密钥名称"}, {"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "密钥算法，取值范围： DES、3DES、AES、SM1、SM4、SM7"}, {"参数名": "key", "类型": "String", "必填": "是", "说明": "Base64编码的加密的对称密钥"}, {"参数名": "decryptKeyAlgorithm", "类型": "String", "必填": "是", "说明": "解密密钥算法，取值范围： RSA、SM2"}, {"参数名": "decryptKeyIndex", "类型": "int", "必填": "是", "说明": "解密密钥索引"}], "输出": "SymmKeyInfo对象。从该对象可获取密钥存储索引以及KCV", "异常": "SVSException", "其它说明": "", "支持算法": ["SM4", "SM1", "3DES", "SM7", "DES", "SM2"]}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0036", "需求名称": "", "优先级": "", "接口功能": "导出指定名称的对称密钥，使用LMK加密输出。", "接口定义": "public String exportSymmKeyByLMK( String name ) throws SVSException", "输入参数": [{"参数名": "name", "类型": "String", "必填": "是", "说明": "密钥名称"}], "输出": "Base64编码的LMK加密的对称密钥", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0037", "需求名称": "", "优先级": "", "接口功能": "导出指定名称的对称密钥，使用指定公钥加密输出。", "接口定义": "public String exportSymmKeyByLMK( String name, String publicKey ) throws SVSException", "输入参数": [{"参数名": "name", "类型": "String", "必填": "是", "说明": "密钥名称"}, {"参数名": "public<PERSON>ey", "类型": "String", "必填": "是", "说明": "Base64编码的公钥，公钥格式为PublicKeyInfo（与证书中相同）"}], "输出": "Base64编码的公钥加密的对称密钥", "异常": "SVSException", "其它说明": "公钥支持RSA公钥、SM2公钥。 RSA公钥：先做PKCS1Padding补丁，再公钥加密 SM2公钥：不做补丁直接公钥加密加密。密文格式为GMT0009", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0038", "需求名称": "", "优先级": "", "接口功能": "停用或启用对称密钥，停用后，调用该密钥提示错误。如密钥状态与要修改的状态一致，则返回成功。", "接口定义": "public void enableSymmKey(String name， boolean enabled ) throws SVSException", "输入参数": [{"参数名": "name", "类型": "String", "必填": "是", "说明": "名称"}, {"参数名": "enabled", "类型": "boolean", "必填": "是", "说明": "密钥状态，取值范围： true：启用 false：停用"}], "输出": "无", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0039", "需求名称": "", "优先级": "", "接口功能": "删除对称密钥", "接口定义": "public void deleteSymmKey( String name ) throws SVSException", "输入参数": [{"参数名": "name", "类型": "String", "必填": "是", "说明": "名称"}], "输出": "无", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0040", "需求名称": "", "优先级": "", "接口功能": "查询指定算法的非对称密钥，在密钥量较大情况下，不适用。这里设计是为了客户易用性。", "接口定义": "public List<AsymmKeyInfo> queryAsymmKeyList( String algorithm ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "密钥算法，取值范围： RSA、SM2、ECDSA、EdDSA"}], "输出": "List<> 对称密钥列表。见1.7.", "异常": "SVSException", "其它说明": "", "支持算法": ["SM2"]}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0041", "需求名称": "", "优先级": "", "接口功能": "按照名称查询对称密钥。", "接口定义": "public List<AsymmKeyInfo> queryAsymmKeyByName(String name) throws SVSException", "输入参数": [{"参数名": "name", "类型": "String", "必填": "是", "说明": "非对称密钥名称"}], "输出": "List<AsymmKeyInfo>：非对称密钥信息.包含签名密钥对和加密密钥对", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0042", "需求名称": "", "优先级": "", "接口功能": "按照索引查询非对称密钥。", "接口定义": "public List<AsymmKeyInfo> queryAsymmKeyByIndex( int keyIndex, String algorithm ) throws SVSException", "输入参数": [{"参数名": "keyIndex", "类型": "int", "必填": "是", "说明": "密钥索引"}], "输出": "List<AsymmKeyInfo>：非对称密钥信息。包含签名密钥对和加密密钥对", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0043", "需求名称": "", "优先级": "", "接口功能": "生成内部非对称密钥，可指定索引。不指定索引时，后台程序查找空闲索引。", "接口定义": "public AsymmKeyInfo generateInteranlAsymmKey( int type， int keyLength， int eccCurveId， int keyIndex， String name， String algorithm ) throws SVSException", "输入参数": [{"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "<PERSON><PERSON><PERSON><PERSON>", "类型": "int", "必填": "是", "说明": "密钥长度，取值范围： RSA：1024、2048、3072、4096 SM2：256 ECDSA：192、256、384、521 EdDSA：256"}, {"参数名": "eccCurveId", "类型": "int", "必填": "否", "说明": "ECDSA算法时生效，取值范围："}, {"参数名": "keyIndex", "类型": "int", "必填": "是", "说明": "密钥索引"}, {"参数名": "name", "类型": "String", "必填": "是", "说明": "密钥名称"}, {"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "密钥算法，取值范围： RSA、SM2、ECDSA、EdDSA、SM9"}], "输出": "AsymmKeyInfo对象", "异常": "SVSException", "其它说明": "", "支持算法": ["SM9", "SM2"]}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0044", "需求名称": "", "优先级": "", "接口功能": "生成外部对称密钥，返回明文密钥", "接口定义": "public AsymmKeyPair generateymmKey( int keyLength， int eccCurveId， String algorithm ) throws SVSException", "输入参数": [{"参数名": "<PERSON><PERSON><PERSON><PERSON>", "类型": "int", "必填": "是", "说明": "密钥长度，取值范围： RSA：1024、2048、3072、4096 SM2：256 ECDSA：192、256、384、521 EdDSA：256"}, {"参数名": "eccCurveId", "类型": "int", "必填": "否", "说明": ""}, {"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "密钥算法，取值范围： RSA、SM2、ECDSA、EdDSA"}], "输出": "AsymmKeyPair：Base64编码的明文的非对称密钥对", "异常": "SVSException", "其它说明": "", "支持算法": ["SM2"]}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0045", "需求名称": "", "优先级": "", "接口功能": "生成外部对称密钥，返回LMK加密的密钥密文", "接口定义": "public AsymmKeyPair generateymmKey( int keyLength， int eccCurveId， String algorithm ) throws SVSException", "输入参数": [{"参数名": "<PERSON><PERSON><PERSON><PERSON>", "类型": "int", "必填": "是", "说明": "密钥长度，取值范围： RSA：1024、2048、3072、4096 SM2：256 ECDSA：192、256、384、521 EdDSA：256"}, {"参数名": "eccCurveId", "类型": "int", "必填": "否", "说明": "ECDSA算法时生效，取值范围："}, {"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "密钥算法，取值范围： RSA、SM2、ECDSA、EdDSA"}], "输出": "AsymmKeyPair：非对称密钥对，私钥使用LMK加密。", "异常": "SVSException", "其它说明": "加密前数据为PKCS#8格式的私钥。加密算法使用SM4/CBC/NoPadding。当密钥长度不足时，右侧补充0。iv使用全0.", "支持算法": ["SM2"]}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0046", "需求名称": "", "优先级": "", "接口功能": "导入明文非对称密钥对，可指定索引位置，不指定时由程序自动查找位置存储并返回密钥索引。", "接口定义": "public AsymmKeyInfo importAsymmKey( int type， int keyIndex， String name， String algorithm， String publicKey， String privateKey ) throws SVSException", "输入参数": [{"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "keyIndex", "类型": "int", "必填": "是", "说明": "密钥索引，取值可包括0，表示不指定密钥索引。"}, {"参数名": "name", "类型": "String", "必填": "是", "说明": "密钥名称"}, {"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "密钥算法，取值范围： RSA、SM2、ECDSA、EdDSA"}, {"参数名": "public<PERSON>ey", "类型": "String", "必填": "是", "说明": "Base64编码的X509格式的公钥"}, {"参数名": "privateKey", "类型": "String", "必填": "是", "说明": "Base64编码的PKCS#8格式的私钥"}], "输出": "AsymmKeyInfo对象。从该对象可获取密钥存储索引", "异常": "SVSException", "其它说明": "", "支持算法": ["SM2"]}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0047", "需求名称": "", "优先级": "", "接口功能": "导入LMK加密的非对称密钥对，可指定索引位置，不指定时由程序自动查找位置存储并返回密钥索引。", "接口定义": "public SymmKeyInfo importSymmKeyByLMK( int type， int keyIndex， String name， String algorithm， String publicKey， String privateKey ) throws SVSException", "输入参数": [{"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "keyIndex", "类型": "int", "必填": "是", "说明": "密钥索引，取值可包括0，表示不指定密钥索引。"}, {"参数名": "name", "类型": "String", "必填": "是", "说明": "密钥名称"}, {"参数名": "algorithm", "类型": "String", "必填": "是", "说明": ""}, {"参数名": "public<PERSON>ey", "类型": "String", "必填": "是", "说明": "Base64编码的X509格式的公钥"}, {"参数名": "privateKey", "类型": "String", "必填": "是", "说明": "Base64编码的LMK加密的PKCS#8格式的私钥"}], "输出": "AsymmKeyInfo对象。从该对象可获取密钥存储索引", "异常": "SVSException", "其它说明": "加密算法使用SM4/CBC/NoPadding。当密钥长度不足时，右侧补充0. iv使用全0.", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0048", "需求名称": "", "优先级": "", "接口功能": "导出指定名称的非对称密钥对，使用LMK加密输出。", "接口定义": "public AsymmKeyPair exportAsymmKeyByLMK( int type， String name ) throws SVSException", "输入参数": [{"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "name", "类型": "String", "必填": "是", "说明": "密钥名称"}], "输出": "公钥：Base64编码的X509格式的公钥 私钥：Base64编码的LMK加密的PKCS#8格式的私钥", "异常": "SVSException", "其它说明": ".", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0049", "需求名称": "", "优先级": "", "接口功能": "停用或启用非对称密钥，停用后，调用该密钥提示错误。如密钥状态与要修改的状态一致，则返回成功。", "接口定义": "public void enableAsymmKey(String name， boolean enabled ) throws SVSException", "输入参数": [{"参数名": "name", "类型": "String", "必填": "是", "说明": "名称"}, {"参数名": "enabled", "类型": "boolean", "必填": "是", "说明": "密钥状态，取值范围： true：启用 false：停用"}], "输出": "无", "异常": "SVSException", "其它说明": "签名密钥对和加密密钥对保持相同状态", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0051", "需求名称": "", "优先级": "", "接口功能": "获取指定的私钥访问控制权限。", "接口定义": "public void getPrivateKeyAccessRight ( String name， String pin ) throws SVSException", "输入参数": [{"参数名": "name", "类型": "String", "必填": "是", "说明": "名称"}, {"参数名": "pin", "类型": "String", "必填": "是", "说明": "私钥访问控制全选，ASCII码字符串，长度要求8-32"}], "输出": "无", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0052", "需求名称": "", "优先级": "", "接口功能": "删除对称密钥", "接口定义": "public void deleteAsymmKey( int type， String name ) throws SVSException", "输入参数": [{"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对 3：签名、加密密钥对"}, {"参数名": "name", "类型": "String", "必填": "是", "说明": "名称"}], "输出": "无", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0054", "需求名称": "", "优先级": "", "接口功能": "实现单步哈希运算，支持的算法包括MD5、SHA1、SHA256、SHA224、SHA384、SHA512、SM3.其中SM3支持带Id的运算", "接口定义": "public byte[] hash( String algorithm， byte[] data, byte[] sm2PublicKey, byte[] sm2UserId ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "哈希算法类型，取值范围： MD5、SHA1、SHA256、SHA224、SHA384、SHA512、SM3."}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "明文，不能为NULL或空字节数组"}, {"参数名": "sm2PublicKey", "类型": "byte[]", "必填": "否", "说明": "SM2公钥，哈希算法为SM3时生效。支持X.509格式、GMT0018格式的公钥。程序自适应"}, {"参数名": "sm2UserId", "类型": "byte[]", "必填": "否", "说明": "SM2公钥Id，哈希算法为SM3且sm2PublicKey不为NULL时生效。 长度限制1-128"}], "输出": "哈希结果", "异常": "SVSException", "其它说明": "sm2PublicKey不为空且algorithm是SM3时，按照计算Z值的SM3哈希算法计算摘要，sm2UserId为空，则使用默认id “1234567812345678” 计算Z值。 sm2PublicKey为空且algorithm是SM3时，不管sm2UserId是否为空，都按照不计算Z值计算SM3摘要。", "支持算法": ["MD5", "SHA512", "SHA224", "SHA1", "SM3", "SHA256", "SHA384"]}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0058", "需求名称": "", "优先级": "", "接口功能": "使用指定索引的对称密钥实现单步MAC运算，支持计算多种MAC，包括HMAC、CMAC、CBCMAC", "接口定义": "public byte[] macByIndex( String algorithm， int keyIndex, byte[] iv, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，取值范围： HMAC： HMAC-MD5、HMAC-SHA1、HMAC-SHA224、HMAC-SHA256、HMAC-SHA384、HMAC-SHA512、HMAC-SM3 CBCMAC： DES-CBCMAC、3DES-CBCMAC、 AES-CBCMAC、SM1-CBCMAC、 SM4-CBCMAC、SM7-CBCMAC CMAC： DES-CMAC、3DES-CMAC、 AES-CMAC、SM1-CMAC、 SM4-CMAC、SM7-CMAC"}, {"参数名": "keyIndex", "类型": "int", "必填": "是", "说明": "对称密钥索引"}, {"参数名": "iv", "类型": "byte[]", "必填": "否", "说明": "CBCMAC和CMAC时生效，长度一般是算法块长"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "明文，不能为NULL或空字节数组"}], "输出": "MAC结果", "异常": "SVSException", "其它说明": "", "支持算法": ["SM4", "MD5", "SHA512", "SM1", "SHA224", "3DES", "SHA1", "SM7", "SM3", "DES", "SHA256", "SHA384"]}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0059", "需求名称": "", "优先级": "", "接口功能": "使用指定名称的对称密钥实现单步MAC运算，支持计算多种MAC，包括HMAC、CMAC、CBCMAC", "接口定义": "public byte[] macByName( String algorithm， String name, byte[] iv, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，取值范围： HMAC： HMAC-MD5、HMAC-SHA1、HMAC-SHA224、HMAC-SHA256、HMAC-SHA384、HMAC-SHA512、HMAC-SM3 CBCMAC： DES-CBCMAC、3DES-CBCMAC、 AES-CBCMAC、SM1-CBCMAC、 SM4-CBCMAC、SM7-CBCMAC CMAC： DES-CMAC、3DES-CMAC、 AES-CMAC、SM1-CMAC、 SM4-CMAC、SM7-CMAC"}, {"参数名": "name", "类型": "String", "必填": "是", "说明": "对称密钥名称"}, {"参数名": "iv", "类型": "byte[]", "必填": "否", "说明": ""}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": ""}], "输出": "MAC结果", "异常": "SVSException", "其它说明": "", "支持算法": ["SM4", "MD5", "SHA512", "SM1", "SHA224", "3DES", "SHA1", "SM7", "SM3", "DES", "SHA256", "SHA384"]}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0060", "需求名称": "", "优先级": "", "接口功能": "使用外部明文对称密钥实现单步MAC运算，支持计算多种MAC，包括HMAC、CMAC、CBCMAC", "接口定义": "public byte[] macBy<PERSON>ey( String algorithm， byte[] key byte[] iv, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，取值范围： HMAC： HMAC-MD5、HMAC-SHA1、HMAC-SHA224、HMAC-SHA256、HMAC-SHA384、HMAC-SHA512、HMAC-SM3 CBCMAC： DES-CBCMAC、3DES-CBCMAC、 AES-CBCMAC、SM1-CBCMAC、 SM4-CBCMAC、SM7-CBCMAC CMAC： DES-CMAC、3DES-CMAC、 AES-CMAC、SM1-CMAC、 SM4-CMAC、SM7-CMAC"}, {"参数名": "key", "类型": "byte[]", "必填": "是", "说明": "对称密钥"}, {"参数名": "iv", "类型": "byte[]", "必填": "否", "说明": ""}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": ""}], "输出": "MAC结果", "异常": "SVSException", "其它说明": "", "支持算法": ["SM4", "MD5", "SHA512", "SM1", "SHA224", "3DES", "SHA1", "SM7", "SM3", "DES", "SHA256", "SHA384"]}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0061", "需求名称": "", "优先级": "", "接口功能": "使用LMK加密的外部对称密钥实现单步MAC运算，支持计算多种MAC，包括HMAC、CMAC、CBCMAC", "接口定义": "public byte[] macByEncryptedKey( String algorithm， byte[] key byte[] iv, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，取值范围： HMAC： HMAC-MD5、HMAC-SHA1、HMAC-SHA224、HMAC-SHA256、HMAC-SHA384、HMAC-SHA512、HMAC-SM3 CBCMAC： DES-CBCMAC、3DES-CBCMAC、 AES-CBCMAC、SM1-CBCMAC、 SM4-CBCMAC、SM7-CBCMAC CMAC： DES-CMAC、3DES-CMAC、 AES-CMAC、SM1-CMAC、 SM4-CMAC、SM7-CMAC"}, {"参数名": "key", "类型": "byte[]", "必填": "是", "说明": "LMK加密的对称密钥"}, {"参数名": "iv", "类型": "byte[]", "必填": "否", "说明": ""}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": ""}], "输出": "MAC结果", "异常": "SVSException", "其它说明": "", "支持算法": ["SM4", "MD5", "SHA512", "SM1", "SHA224", "3DES", "SHA1", "SM7", "SM3", "DES", "SHA256", "SHA384"]}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0068", "需求名称": "", "优先级": "", "接口功能": "使用指定索引的对称密钥实现单步对称加密运算， 支持算法：DES/3DES/AES/SM1/SM4/SM7 支持模式：ECB/CBC/CFB/OFB/CTR/GCM/CCM/XTS/FPE 支持补丁：NoPadding/PKCS5Padding/PKCS7Padding/", "接口定义": "public byte[] encryptByIndex( String algorithm， int keyIndex, byte[] aad, byte[] iv, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：算法/模式/补丁"}, {"参数名": "keyIndex", "类型": "int", "必填": "是", "说明": "对称密钥索引"}, {"参数名": "aad", "类型": "byte[]", "必填": "否", "说明": "GCM、CCM模式时使用的额外参数。长度受算法模式控制"}, {"参数名": "iv", "类型": "byte[]", "必填": "否", "说明": "非ECB时生效，长度一般是算法块长"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "明文，不能为NULL或空字节数组"}], "输出": "加密结果", "异常": "SVSException", "其它说明": "注意： 不是所有的算法都支持全部模式， 除ECB、CBC模式外，其他模式只需要NoPadding GCM、CCM模式最后一个分组是校验码", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0069", "需求名称": "", "优先级": "", "接口功能": "使用指定名称的对称密钥实现单步对称加密运算， 支持算法：DES/3DES/AES/SM1/SM4/SM7 支持模式：ECB/CBC/CFB/OFB/CTR/GCM/CCM/XTS/FPE 支持补丁：NoPadding/PKCS5Padding/PKCS7Padding/", "接口定义": "public byte[] encryptByName( String algorithm， String name, byte[] aad, byte[] iv, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：算法/模式/补丁"}, {"参数名": "keyIndex", "类型": "int", "必填": "是", "说明": "对称密钥名称"}, {"参数名": "aad", "类型": "byte[]", "必填": "否", "说明": "GCM、CCM模式时使用的额外参数。长度受算法模式控制"}, {"参数名": "iv", "类型": "byte[]", "必填": "否", "说明": "非ECB时生效，长度一般是算法块长"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "明文，不能为NULL或空字节数组"}], "输出": "加密结果", "异常": "SVSException", "其它说明": "注意： 不是所有的算法都支持全部模式， 除ECB、CBC模式外，其他模式只需要NoPadding GCM、CCM模式最后一个分组是校验码", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0070", "需求名称": "", "优先级": "", "接口功能": "使用外部明文对称密钥实现单步对称加密运算， 支持算法：DES/3DES/AES/SM1/SM4/SM7 支持模式：ECB/CBC/CFB/OFB/CTR/GCM/CCM/XTS/FPE 支持补丁：NoPadding/PKCS5Padding/PKCS7Padding/", "接口定义": "public byte[] encryptByKey( String algorithm， byte[] key, byte[] aad, byte[] iv, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：算法/模式/补丁"}, {"参数名": "key", "类型": "byte[]", "必填": "是", "说明": "对称密钥"}, {"参数名": "aad", "类型": "byte[]", "必填": "否", "说明": "GCM、CCM模式时使用的额外参数。长度受算法模式控制"}, {"参数名": "iv", "类型": "byte[]", "必填": "否", "说明": "非ECB时生效，长度一般是算法块长"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "明文，不能为NULL或空字节数组"}], "输出": "加密结果", "异常": "SVSException", "其它说明": "注意： 不是所有的算法都支持全部模式， 除ECB、CBC模式外，其他模式只需要NoPadding GCM、CCM模式最后一个分组是校验码", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0071", "需求名称": "", "优先级": "", "接口功能": "使用外部密文对称密钥实现单步对称加密运算， 支持算法：DES/3DES/AES/SM1/SM4/SM7 支持模式：ECB/CBC/CFB/OFB/CTR/GCM/CCM/XTS/FPE 支持补丁：NoPadding/PKCS5Padding/PKCS7Padding/", "接口定义": "public byte[] encryptByEncryptedKey( String algorithm， byte[] key, byte[] aad, byte[] iv, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：算法/模式/补丁"}, {"参数名": "key", "类型": "byte[]", "必填": "是", "说明": "LMK加密的对称密钥"}, {"参数名": "aad", "类型": "byte[]", "必填": "否", "说明": "GCM、CCM模式时使用的额外参数。长度受算法模式控制"}, {"参数名": "iv", "类型": "byte[]", "必填": "否", "说明": "非ECB时生效，长度一般是算法块长"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "明文，不能为NULL或空字节数组"}], "输出": "加密结果", "异常": "SVSException", "其它说明": "注意： 不是所有的算法都支持全部模式， 除ECB、CBC模式外，其他模式只需要NoPadding GCM、CCM模式最后一个分组是校验码", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0078", "需求名称": "", "优先级": "", "接口功能": "使用指定索引的对称密钥实现单步对称解密运算， 支持算法：DES/3DES/AES/SM1/SM4/SM7 支持模式：ECB/CBC/CFB/OFB/CTR/GCM/CCM/XTS/FPE 支持补丁：NoPadding/PKCS5Padding/PKCS7Padding/", "接口定义": "public byte[] decryptByIndex( String algorithm， int keyIndex, byte[] aad, byte[] iv, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：算法/模式/补丁"}, {"参数名": "keyIndex", "类型": "int", "必填": "是", "说明": "对称密钥索引"}, {"参数名": "aad", "类型": "byte[]", "必填": "否", "说明": "GCM、CCM模式时使用的额外参数。长度受算法模式控制"}, {"参数名": "iv", "类型": "byte[]", "必填": "否", "说明": "非ECB时生效，长度一般是算法块长"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "密文，不能为NULL或空字节数组"}], "输出": "解密结果", "异常": "SVSException", "其它说明": "注意： 不是所有的算法都支持全部模式， 除ECB、CBC模式外，其他模式只需要NoPadding GCM、CCM模式传入的密文最后一个分组是校验码", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0079", "需求名称": "", "优先级": "", "接口功能": "使用指定名称的对称密钥实现单步对称解密运算， 支持算法：DES/3DES/AES/SM1/SM4/SM7 支持模式：ECB/CBC/CFB/OFB/CTR/GCM/CCM/XTS/FPE 支持补丁：NoPadding/PKCS5Padding/PKCS7Padding/", "接口定义": "public byte[] decryptByName( String algorithm， String name, byte[] aad, byte[] iv, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：算法/模式/补丁"}, {"参数名": "keyIndex", "类型": "int", "必填": "是", "说明": "对称密钥名称"}, {"参数名": "aad", "类型": "byte[]", "必填": "否", "说明": "GCM、CCM模式时使用的额外参数。长度受算法模式控制"}, {"参数名": "iv", "类型": "byte[]", "必填": "否", "说明": "非ECB时生效，长度一般是算法块长"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "密文，不能为NULL或空字节数组"}], "输出": "解密结果", "异常": "SVSException", "其它说明": "注意： 不是所有的算法都支持全部模式， 除ECB、CBC模式外，其他模式只需要NoPadding GCM、CCM模式传入的密文最后一个分组是校验码", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0080", "需求名称": "", "优先级": "", "接口功能": "使用外部明文对称密钥实现单步对称解密运算， 支持算法：DES/3DES/AES/SM1/SM4/SM7 支持模式：ECB/CBC/CFB/OFB/CTR/GCM/CCM/XTS/FPE 支持补丁：NoPadding/PKCS5Padding/PKCS7Padding/", "接口定义": "public byte[] decryptByKey( String algorithm， byte[] key, byte[] aad, byte[] iv, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：算法/模式/补丁"}, {"参数名": "key", "类型": "byte[]", "必填": "是", "说明": "对称密钥"}, {"参数名": "aad", "类型": "byte[]", "必填": "否", "说明": "GCM、CCM模式时使用的额外参数。长度受算法模式控制"}, {"参数名": "iv", "类型": "byte[]", "必填": "否", "说明": "非ECB时生效，长度一般是算法块长"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "密文，不能为NULL或空字节数组"}], "输出": "解密结果", "异常": "SVSException", "其它说明": "注意： 不是所有的算法都支持全部模式， 除ECB、CBC模式外，其他模式只需要NoPadding GCM、CCM模式传入的密文最后一个分组是校验码", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0081", "需求名称": "", "优先级": "", "接口功能": "使用外部密文对称密钥实现单步对称解密运算， 支持算法：DES/3DES/AES/SM1/SM4/SM7 支持模式：ECB/CBC/CFB/OFB/CTR/GCM/CCM/XTS/FPE 支持补丁：NoPadding/PKCS5Padding/PKCS7Padding/", "接口定义": "public byte[] decryptByEncryptedKey( String algorithm， byte[] key, byte[] aad, byte[] iv, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：算法/模式/补丁"}, {"参数名": "key", "类型": "byte[]", "必填": "是", "说明": "LMK加密的对称密钥"}, {"参数名": "aad", "类型": "byte[]", "必填": "否", "说明": "GCM、CCM模式时使用的额外参数。长度受算法模式控制"}, {"参数名": "iv", "类型": "byte[]", "必填": "否", "说明": "非ECB时生效，长度一般是算法块长"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "密文，不能为NULL或空字节数组"}], "输出": "解密结果", "异常": "SVSException", "其它说明": "注意： 不是所有的算法都支持全部模式， 除ECB、CBC模式外，其他模式只需要NoPadding GCM、CCM模式传入的密文最后一个分组是校验码", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0088", "需求名称": "", "优先级": "", "接口功能": "使用指定索引的公钥实现加密运算， 支持算法：RSA、SM2、ECDSA 支持补丁：NoPadding/PKCS1Padding/OAEPPadding", "接口定义": "public byte[] encryptByPublicKeyIndex( String algorithm， int type， int keyIndex, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：算法/补丁 补丁可省略，省略表示使用NoPadding"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "keyIndex", "类型": "int", "必填": "是", "说明": "公钥索引"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "明文，不能为NULL或空字节数组"}], "输出": "加密结果", "异常": "SVSException", "其它说明": "注意： SM2和ECDSA使用NoPadding", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0089", "需求名称": "", "优先级": "", "接口功能": "使用指定名称的公钥实现加密运算， 支持算法：RSA、SM2、ECDSA 支持补丁：NoPadding/PKCS1Padding/OAEPPadding", "接口定义": "public byte[] encryptByPublicKeyName ( String algorithm， int type， String name, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：算法/补丁 补丁可省略，省略表示使用NoPadding"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "name", "类型": "String", "必填": "是", "说明": "公钥名称"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "明文，不能为NULL或空字节数组"}], "输出": "加密结果", "异常": "SVSException", "其它说明": "注意： SM2和ECDSA使用NoPadding", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0091", "需求名称": "", "优先级": "", "接口功能": "使用指定索引的私钥实现解密操作， 支持算法：RSA、SM2、ECDSA 支持补丁：NoPadding/PKCS1Padding/OAEPPadding", "接口定义": "public byte[] decryptByPrivateKeyIndex( String algorithm， int type， int keyIndex, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：算法/补丁 补丁可省略，省略表示使用NoPadding"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "keyIndex", "类型": "int", "必填": "是", "说明": "私钥索引"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "密文，不能为NULL或空字节数组"}], "输出": "解密结果", "异常": "SVSException", "其它说明": "注意： SM2和ECDSA使用NoPadding", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0092", "需求名称": "", "优先级": "", "接口功能": "使用指定name的私钥实现解密操作， 支持算法：RSA、SM2、ECDSA 支持补丁：NoPadding/PKCS1Padding/OAEPPadding", "接口定义": "public byte[] decryptByPrivateKeyName( String algorithm， int type， String name, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：算法/补丁 补丁可省略，省略表示使用NoPadding"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "name", "类型": "String", "必填": "是", "说明": "私钥名称"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "密文，不能为NULL或空字节数组"}], "输出": "解密结果", "异常": "SVSException", "其它说明": "注意： SM2和ECDSA使用NoPadding", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0093", "需求名称": "", "优先级": "", "接口功能": "使用外部明文私钥实现解密操作， 支持算法：RSA、SM2、ECDSA 支持补丁：NoPadding/PKCS1Padding/OAEPPadding", "接口定义": "public byte[] decryptByPrivateKey( String algorithm， String privateKey, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：算法/补丁 补丁可省略，省略表示使用NoPadding"}, {"参数名": "privateKey", "类型": "String", "必填": "是", "说明": "Base64编码的私钥，支持PKCS#8、GMT0018格式的私钥。程序自动识别"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "密文，不能为NULL或空字节数组"}], "输出": "解密结果", "异常": "SVSException", "其它说明": "注意： SM2和ECDSA使用NoPadding", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0094", "需求名称": "", "优先级": "", "接口功能": "使用LMK加密的外部密文私钥实现解密操作， 支持算法：RSA、SM2、ECDSA 支持补丁：NoPadding/PKCS1Padding/OAEPPadding", "接口定义": "public byte[] decryptByEncryptedPrivateKey( String algorithm， String privateKey, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：算法/补丁 补丁可省略，省略表示使用NoPadding"}, {"参数名": "privateKey", "类型": "String", "必填": "是", "说明": "Base64编码LMK加密的私钥，支持PKCS#8、GMT0018格式的私钥。程序自动识别"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "密文，不能为NULL或空字节数组"}], "输出": "解密结果", "异常": "SVSException", "其它说明": "注意： SM2和ECDSA使用NoPadding", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0099", "需求名称": "", "优先级": "", "接口功能": "使用外部明文私钥对指定数据做摘要、打补丁、裸签，", "接口定义": "public String rawSignByPrivateKey( String algorithm， String privateKey, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SHA1WithRSA/PSS、 SHA256WithRSA/PSS、SHA224WithRSA/PSS、 SHA384WithRSA/PSS SHA512WithRSA/PSS NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "privateKey", "类型": "String", "必填": "是", "说明": "Base64编码的私钥，支持PKCS#8、GMT0018格式的私钥。程序自动识别"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "待签名数据。可以是原文或哈希值。根据algorithm确定。"}], "输出": "Base64编码的签名结果", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA1WithRSA/PSS", "SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "SHA512WithRSA/PSS", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SHA256WithRSA", "SM2", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "SHA256WithRSA/PSS", "NoneWithSM2", "SHA224WithRSA/PSS", "ED25519", "SHA384WithRSA", "SHA384WithRSA/PSS", "NoneWithRSA"]}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0100", "需求名称": "", "优先级": "", "接口功能": "使用外部明文私钥对指定数据做摘要、打补丁、裸签，", "接口定义": "public String rawSignByEncryptedPrivateKey( String algorithm， String privateKey, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SHA1WithRSA/PSS、 SHA256WithRSA/PSS、SHA224WithRSA/PSS、 SHA384WithRSA/PSS SHA512WithRSA/PSS NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "privateKey", "类型": "String", "必填": "是", "说明": "Base64编码LMK加密的私钥，支持PKCS#8、GMT0018格式的私钥。程序自动识别"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "待签名数据。可以是原文或哈希值。根据algorithm确定。"}], "输出": "Base64编码的签名结果", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA1WithRSA/PSS", "SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "SHA512WithRSA/PSS", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SHA256WithRSA", "SM2", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "SHA256WithRSA/PSS", "NoneWithSM2", "SHA224WithRSA/PSS", "ED25519", "SHA384WithRSA", "SHA384WithRSA/PSS", "NoneWithRSA"]}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0110", "需求名称": "", "优先级": "", "接口功能": "使用外部明文私钥对指定文件内容做摘要、打补丁、裸签，", "接口定义": "public String rawSignFileByPrivateKey( String algorithm， String privateKey, String filename ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SHA1WithRSA/PSS、 SHA256WithRSA/PSS、SHA224WithRSA/PSS、 SHA384WithRSA/PSS SHA512WithRSA/PSS NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "privateKey", "类型": "String", "必填": "是", "说明": "Base64编码的私钥，支持PKCS#8、GMT0018格式的私钥。程序自动识别"}, {"参数名": "filename", "类型": "String", "必填": "是", "说明": "待签名文件"}], "输出": "Base64编码的签名结果", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA1WithRSA/PSS", "SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "SHA512WithRSA/PSS", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SHA256WithRSA", "SM2", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "SHA256WithRSA/PSS", "NoneWithSM2", "SHA224WithRSA/PSS", "ED25519", "SHA384WithRSA", "SHA384WithRSA/PSS", "NoneWithRSA"]}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0111", "需求名称": "", "优先级": "", "接口功能": "使用外部明文私钥对指定文件内容做摘要、打补丁、裸签，", "接口定义": "public String rawSignFileByEncryptedPrivateKey( String algorithm， String privateKey, String filename ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SHA1WithRSA/PSS、 SHA256WithRSA/PSS、SHA224WithRSA/PSS、 SHA384WithRSA/PSS SHA512WithRSA/PSS NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "privateKey", "类型": "String", "必填": "是", "说明": "Base64编码LMK加密的私钥，支持PKCS#8、GMT0018格式的私钥。程序自动识别"}, {"参数名": "filename", "类型": "String", "必填": "是", "说明": "待签名文件"}], "输出": "Base64编码的签名结果", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA1WithRSA/PSS", "SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "SHA512WithRSA/PSS", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SHA256WithRSA", "SM2", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "SHA256WithRSA/PSS", "NoneWithSM2", "SHA224WithRSA/PSS", "ED25519", "SHA384WithRSA", "SHA384WithRSA/PSS", "NoneWithRSA"]}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0142", "需求名称": "", "优先级": "", "接口功能": "使用外部明文私钥解密数字信封", "接口定义": "public DecryptResult pkcs7DecryptEnvelopByPrivateKey( String privateKey, String envelop ) throws SVSException", "输入参数": [{"参数名": "privateKey", "类型": "String", "必填": "是", "说明": "Base64编码的私钥，支持PKCS#8、GMT0018"}, {"参数名": "envelop", "类型": "byte[]", "必填": "是", "说明": "Base64编码的数字信封。"}], "输出": "DecryptResult：解密结果，包括加密证书、明文", "异常": "SVSException", "其它说明": "注意：", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0143", "需求名称": "", "优先级": "", "接口功能": "使用外部密文私钥解密数字信封", "接口定义": "public DecryptResult pkcs7DecryptEnvelopByEncryptedPrivateKey( String privateKey, String envelop ) throws SVSException", "输入参数": [{"参数名": "privateKey", "类型": "String", "必填": "是", "说明": "Base64编码的LMK加密的私钥，支持PKCS#8"}, {"参数名": "envelop", "类型": "byte[]", "必填": "是", "说明": "Base64编码的数字信封。"}], "输出": "DecryptResult：解密结果，包括加密证书、明文", "异常": "SVSException", "其它说明": "注意：", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0154", "需求名称": "", "优先级": "", "接口功能": "使用外部明文私钥解密数字信封", "接口定义": "public DecryptResult pkcs7DecryptSignedEnvelopByPrivateKey( String privateKey, String envelop ) throws SVSException", "输入参数": [{"参数名": "privateKey", "类型": "String", "必填": "是", "说明": "Base64编码的私钥，支持PKCS#8、GMT0018"}, {"参数名": "envelop", "类型": "byte[]", "必填": "是", "说明": "Base64编码的数字信封。"}], "输出": "DecryptResult：解密结果，包括加密证书、签名证书、明文", "异常": "SVSException", "其它说明": "注意：如验证签名失败，则不返回解密结果", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0155", "需求名称": "", "优先级": "", "接口功能": "使用外部密文私钥解密数字信封", "接口定义": "public DecryptResult pkcs7DecryptSignedEnvelopByEncryptedPrivateKey( String privateKey, String envelop ) throws SVSException", "输入参数": [{"参数名": "privateKey", "类型": "String", "必填": "是", "说明": "Base64编码的LMK加密的私钥，支持PKCS#8、GMT0018"}, {"参数名": "envelop", "类型": "byte[]", "必填": "是", "说明": "Base64编码的数字信封。"}], "输出": "DecryptResult：解密结果，包括加密证书、签名证书、明文", "异常": "SVSException", "其它说明": "注意：如验证签名失败，则不返回解密结果", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0210", "需求名称": "", "优先级": "", "接口功能": "将Base64编码的GMT0018规范的私钥转换为Base64编码的PKCS#8私钥", "接口定义": "public String translatePriKeyGm2Der( String gmt0018PrivateKey String gmt0018PublicKey ) throws SVSException", "输入参数": [{"参数名": "gmt0018PrivateKey", "类型": "String", "必填": "是", "说明": "Base64编码的GMT0018规范的私钥"}, {"参数名": "gmt0018PublicKey", "类型": "String", "必填": "否", "说明": "Base64编码的GMT0018规范的公钥"}], "输出": "Base64编码的PKCS#8规范的私钥", "异常": "SVSException", "其它说明": "gmt0018PublicKey为空时，PKCS#8格式私钥里公钥部分省略 注意兼容32字节长度的公钥和私钥（省略了32字节0）", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0211", "需求名称": "", "优先级": "", "接口功能": "将Base64编码的PKCS#8规范的私钥转换为Base64编码的GMT0018私钥", "接口定义": "public String translatePriKeyDer2Gm(String pkcs8PrivateKey) throws SVSException", "输入参数": [{"参数名": "pkcs8PrivateKey", "类型": "String", "必填": "是", "说明": "Base64编码的PKCS#8规范的私钥"}], "输出": "Base64编码的GMT0018规范的私钥", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0212", "需求名称": "", "优先级": "", "接口功能": "将Base64编码的GMT0018规范的公钥转换为Base64编码的X509公钥", "接口定义": "public String translatePubKeyGm2Der(String gmt0018PublicKey) throws SVSException", "输入参数": [{"参数名": "gmt0018PublicKey", "类型": "String", "必填": "是", "说明": "Base64编码的GMT0018规范的公钥"}], "输出": "Base64编码的PKCS8规范的公钥", "异常": "SVSException", "其它说明": "注意兼容32字节长度的公钥（省略了32字节0）", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0213", "需求名称": "", "优先级": "", "接口功能": "将Base64编码的X509规范的公钥转换为Base64编码的GMT0018公钥", "接口定义": "public String translatePubKeyDer2Gm(String x509PublicKey) throws SVSException", "输入参数": [{"参数名": "x509PublicKey", "类型": "String", "必填": "是", "说明": "Base64编码的X509规范的公钥"}], "输出": "Base64编码的GMT0018规范的公钥", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0225", "需求名称": "", "优先级": "", "接口功能": "非对称密钥信息类", "接口定义": "public class AsymmKeyInfo { private int type； private int status； private int authCode； private int index; private String name; private String publicKey; private String algorithm; //.每个属性自带get属性 }", "输入参数": [], "输出": "无", "异常": "无", "其它说明": "", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0226", "需求名称": "", "优先级": "", "接口功能": "非对称密钥对", "接口定义": "public class AsymmKeyPair { private String publicKey; private String privateKey； private String algorithm; //.每个属性自带get属性 }", "输入参数": [], "输出": "无", "异常": "无", "其它说明": "", "支持算法": []}, {"章节": "密钥管理", "需求编号": "PR-F-SVS-0227", "需求名称": "", "优先级": "", "接口功能": "对称密钥信息类", "接口定义": "public class SymmKeyInfo{ private int status； private int index; private int bits； private String name; private String algorithm; private String kcv； //.每个属性自带get属性 }", "输入参数": [], "输出": "无", "异常": "无", "其它说明": "", "支持算法": []}], "密码运算": [{"章节": "密码运算", "需求编号": "PR-F-SVS-0056", "需求名称": "", "优先级": "", "接口功能": "多步哈希运算Update数据操作", "接口定义": "public byte[] hashUpdate( byte[] hashHandle, byte[] data, ) throws SVSException", "输入参数": [{"参数名": "hashH<PERSON>le", "类型": "byte[]", "必填": "是", "说明": "哈希运算中间数据，第一次取hashInit方法的返回值，之后都是上一次hashUpdate方法的返回值"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "明文，不能为NULL或空字符数组"}], "输出": "哈希句柄，里面存放了中间过程数据", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "密码运算", "需求编号": "PR-F-SVS-0057", "需求名称": "", "优先级": "", "接口功能": "多步哈希运算输出摘要结果", "接口定义": "public byte[] hashF<PERSON>( byte[] hashHandle, ) throws SVSException", "输入参数": [{"参数名": "hashH<PERSON>le", "类型": "byte[]", "必填": "是", "说明": "哈希运算中间数据，最后一次hashUpdate方法的返回值"}], "输出": "哈希结果", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "密码运算", "需求编号": "PR-F-SVS-0066", "需求名称": "", "优先级": "", "接口功能": "多步MAC运算Update数据操作", "接口定义": "public byte[] macUpdate( byte[] macHandle, byte[] data, ) throws SVSException", "输入参数": [{"参数名": "<PERSON><PERSON><PERSON><PERSON>", "类型": "byte[]", "必填": "是", "说明": "MAC运算中间数据，第一次取macInit方法的返回值，之后都是上一次macUpdate方法的返回值"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "明文，不能为NULL或空字符数组"}], "输出": "MAC句柄，里面存放了中间过程数据", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "密码运算", "需求编号": "PR-F-SVS-0067", "需求名称": "", "优先级": "", "接口功能": "多步MAC运算输出结果", "接口定义": "public byte[] mac<PERSON><PERSON>( byte[] mac<PERSON><PERSON><PERSON>, ) throws SVSException", "输入参数": [{"参数名": "<PERSON><PERSON><PERSON><PERSON>", "类型": "byte[]", "必填": "是", "说明": "MAC运算中间数据，最后一次macUpdate方法的返回值"}], "输出": "MAC结果", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "密码运算", "需求编号": "PR-F-SVS-0076", "需求名称": "", "优先级": "", "接口功能": "多步加密运算update数据，", "接口定义": "public byte[][] encryptUpdate( byte[] handle， byte[] data ) throws SVSException", "输入参数": [{"参数名": "handle", "类型": "byte[]", "必填": "是", "说明": "加密句柄，第一次使用多步加密Init方法返回的句柄，以后都是前一次Update方法返回的第一个byte数组"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "明文，不能为NULL或空字节数组"}], "输出": "2维byte数组，byte[0][]对应的数组是handle，下次update时使用。 byte[1][]是密文数据，可能为null", "异常": "SVSException", "其它说明": "程序会根据当前明文长度决定是否加密，也就是说可能返回明文，也可能不返回", "支持算法": []}, {"章节": "密码运算", "需求编号": "PR-F-SVS-0077", "需求名称": "", "优先级": "", "接口功能": "多步加密运算Final", "接口定义": "public byte[] encryptFinal( byte[] handle ) throws SVSException", "输入参数": [{"参数名": "handle", "类型": "byte[]", "必填": "是", "说明": "加密句柄，最后一次调用Update方法返回的第一个byte数组"}], "输出": "密文数据", "异常": "SVSException", "其它说明": "所有update方法返回的密文拼接本方法返回的密文才是最后的密文 GCM、CCM模式最后一个分组是校验码", "支持算法": []}, {"章节": "密码运算", "需求编号": "PR-F-SVS-0086", "需求名称": "", "优先级": "", "接口功能": "多步解密运算update数据", "接口定义": "public byte[][] encryptUpdate( byte[] handle， byte[] data ) throws SVSException", "输入参数": [{"参数名": "handle", "类型": "byte[]", "必填": "是", "说明": "解密句柄，第一次使用多步解密Init方法返回的句柄，以后都是前一次Update方法返回的第一个byte数组"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "密文，不能为NULL或空字节数组"}], "输出": "2维byte数组，byte[0][]对应的数组是handle，下次update时使用。 byte[1][]是明文数据，可能为null", "异常": "SVSException", "其它说明": "程序会根据当前明文长度决定是否解密，也就是说可能返回明文，也可能不返回", "支持算法": []}, {"章节": "密码运算", "需求编号": "PR-F-SVS-0087", "需求名称": "", "优先级": "", "接口功能": "多步解密运算Final", "接口定义": "public byte[] decryptFinal( byte[] handle ) throws SVSException", "输入参数": [{"参数名": "handle", "类型": "byte[]", "必填": "是", "说明": "解密句柄，最后一次调用Update方法返回的第一个byte数组"}], "输出": "明文数据", "异常": "SVSException", "其它说明": "所有update方法返回的密文拼接本方法返回的密文才是最后的密文 GCM、CCM模式最后一个分组是校验码", "支持算法": []}, {"章节": "密码运算", "需求编号": "PR-F-SVS-0138", "需求名称": "", "优先级": "", "接口功能": "使用内部指定索引的私钥解密数字信封", "接口定义": "public DecryptResult pkcs7DecryptEnvelopByIndex( int type， int index, String envelop ) throws SVSException", "输入参数": [{"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "index", "类型": "int", "必填": "是", "说明": "私钥索引"}, {"参数名": "envelop", "类型": "byte[]", "必填": "是", "说明": "Base64编码的数字信封。"}], "输出": "DecryptResult：解密结果，包括加密证书、明文", "异常": "SVSException", "其它说明": "注意：", "支持算法": []}, {"章节": "密码运算", "需求编号": "PR-F-SVS-0139", "需求名称": "", "优先级": "", "接口功能": "使用内部指定名称的私钥解密数字信封", "接口定义": "public DecryptResult pkcs7DecryptEnvelopByName( int type， String name, String envelop ) throws SVSException", "输入参数": [{"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "name", "类型": "String", "必填": "是", "说明": "私钥名称"}, {"参数名": "envelop", "类型": "byte[]", "必填": "是", "说明": "Base64编码的数字信封。"}], "输出": "DecryptResult：解密结果，包括加密证书、明文", "异常": "SVSException", "其它说明": "注意：", "支持算法": []}, {"章节": "密码运算", "需求编号": "PR-F-SVS-0150", "需求名称": "", "优先级": "", "接口功能": "使用内部指定索引的私钥解密数字信封", "接口定义": "public DecryptResult pkcs7DecryptSignedEnvelopByIndex( int type， int index, String envelop ) throws SVSException", "输入参数": [{"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "index", "类型": "int", "必填": "是", "说明": "私钥索引"}, {"参数名": "envelop", "类型": "byte[]", "必填": "是", "说明": "Base64编码的数字信封。"}], "输出": "DecryptResult：解密结果，包括加密证书、签名证书、明文", "异常": "SVSException", "其它说明": "注意：如验证签名失败，则不返回解密结果", "支持算法": []}, {"章节": "密码运算", "需求编号": "PR-F-SVS-0151", "需求名称": "", "优先级": "", "接口功能": "使用内部指定名称的私钥解密数字信封", "接口定义": "public DecryptResult pkcs7DecryptSignedEnvelopByName( int type， String name, String envelop ) throws SVSException", "输入参数": [{"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "name", "类型": "String", "必填": "是", "说明": "私钥名称"}, {"参数名": "envelop", "类型": "byte[]", "必填": "是", "说明": "Base64编码的数字信封。"}], "输出": "DecryptResult：解密结果，包括加密证书、签名证书、明文", "异常": "SVSException", "其它说明": "注意：如验证签名失败，则不返回解密结果", "支持算法": []}, {"章节": "密码运算", "需求编号": "PR-F-SVS-0207", "需求名称": "", "优先级": "", "接口功能": "获取业务的TPS和平均响应时间(RT)信息，包括裸签名，验证裸签名，消息签名，验证消息签名，制作数字信封，解数字信封，加密，解密，PDF签名，PDF验签，XML签名，XML验签，时间戳生成，时间戳验证 制作OFD签章，核验OFD签章，其他等业务", "接口定义": "public TPSInfo getBusinessMonitor( ) throws SVSException", "输入参数": [], "输出": "TPSInfo：业务性能信息", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "密码运算", "需求编号": "PR-F-SVS-0235", "需求名称": "", "优先级": "", "接口功能": "条形码参数", "接口定义": "public class TPSInfo { private TPS sign;//裸签名 private TPS verify;//验证裸签名 private TPS encryptEnvelop;//制作数字信封 private TPS decryptEnvelop;//解数字信封 private TPS encrypt;//加密 private TPS decrypt;//解密 private TPS pdfSign;//PDF签名 private TPS verifyPdfSign;//PDF验签 private TPS xmlSign;//XML签名 private TPS verifyXmlSign;//XML验签 private TPS p7Sign;//消息签名 private TPS p7verify;//验证消息签名 private TPS tspGen;//时间戳生成 private TPS tspVerify;//时间戳验证 private TPS makeSeal;//制作电子印章 private TPS verifySeal;//验证电子印章 private TPS makeESign;//制作电子签章 private TPS verifyESign;//核验电子签章 private TPS ofdSign;//制作OFD签章 private TPS verifyOfdSign;//核验OFD签章 private TPS other;//其他。 //.每个属性自带get/set属性 } private class TPS{ private String rt;// 平均响应时间(毫秒/每笔)，带单位ms private String tps;// 业务TPS(数量/每秒)，不带单位 }", "输入参数": [], "输出": "", "异常": "", "其它说明": "", "支持算法": []}], "非对称算法": [], "消息签名验签": [{"章节": "消息签名验签", "需求编号": "PR-F-SVS-0095", "需求名称": "", "优先级": "", "接口功能": "使用指定索引的内部私钥对指定数据做摘要、打补丁、裸签，", "接口定义": "public String rawSignByIndex( String algorithm， int type， int index, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SHA1WithRSA/PSS、 SHA256WithRSA/PSS、SHA224WithRSA/PSS、 SHA384WithRSA/PSS SHA512WithRSA/PSS NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "index", "类型": "int", "必填": "是", "说明": "私钥索引"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "待签名数据。可以是原文或哈希值。根据algorithm确定。"}], "输出": "Base64编码的签名结果", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA1WithRSA/PSS", "SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "SHA512WithRSA/PSS", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SHA256WithRSA", "SM2", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "SHA256WithRSA/PSS", "NoneWithSM2", "SHA224WithRSA/PSS", "ED25519", "SHA384WithRSA", "SHA384WithRSA/PSS", "NoneWithRSA"]}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0096", "需求名称": "", "优先级": "", "接口功能": "使用指定名称的内部私钥对指定数据做摘要、打补丁、裸签，", "接口定义": "public String rawSignByName( String algorithm， int type， String name, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SHA1WithRSA/PSS、 SHA256WithRSA/PSS、SHA224WithRSA/PSS、 SHA384WithRSA/PSS SHA512WithRSA/PSS NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "name", "类型": "String", "必填": "是", "说明": "私钥名称"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "待签名数据。可以是原文或哈希值。根据algorithm确定。"}], "输出": "Base64编码的签名结果", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA1WithRSA/PSS", "SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "SHA512WithRSA/PSS", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SHA256WithRSA", "SM2", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "SHA256WithRSA/PSS", "NoneWithSM2", "SHA224WithRSA/PSS", "ED25519", "SHA384WithRSA", "SHA384WithRSA/PSS", "NoneWithRSA"]}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0101", "需求名称": "", "优先级": "", "接口功能": "使用指定索引的内部公钥验证签名有效性", "接口定义": "public boolean rawVerifyByIndex( String algorithm， int type， int index, byte[] data， String signature ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SHA1WithRSA/PSS、 SHA256WithRSA/PSS、SHA224WithRSA/PSS、 SHA384WithRSA/PSS SHA512WithRSA/PSS NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "index", "类型": "int", "必填": "是", "说明": "公钥索引"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "待签名数据。可以是原文或哈希值。根据algorithm确定。"}, {"参数名": "signature", "类型": "String", "必填": "是", "说明": "Base64编码的签名结果"}], "输出": "验签结果", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA1WithRSA/PSS", "SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "SHA512WithRSA/PSS", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SHA256WithRSA", "SM2", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "SHA256WithRSA/PSS", "NoneWithSM2", "SHA224WithRSA/PSS", "ED25519", "SHA384WithRSA", "SHA384WithRSA/PSS", "NoneWithRSA"]}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0102", "需求名称": "", "优先级": "", "接口功能": "使用指定名称的内部公钥验证签名有效性", "接口定义": "public boolean rawVerifyByName( String algorithm， int type， String name, byte[] data， String signature ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SHA1WithRSA/PSS、 SHA256WithRSA/PSS、SHA224WithRSA/PSS、 SHA384WithRSA/PSS SHA512WithRSA/PSS NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "name", "类型": "String", "必填": "是", "说明": "公钥名称"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "待签名数据。可以是原文或哈希值。根据algorithm确定。"}, {"参数名": "signature", "类型": "String", "必填": "是", "说明": "Base64编码的签名结果"}], "输出": "验签结果", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA1WithRSA/PSS", "SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "SHA512WithRSA/PSS", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SHA256WithRSA", "SM2", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "SHA256WithRSA/PSS", "NoneWithSM2", "SHA224WithRSA/PSS", "ED25519", "SHA384WithRSA", "SHA384WithRSA/PSS", "NoneWithRSA"]}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0106", "需求名称": "", "优先级": "", "接口功能": "使用指定索引的内部私钥对指定文件内容做摘要、打补丁、裸签，", "接口定义": "public String rawSignFileByIndex( String algorithm， int type， int index, String filename ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SHA1WithRSA/PSS、 SHA256WithRSA/PSS、SHA224WithRSA/PSS、 SHA384WithRSA/PSS SHA512WithRSA/PSS NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "index", "类型": "int", "必填": "是", "说明": "私钥索引"}, {"参数名": "filename", "类型": "String", "必填": "是", "说明": "待签名文件"}], "输出": "Base64编码的签名结果", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值 读取文件时按照文件自身编码读取 密钥算法要与签名算法保持一致", "支持算法": ["SHA1WithRSA/PSS", "SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "SHA512WithRSA/PSS", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SHA256WithRSA", "SM2", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "SHA256WithRSA/PSS", "NoneWithSM2", "SHA224WithRSA/PSS", "ED25519", "SHA384WithRSA", "SHA384WithRSA/PSS", "NoneWithRSA"]}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0107", "需求名称": "", "优先级": "", "接口功能": "使用指定name的内部私钥对指定文件内容做摘要、打补丁、裸签，", "接口定义": "public String rawSignFileByName( String algorithm， int type， String name, String filename ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SHA1WithRSA/PSS、 SHA256WithRSA/PSS、SHA224WithRSA/PSS、 SHA384WithRSA/PSS SHA512WithRSA/PSS NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "name", "类型": "String", "必填": "是", "说明": "私钥名称"}, {"参数名": "filename", "类型": "String", "必填": "是", "说明": "待签名文件"}], "输出": "Base64编码的签名结果", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA1WithRSA/PSS", "SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "SHA512WithRSA/PSS", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SHA256WithRSA", "SM2", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "SHA256WithRSA/PSS", "NoneWithSM2", "SHA224WithRSA/PSS", "ED25519", "SHA384WithRSA", "SHA384WithRSA/PSS", "NoneWithRSA"]}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0112", "需求名称": "", "优先级": "", "接口功能": "使用指定索引的内部公钥验证签名有效性", "接口定义": "public boolean rawVerifyFileByIndex( String algorithm， int type， int index, String filename String signature ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SHA1WithRSA/PSS、 SHA256WithRSA/PSS、SHA224WithRSA/PSS、 SHA384WithRSA/PSS SHA512WithRSA/PSS NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "index", "类型": "int", "必填": "是", "说明": "公钥索引"}, {"参数名": "filename", "类型": "String", "必填": "是", "说明": "待签名文件"}, {"参数名": "signature", "类型": "String", "必填": "是", "说明": "Base64编码的签名结果"}], "输出": "验签结果", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA1WithRSA/PSS", "SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "SHA512WithRSA/PSS", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SHA256WithRSA", "SM2", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "SHA256WithRSA/PSS", "NoneWithSM2", "SHA224WithRSA/PSS", "ED25519", "SHA384WithRSA", "SHA384WithRSA/PSS", "NoneWithRSA"]}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0113", "需求名称": "", "优先级": "", "接口功能": "使用指定名称的内部公钥验证签名有效性", "接口定义": "public boolean rawVerifyFileByName( String algorithm， int type， String name, String filename String signature ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SHA1WithRSA/PSS、 SHA256WithRSA/PSS、SHA224WithRSA/PSS、 SHA384WithRSA/PSS SHA512WithRSA/PSS NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "name", "类型": "String", "必填": "是", "说明": "公钥名称"}, {"参数名": "filename", "类型": "String", "必填": "是", "说明": "待签名文件"}, {"参数名": "signature", "类型": "String", "必填": "是", "说明": "Base64编码的签名结果"}], "输出": "验签结果", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA1WithRSA/PSS", "SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "SHA512WithRSA/PSS", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SHA256WithRSA", "SM2", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "SHA256WithRSA/PSS", "NoneWithSM2", "SHA224WithRSA/PSS", "ED25519", "SHA384WithRSA", "SHA384WithRSA/PSS", "NoneWithRSA"]}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0117", "需求名称": "", "优先级": "", "接口功能": "使用指定索引的内部私钥签发带原文的PKCS#7签名数据", "接口定义": "public String pkcs7AttachSignByIndex( String algorithm， int type， int index, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "index", "类型": "int", "必填": "是", "说明": "私钥索引"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "待签名数据。"}], "输出": "Base64编码的带原文的PKCS#7签名", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "ED25519", "SHA384WithRSA"]}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0118", "需求名称": "", "优先级": "", "接口功能": "使用指定名称的内部私钥签发带原文的PKCS#7签名数据", "接口定义": "public String pkcs7AttachSignByName( String algorithm， int type， String name, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "name", "类型": "String", "必填": "是", "说明": "私钥名称"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "待签名数据。"}], "输出": "Base64编码的带原文的PKCS#7签名", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "ED25519", "SHA384WithRSA"]}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0125", "需求名称": "", "优先级": "", "接口功能": "使用指定索引的内部私钥签发不带原文的PKCS#7签名数据", "接口定义": "public String pkcs7DetachSignByIndex( String algorithm， int type， int index, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "index", "类型": "int", "必填": "是", "说明": "私钥索引"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "待签名数据。"}], "输出": "Base64编码的不带原文的PKCS#7签名", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "ED25519", "SHA384WithRSA"]}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0126", "需求名称": "", "优先级": "", "接口功能": "使用指定名称的内部私钥签发不带原文的PKCS#7签名数据", "接口定义": "public String pkcs7DetachSignByName( String algorithm， int type， String name, byte[] data ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "name", "类型": "String", "必填": "是", "说明": "私钥名称"}, {"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "待签名数据。"}], "输出": "Base64编码的不带原文的PKCS#7签名", "异常": "SVSException", "其它说明": "注意： NoneWith***表示不做摘要，传入的是摘要值", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "ED25519", "SHA384WithRSA"]}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0156", "需求名称": "", "优先级": "", "接口功能": "使用指定索引的内部私钥对XMl封皮签名", "接口定义": "public byte[] xmlSignEnvelopedByIndex( String algorithm, int type， int index byte[] xmlData ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "index", "类型": "int", "必填": "是", "说明": "私钥索引"}, {"参数名": "xmlData", "类型": "byte[]", "必填": "是", "说明": "XML数据。"}], "输出": "签名后的XML数据", "异常": "SVSException", "其它说明": "", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "NoneWithSM2", "ED25519", "SHA384WithRSA", "NoneWithRSA"]}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0157", "需求名称": "", "优先级": "", "接口功能": "使用指定名称的内部私钥对XMl封皮签名", "接口定义": "public byte[] xmlSignEnvelopedByName( String algorithm, int type， String name, byte[] xmlData ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "index", "类型": "String", "必填": "是", "说明": "私钥名称"}, {"参数名": "xmlData", "类型": "byte[]", "必填": "是", "说明": "XML数据。"}], "输出": "签名后的XML数据", "异常": "SVSException", "其它说明": "", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "NoneWithSM2", "ED25519", "SHA384WithRSA", "NoneWithRSA"]}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0159", "需求名称": "", "优先级": "", "接口功能": "使用内部私钥对XMl封皮签名", "接口定义": "public byte[] xmlSignEnvelopedByDN( String algorithm, int type， String subjectDN, byte[] xmlData ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "subjectDN", "类型": "String", "必填": "是", "说明": "证书主题"}, {"参数名": "xmlData", "类型": "byte[]", "必填": "是", "说明": "XML数据。"}], "输出": "签名后的XML数据", "异常": "SVSException", "其它说明": "", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "NoneWithSM2", "ED25519", "SHA384WithRSA", "NoneWithRSA"]}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0162", "需求名称": "", "优先级": "", "接口功能": "使用指定索引的内部私钥对XMl封内签名", "接口定义": "public byte[] xmlSignEnvelopingByIndex( String algorithm, int type， int index byte[] xmlData ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "index", "类型": "int", "必填": "是", "说明": "私钥索引"}, {"参数名": "xmlData", "类型": "byte[]", "必填": "是", "说明": "XML数据。"}], "输出": "签名后的XML数据", "异常": "SVSException", "其它说明": "", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "NoneWithSM2", "ED25519", "SHA384WithRSA", "NoneWithRSA"]}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0163", "需求名称": "", "优先级": "", "接口功能": "使用指定名称的内部私钥对XMl封内签名", "接口定义": "public byte[] xmlSignEnvelopingByName( String algorithm, int type， String name, byte[] xmlData ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "index", "类型": "String", "必填": "是", "说明": "私钥名称"}, {"参数名": "xmlData", "类型": "byte[]", "必填": "是", "说明": "XML数据。"}], "输出": "签名后的XML数据", "异常": "SVSException", "其它说明": "", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "NoneWithSM2", "ED25519", "SHA384WithRSA", "NoneWithRSA"]}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0165", "需求名称": "", "优先级": "", "接口功能": "使用内部私钥对XMl封皮签名", "接口定义": "public byte[] xmlSignEnvelopingByDN( String algorithm, int type， String subjectDN, byte[] xmlData ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "subjectDN", "类型": "String", "必填": "是", "说明": "证书主题"}, {"参数名": "xmlData", "类型": "byte[]", "必填": "是", "说明": "XML数据。"}], "输出": "签名后的XML数据", "异常": "SVSException", "其它说明": "", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "NoneWithSM2", "ED25519", "SHA384WithRSA", "NoneWithRSA"]}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0168", "需求名称": "", "优先级": "", "接口功能": "使用指定索引的内部私钥对XMl分离签名", "接口定义": "public byte[] xmlSignDetachedByIndex( String algorithm, int type， int index String uri, ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "index", "类型": "int", "必填": "是", "说明": "私钥索引"}, {"参数名": "uri", "类型": "String", "必填": "是", "说明": "XML存储位置"}], "输出": "签名后的XML数据", "异常": "SVSException", "其它说明": "", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "NoneWithSM2", "ED25519", "SHA384WithRSA", "NoneWithRSA"]}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0169", "需求名称": "", "优先级": "", "接口功能": "使用指定名称的内部私钥对XMl分离签名", "接口定义": "public byte[] xmlSignDetachedByName( String algorithm, int type， String name, String uri, ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "name", "类型": "String", "必填": "是", "说明": "私钥名称"}, {"参数名": "uri", "类型": "String", "必填": "是", "说明": "XML存储位置"}], "输出": "签名后的XML数据", "异常": "SVSException", "其它说明": "", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "NoneWithSM2", "ED25519", "SHA384WithRSA", "NoneWithRSA"]}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0171", "需求名称": "", "优先级": "", "接口功能": "使用内部私钥对XMl分离签名", "接口定义": "public byte[] xmlSignDetachedByDN( String algorithm, int type， String subjectDN, String uri, ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "subjectDN", "类型": "String", "必填": "是", "说明": "证书主题"}, {"参数名": "uri", "类型": "String", "必填": "是", "说明": "XML存储位置"}], "输出": "签名后的XML数据", "异常": "SVSException", "其它说明": "", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "NoneWithSM2", "ED25519", "SHA384WithRSA", "NoneWithRSA"]}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0176", "需求名称": "", "优先级": "", "接口功能": "使用指定索引的内部私钥对PDF签名", "接口定义": "public byte[] pdfSignByIndex( String algorithm, int type， int index PDFSignParameter pdfParameter ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "index", "类型": "int", "必填": "是", "说明": "私钥索引"}, {"参数名": "pdfParameter", "类型": "PDFSignParameter", "必填": "是", "说明": "PDF签名参数"}], "输出": "签名后的PDF数据", "异常": "SVSException", "其它说明": "注意：普通签章与骑缝签章参数区别", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "NoneWithSM2", "ED25519", "SHA384WithRSA", "NoneWithRSA"]}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0177", "需求名称": "", "优先级": "", "接口功能": "使用指定名称的内部私钥对PDF签名", "接口定义": "public byte[]pdfSignByName( String algorithm, int type， String name， PDFSignParameter pdfParameter ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "name", "类型": "String", "必填": "是", "说明": "私钥名称"}, {"参数名": "pdfParameter", "类型": "PDFSignParameter", "必填": "是", "说明": "PDF签名参数"}], "输出": "签名后的PDF数据", "异常": "SVSException", "其它说明": "注意：普通签章与骑缝签章参数区别", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "NoneWithSM2", "ED25519", "SHA384WithRSA", "NoneWithRSA"]}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0182", "需求名称": "", "优先级": "", "接口功能": "PDF添加图片", "接口定义": "public byte[]pdfAddImage ( PDFSignParameter pdfParameter ) throws SVSException", "输入参数": [{"参数名": "pdfParameter", "类型": "PDFSignParameter", "必填": "是", "说明": "PDF参数"}], "输出": "添加图片后的PDF数据", "异常": "SVSException", "其它说明": "PDFSignParameter中应对以下参数赋值： pdf pdfPassword image imageWidth imageHeight x y pageNumber", "支持算法": []}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0183", "需求名称": "", "优先级": "", "接口功能": "PDF添加骑缝图片", "接口定义": "public byte[]pdfAddAcrossImage ( PDFSignParameter pdfParameter ) throws SVSException", "输入参数": [{"参数名": "pdfParameter", "类型": "PDFSignParameter", "必填": "是", "说明": "PDF参数"}], "输出": "添加图片后的PDF数据", "异常": "SVSException", "其它说明": "PDFSignParameter中应对以下参数赋值： pdf pdfPassword image imageWidth imageHeight", "支持算法": []}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0186", "需求名称": "", "优先级": "", "接口功能": "使用指定索引的内部私钥对OFD签名", "接口定义": "public byte[] ofdSignByIndex( String algorithm, int type， int index OFDSignParameter ofdParameter ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "index", "类型": "int", "必填": "是", "说明": "私钥索引"}, {"参数名": "ofdParameter", "类型": "OFDSignParameter", "必填": "是", "说明": "OFD签名参数"}], "输出": "签名后的OFD数据", "异常": "SVSException", "其它说明": "注意：普通签章与骑缝签章参数区别", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "NoneWithSM2", "ED25519", "SHA384WithRSA", "NoneWithRSA"]}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0187", "需求名称": "", "优先级": "", "接口功能": "使用指定名称的内部私钥对OFD签名", "接口定义": "public byte[]ofdSignByName( String algorithm, int type， String name， OFDSignParameter ofdParameter ) throws SVSException", "输入参数": [{"参数名": "algorithm", "类型": "String", "必填": "是", "说明": "算法类型，格式：摘要算法With签名算法/补丁方式 补丁可省略， RSA默认使用PKCS1Padding SM2和ECDSA没有补丁 支持的算法包括 NoneWithRSA、 SHA1WithRSA、 SHA256WithRSA、SHA224WithRSA、 SHA384WithRSA SHA512WithRSA NoneWithSM2 SM3WithSM2 NoneWithECDSA SHA1WithECDSA SHA256WithECDSA SHA224WithECDSA SHA384WithECDSA SHA512WithECDSA ED25519 ED448"}, {"参数名": "type", "类型": "int", "必填": "是", "说明": "密钥类型，取值范围： 1：签名密钥对 2：加密密钥对"}, {"参数名": "name", "类型": "String", "必填": "是", "说明": "私钥名称"}, {"参数名": "ofdParameter", "类型": "OFDSignParameter", "必填": "是", "说明": "OFD签名参数"}], "输出": "签名后的OFD数据", "异常": "SVSException", "其它说明": "注意：普通签章与骑缝签章参数区别", "支持算法": ["SHA512WithRSA", "SHA1WithRSA", "SHA224WithECDSA", "SHA224", "SHA1WithECDSA", "SHA224WithRSA", "SHA256", "SHA384", "NoneWithECDSA", "SHA512", "SHA512WithECDSA", "SHA1", "SM3", "SM3WithSM2", "SM2", "SHA256WithRSA", "SHA256WithECDSA", "SHA384WithECDSA", "ED448", "NoneWithSM2", "ED25519", "SHA384WithRSA", "NoneWithRSA"]}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0214", "需求名称": "", "优先级": "", "接口功能": "将Base64编码的GMT0018规范的SM2签名转换为Base64编码的GMT0009签名", "接口定义": "public String translateSignatureGm2Der(String gmt0018Signature) throws SVSException", "输入参数": [{"参数名": "gmt0018Signature", "类型": "String", "必填": "是", "说明": "Base64编码的GMT0018规范的SM2签名"}], "输出": "Base64编码的GMT0009规范的签名", "异常": "SVSException", "其它说明": "注意：兼容R和S为32字节情况", "支持算法": []}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0215", "需求名称": "", "优先级": "", "接口功能": "将Base64编码的GMT0009规范的SM2签名转换为Base64编码的GMT0018签名", "接口定义": "public String translateSignatureDer2Gm(String gmt0009Signature) throws SVSException", "输入参数": [{"参数名": "gmt0009Signature", "类型": "String", "必填": "是", "说明": "Base64编码的GMT0009规范的SM2签名"}], "输出": "Base64编码的GMT0018规范的签名", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0230", "需求名称": "", "优先级": "", "接口功能": "PDF签章参数", "接口定义": "public class PDFSignParameter { private byte[] pdf； private String pdfPassword； private byte[] image; private boolean riding； private String reason； private String location; private String signField; private int x; private int y; private int x2; private int y2; private int pageNumber; //.每个属性自带get/set属性 }", "输入参数": [], "输出": "无", "异常": "无", "其它说明": "", "支持算法": []}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0231", "需求名称": "", "优先级": "", "接口功能": "OFD签章参数", "接口定义": "public class OFDSignParameter { private byte[] ofd； private int x; private int y; private int imageWidth; private int imageHeight; private int pageNumber; private boolean riding； private String side; //.每个属性自带get/set属性 }", "输入参数": [], "输出": "无", "异常": "无", "其它说明": "非骑缝章时，x、y、imageWidth、imageHeight、pageNumber要赋值 骑缝章时，side必须赋值", "支持算法": []}, {"章节": "消息签名验签", "需求编号": "PR-F-SVS-0232", "需求名称": "", "优先级": "", "接口功能": "条形码参数", "接口定义": "public class PDFSignParameter { private double barHeight; private double moduleWidth; private boolean doQuietZone; private double quietZoneWidth; private boolean displayHumanReadable; private int imageFormat； //.每个属性自带get/set属性 }", "输入参数": [], "输出": "无", "异常": "无", "其它说明": "PDF是否需要支持国标的电子签章？", "支持算法": []}], "PKCS7操作": [], "数字信封": [], "XML操作": [], "PDF操作": [], "OFD操作": [], "条形码": [{"章节": "条形码", "需求编号": "PR-F-SVS-0194", "需求名称": "", "优先级": "", "接口功能": "生成39条码", "接口定义": "public byte[] generateBarCode39 ( String msg, BarCodeParameter parameter, boolean addCheckSUM, float wideFactor, boolean displayStartStop ) throws SVSException", "输入参数": [{"参数名": "msg", "类型": "String", "必填": "是", "说明": "条码信息，包括0-9，A-Z，空格、$、/、+、%、-、."}, {"参数名": "parameter", "类型": "BarCodeParameter", "必填": "是", "说明": "条码参数"}, {"参数名": "addCheckSUM", "类型": "boolean", "必填": "是", "说明": "是否添加和值校验位"}, {"参数名": "wideFactor", "类型": "float", "必填": "是", "说明": "设置宽条的宽度是窄条的几倍，必须大于1.0"}, {"参数名": "displayStartStop", "类型": "boolean", "必填": "是", "说明": "是否显示人工阅读字符的起始和终止符"}], "输出": "39条码", "异常": "无", "其它说明": "", "支持算法": []}, {"章节": "条形码", "需求编号": "PR-F-SVS-0195", "需求名称": "", "优先级": "", "接口功能": "生成128条码", "接口定义": "public byte[] generateBarCode128( String msg, BarCodeParameter parameter ) throws SVSException", "输入参数": [{"参数名": "msg", "类型": "String", "必填": "是", "说明": "条码信息，包括0-9，A-Z，空格、$、/、+、%、-、."}, {"参数名": "parameter", "类型": "BarCodeParameter", "必填": "是", "说明": "条码参数"}], "输出": "128条码", "异常": "无", "其它说明": "", "支持算法": []}, {"章节": "条形码", "需求编号": "PR-F-SVS-0196", "需求名称": "", "优先级": "", "接口功能": "生成交叉25条码", "接口定义": "public byte[] generateBarCodeInter25 ( String msg, BarCodeParameter parameter, boolean addCheckSUM, float wideFactor ) throws SVSException", "输入参数": [{"参数名": "msg", "类型": "String", "必填": "是", "说明": "条码信息，包括0-9，A-Z，空格、$、/、+、%、-、."}, {"参数名": "parameter", "类型": "BarCodeParameter", "必填": "是", "说明": "条码参数"}, {"参数名": "addCheckSUM", "类型": "boolean", "必填": "是", "说明": "是否添加和值校验位"}, {"参数名": "wideFactor", "类型": "float", "必填": "是", "说明": "设置宽条的宽度是窄条的几倍，必须大于1.0"}], "输出": "交叉25条码", "异常": "无", "其它说明": "", "支持算法": []}, {"章节": "条形码", "需求编号": "PR-F-SVS-0197", "需求名称": "", "优先级": "", "接口功能": "生成库德巴条码", "接口定义": "public byte[] generateBarCodeCodabar ( String msg, BarCodeParameter parameter, float wideFactor, boolean displayStartStop ) throws SVSException", "输入参数": [{"参数名": "msg", "类型": "String", "必填": "是", "说明": "条码信息，包括0-9，A-Z，空格、$、/、+、%、-、."}, {"参数名": "parameter", "类型": "BarCodeParameter", "必填": "是", "说明": "条码参数"}, {"参数名": "wideFactor", "类型": "float", "必填": "是", "说明": "设置宽条的宽度是窄条的几倍，必须大于1.0"}, {"参数名": "displayStartStop", "类型": "boolean", "必填": "是", "说明": "是否显示人工阅读字符的起始和终止符"}], "输出": "库德巴条码", "异常": "无", "其它说明": "", "支持算法": []}, {"章节": "条形码", "需求编号": "PR-F-SVS-0198", "需求名称": "", "优先级": "", "接口功能": "生成417条码", "接口定义": "public byte[] generateBarCode417( byte[] msg, boolean isBinary int errorCorrectLevel, float aspectRatio, float yHeight, int returnFormat ) throws SVSException", "输入参数": [{"参数名": "msg", "类型": "byte[]", "必填": "是", "说明": "字符，可以是任何数据"}, {"参数名": "isBinary", "类型": "boolean", "必填": "是", "说明": "字符是否是二进制数据"}, {"参数名": "errorCorrectLevel", "类型": "int", "必填": "是", "说明": "设置纠错等级，0-9，对应产生2到512个纠错码词"}, {"参数名": "aspectRatio", "类型": "float", "必填": "是", "说明": "设置长度比高度的值，输入0时默认为0.5，表示宽是高的2倍"}, {"参数名": "yHeight", "类型": "float", "必填": "是", "说明": "设置每个单元的长宽比，输入0时默认为3"}, {"参数名": "returnFormat", "类型": "int", "必填": "是", "说明": "设置生成的图片格式，0：PNG，1：JPEG，2：GIF"}], "输出": "417条码", "异常": "无", "其它说明": "", "支持算法": []}], "时间戳": [], "电子签章": [], "监控": [{"章节": "监控", "需求编号": "PR-F-SVS-", "需求名称": "", "优先级": "", "接口功能": "获取设备信息，包括版本型号，序列号，运行模式，设备状态，系统时间，运行时间", "接口定义": "public DeviceInfo getDeviceInfo( ) throws SVSException", "输入参数": [], "输出": "DeviceInfo：设备信息", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "监控", "需求编号": "PR-F-SVS-0206", "需求名称": "", "优先级": "", "接口功能": "获取设备监控信息，包括CPU，内存，磁盘，网桥信息，网卡信息，网络IO流量，连接数等", "接口定义": "public DeviceMonitor getDeviceMonitor( ) throws SVSException", "输入参数": [], "输出": "DeviceMonitor：设备监控信息", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "监控", "需求编号": "PR-F-SVS-0233", "需求名称": "", "优先级": "", "接口功能": "设备监控信息", "接口定义": "public class DeviceMonitor { private CPUMonitor cpu; //cpu使用情况 private MemoryMonitor memory; //内存使用情况 private DiskMonitor disk; //磁盘使用情况 private NetworkMonitor network; //网络使用情况 //.每个属性自带get/set属性 } public class CPUMonitor{ private String used; // Cpu使用率，带单位% //.每个属性自带get/set属性 } public class MemoryMonitor { private String total; //内存总大小，带单位(K或M或G,自动转换单位) private String used; //内存使用量，带单位(K或M或G,自动转换单位) //.每个属性自带get/set属性 } public class DiskMonitor { private String total; //磁盘总大小，带单位(K或M或G,自动转换单位) private String used; //磁盘已使用量，带单位(K或M或G,自动转换单位) private String root; //根路径已使用量，带单位(K或M或G,自动转换单位) private String database; //数据库已使用量，带单位(K或M或G,自动转换单位) private String log; //日志已使用量，带单位(K或M或G,自动转换单位) //.每个属性自带get/set属性 } public class NetworkMonitor { private List<Bond> bondList; //网桥列表，没有绑定网卡时该字段为空。。 private List<NIC> nicList; //网卡列表，网卡信息详情查看下面列表。 private List<Connection> connectionList; //多个端口的连接数信息列表 //.每个属性自带get/set属性 } public class Bond{ private String bondNicName; //已绑定的网卡 private String ip; //IPV4地址 private String mask; //IPV4的子网掩码 private String gateway; //IPV4的网关 private String ipv6; //IPV6地址 private String ipv6Len; //IPV6的子网前缀 private String ipv6Gateway; //IPV6的网关 private String rx; //流入总量，带单位mbps private String tx; //流出总量，带单位mbps //.每个属性自带get/set属性 } public class NIC { private String ip; //IPV4地址 private String mask; //IPV4的子网掩码 private String gateway; //IPV4的网关 private String ipv6; //IPV6地址 private String ipv6Len; //IPV6的子网前缀 private String ipv6Gateway; //IPV6的网关 private String rx; //流入总量，带单位mbps private String tx; //流出总量，带单位mbps //.每个属性自带get/set属性 } public class Connection { private int closeWait; // CLOSE_WAIT状态的连接数 private String closeWaitMaxIp; // CLOSE_WAIT状态时，最多连接的IP地址 private String closeWaitMaxConnect; // CLOSE_WAIT状态时，最多连接的IP的连接数量 private String established; // ESTABLISHED状态的连接数 private String establishedMaxIp; // ESTABLISHED状态时，最多连接的IP地址 private String establishedMaxIpConnect; // ESTABLISHED状态时，最多连接的IP的连接数量 private String listen; // LISTEN状态的连接数 private String timeWait; // TIME_WAIT状态的连接数 private String timeWaitMaxIp; // TIME_WAIT状态时，最多连接的IP地址 private String timeWaitMaxIpConnect; // TIME_WAIT状态时，最多连接的IP的连接数量 //.每个属性自带get/set属性 }", "输入参数": [], "输出": "", "异常": "", "其它说明": "", "支持算法": []}], "工具类": [{"章节": "工具类", "需求编号": "PR-F-SVS-0208", "需求名称": "", "优先级": "", "接口功能": "将字节数组做base64编码成字符串", "接口定义": "public String base64Encode(byte[] data ) throws SVSException", "输入参数": [{"参数名": "data", "类型": "byte[]", "必填": "是", "说明": "原文"}], "输出": "Base64字符串", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "工具类", "需求编号": "PR-F-SVS-0209", "需求名称": "", "优先级": "", "接口功能": "Base64解码字符串成byte[]", "接口定义": "public byte[] base64Decode(String base64String ) throws SVSException", "输入参数": [{"参数名": "base64String", "类型": "String", "必填": "是", "说明": "Base64字符串"}], "输出": "Base64解码后的原文", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "工具类", "需求编号": "PR-F-SVS-0216", "需求名称": "", "优先级": "", "接口功能": "将Base64编码的GMT0018规范的SM2密文转换为Base64编码的GMT0009密文", "接口定义": "public String translateCipherGm2Der(String gmt0018Cipher) throws SVSException", "输入参数": [{"参数名": "gmt0018Cipher", "类型": "String", "必填": "是", "说明": "Base64编码的GMT0018规范的SM2密文"}], "输出": "Base64编码的GMT0009规范的密文", "异常": "SVSException", "其它说明": "注意：兼容X和Y为32字节情况", "支持算法": []}, {"章节": "工具类", "需求编号": "PR-F-SVS-0217", "需求名称": "", "优先级": "", "接口功能": "将Base64编码的GMT0009规范的SM2密文转换为Base64编码的GMT0018密文", "接口定义": "public String translateCipherDer2Gm(String gmt0009Cipher) throws SVSException", "输入参数": [{"参数名": "gmt0009Cipher", "类型": "String", "必填": "是", "说明": "Base64编码的GMT0009规范的SM2密文"}], "输出": "Base64编码的GMT0018规范的密文", "异常": "SVSException", "其它说明": "", "支持算法": []}], "其他": [{"章节": "其他操作", "需求编号": "PR-F-SVS-0006", "需求名称": "", "优先级": "", "接口功能": "断开连接，释放资源。", "接口定义": "public synchronized void finalize() throws SVSException", "输入参数": [], "输出": "无", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "其他操作", "需求编号": "PR-F-SVS-0053", "需求名称": "", "优先级": "", "接口功能": "生成指定长度的随机数", "接口定义": "public byte[] generateRandom( int length ) throws SVSException", "输入参数": [{"参数名": "length", "类型": "int", "必填": "是", "说明": "随机数长度，单位：字节"}], "输出": "随机数", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "其他操作", "需求编号": "PR-F-SVS-0199", "需求名称": "", "优先级": "", "接口功能": "生成快速响应码", "接口定义": "public byte[] generateBarCodeQRCode( byte[] msg, int encodeMode， int errorCorrectLevel, int barSize, int returnFormat ) throws SVSException", "输入参数": [{"参数名": "msg", "类型": "byte[]", "必填": "是", "说明": "字符"}, {"参数名": "encodeMode", "类型": "int", "必填": "是", "说明": "字符编码格式0: 2进制，1：英文，2:数字"}, {"参数名": "errorCorrectLevel", "类型": "int", "必填": "是", "说明": "纠错级别 0：M，1：L，2：Q，3：H"}, {"参数名": "barSize", "类型": "int", "必填": "是", "说明": "生成条码单元的大小，默认为3个像素"}, {"参数名": "returnFormat", "类型": "int", "必填": "是", "说明": "生成的图片格式，0：PNG，1：JPEG，2：GIF"}], "输出": "快速响应码条码", "异常": "无", "其它说明": "", "支持算法": []}, {"章节": "其他操作", "需求编号": "PR-F-SVS-0204", "需求名称": "", "优先级": "", "接口功能": "解析时间戳内容", "接口定义": "public byte[] getTSInfo( String timestamp， int itemType ) throws SVSException", "输入参数": [{"参数名": "timestamp", "类型": "String", "必填": "是", "说明": "Base64编码的时间戳"}, {"参数名": "itemType", "类型": "int", "必填": "是", "说明": "信息类型，取值如下： 0：签发时间 1：签发者通用名 2：时间戳请求原始信息 3：时间戳服务器证书 4：时间戳服务器证书链 5：时间源的来源 6：时间精度 7：响应方式 8：签发者国家 9：签发者组织 10：签发者城市 11：签发者邮箱"}], "输出": "时间戳信息", "异常": "无", "其它说明": "当时间戳里不包含时间戳服务器证书时，调用该方法解析时间戳服务器证书和证书链时，返回的是对应的证书序列号。此时，无法解析的签发者的国家、组织、城市和邮箱，但是可以正常解析签发时间、签发者通用名、时间戳请求原始信息", "支持算法": []}, {"章节": "其他操作", "需求编号": "PR-F-SVS-0236", "需求名称": "", "优先级": "", "接口功能": "异常类接口，返回错误码和错误描述", "接口定义": "public SVSException(Integer errorCode,String errorMsg)", "输入参数": [{"参数名": "errorCode", "类型": "Integer", "必填": "是", "说明": "错误码"}, {"参数名": "errorMsg", "类型": "String", "必填": "否", "说明": "错误描述"}], "输出": "无", "异常": "无", "其它说明": "", "支持算法": []}, {"章节": "其他操作", "需求编号": "PR-F-SVS-0237", "需求名称": "", "优先级": "", "接口功能": "异常类接口，返回错误码和错误描述", "接口定义": "public SVSException(Integer errorCode,String errorMsg, Throwable throwable)", "输入参数": [{"参数名": "errorCode", "类型": "Integer", "必填": "是", "说明": "错误码"}, {"参数名": "errorMsg", "类型": "String", "必填": "否", "说明": "错误描述"}], "输出": "无", "异常": "无", "其它说明": "", "支持算法": []}]}}