/**
 * Token Multiplier Transformer
 * 用于修改后端LLM响应中的token使用量
 * 
 * 使用方法：
 * 在配置文件中注册transformer：
  "transformers": [
    {
      "path": "/mnt/d/agent/claude-code-router/plugin/token-multiplier-transformer.js",
      "options": {
        "multiplier": 2.0,
        "readTool": "'mcp read tool'"
      }
    }
  ]
  
 * provider使用transformer：
  "transformer": {
    "use": [
      [
        "token-multiplier",
        {
          "multiplier": 1.5,  // token使用量的乘数系数
          "removeTools": ["ToolName1", "ToolName2"],  // 要移除的工具列表
          "readTool": "mcp__read__read_file"  // 读取文件的工具名称
        }
      ]
    ]
  }
 */

class TokenMultiplierTransformer {
  name = "token-multiplier";

  constructor(options = {}) {
    this.multiplier = options.multiplier || 1.0;
    console.log(`TokenMultiplierTransformer initialized with multiplier: ${this.multiplier}`);
    this.removeTools = options.removeTools || [];
    if (this.removeTools.length > 0) {
      console.log(`TokenMultiplierTransformer will remove tools: ${this.removeTools.join(', ')}`);
    }
    
    this.readTool = options.readTool || null;
    if (this.readTool) {
      console.log(`TokenMultiplierTransformer will replace tool description "Read" with "${this.readTool}"`);
    }
  }

  /*
   * 检查如果是流式请求("stream":true)，则需要增加或修改属性：
   "stream_options": {
        "include_usage": true  
    }
   * 同时根据配置移除指定的工具
  */
  async transformRequestIn(request) {
      // 检查是否为流式请求
      if (request.stream === true) {
        // 添加或修改 stream_options 属性，保留原有属性
        request.stream_options = {
          ...request.stream_options, // 保留原有的 stream_options 属性
          include_usage: true
        };
      }
      
      // 移除指定的工具
      // if (Array.isArray(this.removeTools) && this.removeTools.length > 0 && Array.isArray(request.tools)) {
      //   request.tools = request.tools.filter(tool => 
      //     !this.removeTools.includes(tool.name)
      //   );
      // }
      
      // 替换工具描述中的Read为mcp__read__read_file
      if (this.readTool && Array.isArray(request.tools)) {
        request.tools = request.tools.map(tool => {
          // console.log("Tool :", tool.function.name);
          if (tool.function && tool.function.description) {
            // 创建新的工具对象以避免直接修改原始对象
            const newTool = { ...tool };
            newTool.function.description = newTool.function.description.replace(/\bRead\b/g, this.readTool);
            return newTool;
          }
          return tool;
        });
      }
      
      return request;
  }
    

  /**
   * 修改usage对象中的token数量
   * @param {Object} usage - 原始usage对象
   * @returns {Object} - 修改后的usage对象
   */
  multiplyUsage(usage) {
    // console.log(`Multiplying usage: ${JSON.stringify(usage)}`);
    if (!usage || typeof usage !== 'object') {
      return usage;
    }

    const modifiedUsage = { ...usage };

    // 修改各种token字段
    if (typeof usage.completion_tokens === 'number') {
      modifiedUsage.completion_tokens = Math.round(usage.completion_tokens * this.multiplier);
    }
    
    if (typeof usage.prompt_tokens === 'number') {
      modifiedUsage.prompt_tokens = Math.round(usage.prompt_tokens * this.multiplier);
    }
    
    if (typeof usage.total_tokens === 'number') {
      modifiedUsage.total_tokens = Math.round(usage.total_tokens * this.multiplier);
    }

    // 支持其他可能的token字段名称
    if (typeof usage.input_tokens === 'number') {
      modifiedUsage.input_tokens = Math.round(usage.input_tokens * this.multiplier);
    }
    
    if (typeof usage.output_tokens === 'number') {
      modifiedUsage.output_tokens = Math.round(usage.output_tokens * this.multiplier);
    }

    //console.log(`Token usage modified: ${JSON.stringify(usage)} -> ${JSON.stringify(modifiedUsage)}`);
    return modifiedUsage;
  }

  /**
   * 处理响应（包括流式和非流式）
   * @param {Response} response - HTTP响应对象
   * @returns {Promise<Response>} - 修改后的响应
   */
  async transformResponseOut(response) {
    try {
      if (response.headers.get("Content-Type")?.includes("application/json")) {
        // 处理非流式JSON响应
        const jsonResponse = await response.json();

        // 修改usage字段
        if (jsonResponse.usage) {
          jsonResponse.usage = this.multiplyUsage(jsonResponse.usage);
        }

        // 创建新的响应
        return new Response(JSON.stringify(jsonResponse), {
          status: response.status,
          statusText: response.statusText,
          headers: response.headers,
        });
      } else if (response.headers.get("Content-Type")?.includes("stream")) {
        // 处理流式响应
        if (!response.body) {
          return response;
        }

        const transformer = this;
        const decoder = new TextDecoder();
        const encoder = new TextEncoder();
        let buffer = ""; // 缓冲区用于处理不完整的数据

        const stream = new ReadableStream({
          async start(controller) {
            const reader = response.body.getReader();

            try {
              while (true) {
                const { done, value } = await reader.read();
                if (done) {
                  // 处理缓冲区中剩余的数据
                  if (buffer.trim()) {
                    const lines = buffer.split('\n');
                    for (const line of lines) {
                      if (line.trim()) {
                        controller.enqueue(encoder.encode(line + '\n'));
                      }
                    }
                  }
                  break;
                }

                const chunk = decoder.decode(value, { stream: true });
                buffer += chunk;

                // 处理缓冲区中的完整行
                const lines = buffer.split('\n');
                buffer = lines.pop() || ""; // 保留不完整的行在缓冲区中

                for (const line of lines) {
                  if (!line.trim()) continue;

                  try {
                    if (line.startsWith('data: ') && line.trim() !== 'data: [DONE]') {
                      const dataStr = line.slice(6); // 移除 'data: ' 前缀
                      const data = JSON.parse(dataStr);

                      // 修改usage字段
                      if (data.usage) {
                        data.usage = transformer.multiplyUsage(data.usage);
                      }

                      // 对于message_delta事件，usage可能在delta中
                      if (data.delta && data.delta.usage) {
                        data.delta.usage = transformer.multiplyUsage(data.delta.usage);
                      }

                      // 检查choices数组中的usage字段
                      if (data.choices && Array.isArray(data.choices)) {
                        for (let i = 0; i < data.choices.length; i++) {
                          const choice = data.choices[i];
                          if (choice.usage) {
                            choice.usage = transformer.multiplyUsage(choice.usage);
                          }
                          if (choice.delta && choice.delta.usage) {
                            choice.delta.usage = transformer.multiplyUsage(choice.delta.usage);
                          }
                        }
                      }

                      // 发送修改后的数据
                      const modifiedLine = 'data: ' + JSON.stringify(data) + '\n';
                      controller.enqueue(encoder.encode(modifiedLine));
                    } else {
                      // 传递非数据行（如 [DONE]）
                      controller.enqueue(encoder.encode(line + '\n'));
                    }
                  } catch (parseError) {
                    // 如果解析失败，保持原样传递
                    console.error("Error parsing stream line in TokenMultiplierTransformer:", parseError);
                    controller.enqueue(encoder.encode(line + '\n'));
                  }
                }
              }
            } catch (error) {
              console.error("Stream error in TokenMultiplierTransformer:", error);
              controller.error(error);
            } finally {
              try {
                reader.releaseLock();
              } catch (e) {
                console.error("Error releasing reader lock:", e);
              }
              controller.close();
            }
          }
        });

        return new Response(stream, {
          status: response.status,
          statusText: response.statusText,
          headers: {
            "Content-Type": "text/event-stream",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
          },
        });
      }
    } catch (error) {
      console.error("Error processing response in TokenMultiplierTransformer:", error);
      return response;
    }

    return response;
  }


}

// 导出transformer类
module.exports = TokenMultiplierTransformer;