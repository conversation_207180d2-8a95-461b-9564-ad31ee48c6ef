#!/usr/bin/env bash
set -e

LOG_DIR="$HOME/.claude-code-router/logs"
PORT=8100
PID_FILE="/tmp/claude-router.pid"

# 解析命令行参数
case "$1" in
    stop)
        if [[ -f "$PID_FILE" ]] && kill -0 "$(cat "$PID_FILE")" 2>/dev/null; then
            echo "Stopping Claude Code Router..."
            kill "$(cat "$PID_FILE")"
            rm -f "$PID_FILE"
            echo "Claude Code Router stopped."
        else
            echo "Claude Code Router is not running."
        fi
        exit 0
        ;;
    restart)
        # 先停止服务
        if [[ -f "$PID_FILE" ]] && kill -0 "$(cat "$PID_FILE")" 2>/dev/null; then
            echo "Stopping Claude Code Router..."
            kill "$(cat "$PID_FILE")"
            rm -f "$PID_FILE"
        fi
        # 继续执行启动逻辑
        echo "Restarting Claude Code Router..."
        ;;
    start|"")
        # 默认行为，继续执行启动逻辑
        ;;
    *)
        echo "Usage: $0 {start|stop|restart}"
        exit 1
        ;;
esac

mkdir -p "$LOG_DIR"

# 如果已运行则退出（可选）
if [[ -f "$PID_FILE" ]] && kill -0 "$(cat "$PID_FILE")" 2>/dev/null; then
    echo "Router already running on PID $(cat "$PID_FILE")"
    exit 0
fi

# 清理日志目录只在启动时进行
rm -rf $LOG_DIR/*
echo "[$(date '+%F %T')] Starting Claude Code Router on port $PORT ..."
nohup ccr start >> "$LOG_DIR/router.out" 2>&1 &
echo $! > "$PID_FILE"

# 等到ccr 启动成功
# for i in {1..30}; do
#     if nc -z localhost $PORT; then
#         echo "ccr started successfully on port $PORT"
#         break
#     fi
#     echo "Waiting for ccr to start on port $PORT... ($i/30)"
#     sleep 3
# done

# tail -f 输出$LOG_DIR目录下ccr开头的日志
#tail -f "$LOG_DIR"/ccr* &
