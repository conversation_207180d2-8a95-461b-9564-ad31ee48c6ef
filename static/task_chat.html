<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI对话 - 全栈助手</title>

    <!-- Bootstrap CSS -->
    <link href="/aicode/static/external/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="/aicode/static/external/all.min.css" rel="stylesheet">
    <!-- Modern Theme -->
    <link href="/aicode/static/css/modern-theme.css" rel="stylesheet">

    <style>
        .chat-container {
            max-height: 70vh;
            overflow-y: auto;
            border: 1.5px solid var(--border-color);
            border-radius: var(--border-radius);
            background-color: var(--bg-secondary);
            padding: 1rem;
        }

        .message-entry {
            border-radius: var(--border-radius-sm);
            margin-bottom: 1rem;
            box-shadow: var(--shadow);
            transition: var(--transition);
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message-entry:hover {
            box-shadow: var(--shadow-md);
        }

        .message-content {
            word-wrap: break-word;
            white-space: pre-wrap;
            font-family: 'Monaco', 'Menlo', 'Courier New', monospace;
            font-size: 0.875rem;
            line-height: 1.6;
        }

        .message-user {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-left: 4px solid #2196f3;
        }

        .message-assistant {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c8 100%);
            border-left: 4px solid #4caf50;
        }

        .message-system {
            background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
            border-left: 4px solid #ff9800;
        }

        .message-streaming {
            background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
            border-left: 4px solid #9c27b0;
            position: relative;
        }

        .typing-indicator {
            display: inline-block;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {

            0%,
            100% {
                opacity: 0.3;
            }

            50% {
                opacity: 1;
            }
        }

        .input-area {
            border-top: 2px solid var(--border-color);
            background-color: var(--bg-primary);
            padding: 1rem;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 0.5rem;
            box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
        }

        .status-connected {
            background-color: var(--success-color);
        }

        .status-disconnected {
            background-color: #dc3545;
        }

        .status-typing {
            background-color: #9c27b0;
            animation: pulse 1.5s infinite;
        }

        .load-more-btn {
            background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
            border: 1px solid #ccc;
            color: #666;
        }

        .load-more-btn:hover {
            background: linear-gradient(135deg, #eeeeee 0%, #d0d0d0 100%);
            color: #333;
        }

        /* 自定义滚动条 */
        .chat-container::-webkit-scrollbar {
            width: 8px;
        }

        .chat-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .chat-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .chat-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .message-time {
            font-size: 0.75rem;
            color: #666;
            margin-left: 0.5rem;
        }

        /* 内容折叠样式 */
        .message-content.collapsed {
            max-height: 5.6em;
            /* 5行高度 */
            overflow: hidden;
            position: relative;
        }

        .message-content.collapsed::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2em;
            background: linear-gradient(transparent, var(--bg-secondary));
        }

        .toggle-content-btn {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(0, 0, 0, 0.1);
            color: #666;
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            border-radius: 0.25rem;
            margin-top: 0.5rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .toggle-content-btn:hover {
            background: rgba(255, 255, 255, 1);
            border-color: rgba(0, 0, 0, 0.2);
            color: #333;
        }

        .message-entry.expanded .message-content {
            max-height: none;
        }

        .message-entry.expanded .message-content::after {
            display: none;
        }

        /* 加载动画优化 */
        .loading-dots::after {
            content: '...';
            animation: dots 1.5s steps(3, end) infinite;
        }

        @keyframes dots {

            0%,
            20% {
                content: '.';
            }

            40% {
                content: '..';
            }

            60%,
            100% {
                content: '...';
            }
        }

        /* 消息进入动画 */
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message-new {
            animation: slideInUp 0.3s ease-out;
        }

        /* 新消息提示 */
        .new-message-indicator {
            position: absolute;
            top: -10px;
            right: -10px;
            background: #ff4757;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            font-weight: bold;
            box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);
            animation: pulse 2s infinite;
        }

        /* 优化滚动条 */
        .chat-container::-webkit-scrollbar {
            width: 6px;
        }

        .chat-container::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 3px;
        }

        .chat-container::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 3px;
            transition: background 0.2s ease;
        }

        .chat-container::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 0, 0, 0.3);
        }

        /* 输入框聚焦效果 */
        .form-control:focus {
            border-color: #4CAF50;
            box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25);
        }

        /* 按钮悬停效果优化 */
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }

        /* 消息内容代码块样式 */
        .message-content pre {
            background: rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            padding: 8px 12px;
            margin: 8px 0;
            overflow-x: auto;
            font-size: 0.8rem;
        }

        .message-content code {
            background: rgba(0, 0, 0, 0.05);
            padding: 2px 4px;
            border-radius: 3px;
            font-size: 0.9em;
        }
    </style>
</head>

<body>
    <!-- 对话容器 -->
    <div class="card-body p-0">
        <!-- 对话消息容器 -->
        <div class="chat-container" id="chatContainer" style="height: calc(90vh - 100px);">
            <!-- 加载更多按钮（在对话容器内部顶部） -->
            <div class="text-center p-2 border-bottom" id="loadMoreSection" style="display: none;">
                <button type="button" class="btn btn-sm load-more-btn" id="loadMoreBtn">
                    <i class="fas fa-history"></i> 显示更多历史记录
                </button>
            </div>

            <!-- 消息列表区域 -->
            <div id="messagesList">
                <div class="text-center py-5">
                    <i class="fas fa-spinner fa-spin fa-3x text-muted mb-3"></i>
                    <div>加载对话历史中...</div>
                </div>
            </div>
        </div>

        <!-- 输入区域 -->
        <div class="input-area">
            <div class="row">
                <div class="col-md-12">
                    <div class="input-group">
                        <textarea class="form-control" id="messageInput" placeholder="输入您的问题..." rows="2"
                            maxlength="500"></textarea>
                        <button class="btn btn-primary" type="button" id="sendBtn" disabled>
                            <i class="fas fa-paper-plane"></i> 发送
                        </button>
                        <button class="btn btn-primary" type="button" id="refreshBtn">
                            <i class="fas fa-paper-plane"></i> 刷新
                        </button>
                    </div>
                    <div class="d-flex justify-content-between mt-2">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            按 Enter 发送，Shift+Enter 换行，最多500字符
                        </small>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="autoRefreshCheckbox">
                            <label class="form-check-label" for="autoRefreshCheckbox">
                                <small class="text-muted">自动刷新</small>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="/aicode/static/external/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="/aicode/static/external/jquery-3.6.0.min.js"></script>

    <!-- 自定义JS -->
    <script src="/aicode/static/js/common.js"></script>
    <script src="/aicode/static/js/task_chat.js"></script>

    <script>
        // 页面初始化
        $(document).ready(function () {
            const projectId = Router.getParam('project_id');
            const taskId = Router.getParam('task_id');

            if (!projectId || !taskId) {
                Utils.showAlert('缺少必要参数', 'danger');
                Router.navigate('projects.html');
                return;
            }

            TaskChatManager.initChatPage(projectId, taskId);

            // 绑定返回任务按钮
            $('#back-to-tasks-btn').on('click', function () {
                Router.navigate(`project_tasks.html?project_id=${projectId}`);
            });

            // 绑定清空对话按钮
            $('#clear-chat-btn').on('click', function () {
                TaskChatManager.clearChat();
            });

            // 绑定发送按钮
            $('#sendBtn').on('click', function () {
                TaskChatManager.sendMessage();
            });

            // 绑定刷新按钮
            $('#refreshBtn').on('click', function () {
                TaskChatManager.refreshMessage();
            });

            // 绑定输入框事件
            $('#messageInput').on('input', function () {
                const text = $(this).val();
                const length = text.length;
                $('#charCount').text(`${length}/2000`);

                // 启用/禁用发送按钮
                $('#sendBtn').prop('disabled', text.trim() === 0);
            });

            // 绑定键盘事件
            $('#messageInput').on('keydown', function (e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    if ($('#sendBtn').prop('disabled') === false) {
                        TaskChatManager.sendMessage();
                    }
                }
            });

            // 绑定加载更多按钮
            $('#loadMoreBtn').on('click', function () {
                TaskChatManager.loadMoreMessages();
            });
        });
    </script>
</body>

</html>