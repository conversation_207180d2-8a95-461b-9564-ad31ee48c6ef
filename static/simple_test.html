<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试界面 - 全栈助手</title>

    <!-- Bootstrap CSS -->
    <link href="/aicode/static/external/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="/aicode/static/external/all.min.css" rel="stylesheet">

    <!-- Bootstrap JS -->
    <script src="/aicode/static/external/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="/aicode/static/external/jquery-3.6.0.min.js"></script>
    <!-- markdown-editor -->
    <link href="/aicode/static/external/cherry-markdown.css" rel="stylesheet">
    <script src="/aicode/static/external/cherry-markdown.js"></script>
    <script src="/aicode/static/external/echarts.min.js"></script>
    <script src="/aicode/static/external/cherry-table-echarts-plugin.js"></script>

    <script src="/aicode/static/js/common.js"></script>
    <script src="/aicode/static/js/tasks.js"></script>
    <style>
        html {
            height: 100%;
        }

        body {
            height: 100%;
            overflow: hidden;
            margin: 0;
        }

        #markdownEditor {
            min-width: 800px;
            max-width: 2600px;
            width: 100%;
            height: 100vh;
            margin: 0 auto;
        }

        iframe.cherry-dialog-iframe {
            width: 100%;
            height: 100%;
        }
    </style>
</head>

<body>
    <!-- 编辑区域 -->
    <div id="markdownEditor" placeholder="请输入需求内容...">
    </div>
</body>
<script>
    let apiEndpoint = 'rules';
    let contentField = 'rules_constraint';
    var saveMenu = Cherry.createMenuHook('保存', {
        noIcon: true, name: '保存',
        onClick: function (selection) {
            saveContent();
            return selection
        }
    });
    /**
     * 定义带图表表格的按钮
     */
    var customMenuTable = Cherry.createMenuHook('图表', {
        iconName: 'trendingUp',
        subMenuConfig: [
            { noIcon: true, name: '折线图', onclick: (event) => { cherry.insert('\n| :line:{x,y} | Header1 | Header2 | Header3 | Header4 |\n| ------ | ------ | ------ | ------ | ------ |\n| Sample1 | 11 | 11 | 4 | 33 |\n| Sample2 | 112 | 111 | 22 | 222 |\n| Sample3 | 333 | 142 | 311 | 11 |\n'); } },
            { noIcon: true, name: '柱状图', onclick: (event) => { cherry.insert('\n| :bar:{x,y} | Header1 | Header2 | Header3 | Header4 |\n| ------ | ------ | ------ | ------ | ------ |\n| Sample1 | 11 | 11 | 4 | 33 |\n| Sample2 | 112 | 111 | 22 | 222 |\n| Sample3 | 333 | 142 | 311 | 11 |\n'); } },
            { noIcon: true, name: '雷达图', onclick: (event) => { cherry.insert('\n| :radar:{x,y} | 技能1 | 技能2 | 技能3 | 技能4 | 技能5 |\n| ------ | ------ | ------ | ------ | ------ | ------ |\n| 用户A | 90 | 85 | 75 | 80 | 88 |\n| 用户B | 75 | 90 | 88 | 85 | 78 |\n| 用户C | 85 | 78 | 90 | 88 | 85 |\n'); } },
            { noIcon: true, name: '热力图', onclick: (event) => { cherry.insert('\n| :heatmap:{x,y,value} | 周一 | 周二 | 周三 | 周四 | 周五 |\n| ------ | ------ | ------ | ------ | ------ | ------ |\n| 上午 | 10 | 20 | 30 | 40 | 50 |\n| 下午 | 15 | 25 | 35 | 45 | 55 |\n| 晚上 | 5 | 15 | 25 | 35 | 45 |\n'); } },
            { noIcon: true, name: '饼图', onclick: (event) => { cherry.insert('\n| :pie:{name,value} | 数值 |\n| ------ | ------ |\n| 苹果 | 40 |\n| 香蕉 | 30 |\n| 橙子 | 20 |\n| 葡萄 | 10 |\n'); } },
            { noIcon: true, name: '地图', onclick: (event) => { cherry.insert('\n<!-- mapDataSource: static/external/100000_full.json -->\n| :map:{name,value} | 数值 |\n| :-: | :-: |\n| 北京 | 100 |\n| 上海 | 200 |\n| 广东 | 300 |\n| 四川 | 150 |\n| 江苏 | 250 |\n| 浙江 | 180 |\n\n**说明：** 修改注释中的URL来自定义地图数据源\n'); } },
        ]
    });
    var config = {
        id: 'markdownEditor',
        value: '',
        callback: {
            afterChange: function (md, html) {
                //console.log('change');
            }
        },
        toolbars: {
            toolbar: [
                'saveMenuName', 'undo', 'redo',
                '|', 'ol', 'ul', 'list', 'checklist', 'align',
                '|', 'graph', 'customMenuTableName',
                '|', 'togglePreview',
            ],
            customMenu: {
                saveMenuName: saveMenu,
                customMenuTableName: customMenuTable,
            },
        }
    }
    var cherry = new Cherry(config);

    // 初始化
    $(document).ready(function () {
        const content = '# 这是1级标题 \n ## 这是2级标题 \n ### 这是3级标题 \n 这是一段普通文本。';
        cherry.setValue(content);
    });
</script>

</html>