<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>规则管理 - 全栈助手</title>

    <!-- Bootstrap CSS -->
    <link href="/aicode/static/external/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="/aicode/static/external/all.min.css" rel="stylesheet">
    <!-- Bootstrap JS -->
    <script src="/aicode/static/external/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="/aicode/static/external/jquery-3.6.0.min.js"></script>

    <!-- markdown-editor -->
    <link href="/aicode/static/external/cherry-markdown.css" rel="stylesheet">
    <script src="/aicode/static/external/cherry-markdown.js"></script>
    <script src="/aicode/static/external/echarts.min.js"></script>
    <script src="/aicode/static/external/cherry-table-echarts-plugin.js"></script>
    <script src="/aicode/static/js/common.js"></script>
    <style>
        html {
            height: 100%;
        }

        body {
            height: 100%;
            overflow: hidden;
            margin: 0;
        }

        #markdownEditor {
            min-width: 800px;
            max-width: 2600px;
            width: 100%;
            height: 100vh;
            margin: 0 auto;
        }

        iframe.cherry-dialog-iframe {
            width: 100%;
            height: 100%;
        }
    </style>
</head>

<body>
    <!-- 编辑区域 -->
    <div id="markdownEditor" placeholder="请输入设计内容...">
    </div>

    <script>
        let apiEndpoint = 'rules';
        let contentField = 'rules_constraint';
        var saveMenu = Cherry.createMenuHook('保存', {
            noIcon: true, name: '保存',
            onClick: function (selection) {
                saveContent();
                return selection
            }
        });

        // 初始化菜单
        var initMenu = Cherry.createMenuHook('初始化', {
            noIcon: true, name: '初始化',
            onClick: function (selection) {
                initRules();
                return selection
            }
        });
        var config = {
            id: 'markdownEditor',
            value: '',
            editor: {
                defaultModel: 'editOnly'
            },
            callback: {
                afterChange: function (md, html) {
                    //console.log('change');
                }
            },
            toolbars: {
                toolbar: [
                    'initMenuName', 'saveMenuName', 'undo', 'redo',
                    '|', 'ol', 'ul', 'list', 'checklist', 'align',
                    '|', 'togglePreview',
                ],
                customMenu: {
                    saveMenuName: saveMenu,
                    initMenuName: initMenu
                },
            }
        }
        var cherry = new Cherry(config);

        // 获取URL参数
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 初始化
        $(document).ready(function () {
            currentProjectId = getUrlParameter('project_id');
            if (!currentProjectId) {
                alert('缺少项目ID参数');
                window.close();
                return;
            }

            fetch(`/aicode/api/projects/${currentProjectId}`)
                .then(response => response.json())
                .then(data => {
                    const content = data[contentField] || '';
                    cherry.setValue(content);
                })
                .catch(error => {
                    Utils.showAlert('加载内容失败', 'danger');
                });
        });
        function saveContent() {
            const content = cherry.getValue();
            const payload = {};
            payload[contentField] = content;

            fetch(`/aicode/api/projects/${currentProjectId}/${apiEndpoint}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(payload)
            })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        Utils.showAlert('保存成功', 'success');
                    } else {
                        Utils.showAlert('保存失败: ' + result.message, 'danger');
                    }
                })
                .catch(error => {
                    Utils.showAlert('保存失败', 'danger');
                });
        }

        // 初始化规则
        function initRules() {
            if (!confirm('确定要初始化规则吗？这将覆盖当前的规则内容。')) {
                return;
            }

            Utils.showAlert('正在初始化规则...', 'info');

            fetch(`/aicode/api/projects/${currentProjectId}/init_rules`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        cherry.setValue(result.message || '');
                        Utils.showAlert('规则初始化成功', 'success');
                    } else {
                        Utils.showAlert('规则初始化失败: ' + result.message, 'danger');
                    }
                })
                .catch(error => {
                    Utils.showAlert('规则初始化失败', 'danger');
                });
        }

    </script>
</body>

</html>