<html lang="en">

<head>
    <meta charset="utf-8" />
    <title>终端管理</title>
    <style>
        html {
            font-family: arial;
            height: 100%;
            width: 100%;
        }
        body {
            height: 100%;
            width: 100%;
            overflow: hidden;
            margin: 0;
        }
    </style>
    <link rel="stylesheet" href="/aiterm/static/css/xterm.css" />
</head>

<body>
    <span style="font-size: small">status:
        <span style="font-size: small" id="status">connecting...</span></span>

    <div style="width: 100%; height: calc(100% - 50px)" id="terminal"></div>
    <script src="/aiterm/static/external/jquery-3.6.0.min.js"></script>
    <script src="/aiterm/static/external/xterm.js"></script>
    <script src="/aiterm/static/external/xterm-addon-fit.js"></script>
    <script src="/aiterm/static/external/xterm-addon-web-links.js"></script>
    <script src="/aiterm/static/external/xterm-addon-search.js"></script>
    <script src="/aiterm/static/external/socket.io.min.js"></script>

    <script>
        let currentProjectId = null;
        // 获取URL参数
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }
        currentProjectId = getUrlParameter('project_id');
        // 初始化
        $(document).ready(function () {
            if (!currentProjectId) {
                alert('缺少项目ID参数');
                window.close();
                return;
            }            
        });

        const term = new Terminal({
            cursorBlink: true,
            macOptionIsMeta: true,
            scrollback: true,
        });
        term.attachCustomKeyEventHandler(customKeyEventHandler);
        // https://github.com/xtermjs/xterm.js/issues/2941
        const fit = new FitAddon.FitAddon();
        term.loadAddon(fit);
        //term.loadAddon(new WebLinksAddon.WebLinksAddon());
        //term.loadAddon(new SearchAddon.SearchAddon());

        term.open(document.getElementById("terminal"));
        fit.fit();
        term.resize(25, 50);
        // console.log(`size: ${term.cols} columns, ${term.rows} rows`);
        fit.fit();
        // term.writeln("Welcome to pyxterm.js!");
        // term.writeln("https://github.com/cs01/pyxterm.js");
        // term.writeln('')
        term.writeln("You can copy with ctrl+shift+x");
        term.writeln("You can paste with ctrl+shift+v");
        term.writeln('')
        term.onData((data) => {
            // console.log("browser terminal received new data:", data);
            socket.emit("pty-input", { input: data, project_id: currentProjectId });
        });

        const socket = io.connect("/aiterm/pty?project_id=" + currentProjectId);
        const status = document.getElementById("status");

        socket.on("pty-output", function (data) {
            // console.log("new output received from server:", data.output);
            // 验证 project_id 是否匹配（可选）
            if (data.project_id && data.project_id !== currentProjectId) {
                console.warn("收到其他项目的输出，忽略");
                return;
            }
            term.write(data.output);
        });

        socket.on("connect", () => {
            fitToscreen();
            status.innerHTML =
                '<span style="background-color: lightgreen;">connected</span>';
        });

        socket.on("disconnect", () => {
            status.innerHTML =
                '<span style="background-color: #ff8383;">disconnected</span>';
        });

        // 页面卸载时关闭终端
        window.addEventListener('beforeunload', function() {
            socket.emit("close", { project_id: currentProjectId });
        });

        function fitToscreen() {
            fit.fit();
            const dims = { cols: term.cols, rows: term.rows, project_id: currentProjectId };
            // console.log("sending new dimensions to server's pty", dims);
            socket.emit("resize", dims);
        }

        function debounce(func, wait_ms) {
            let timeout;
            return function (...args) {
                const context = this;
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(context, args), wait_ms);
            };
        }

        /**
         * Handle copy and paste events
         */
        function customKeyEventHandler(e) {
            if (e.type !== "keydown") {
                return true;
            }
            if (e.ctrlKey && e.shiftKey) {
                const key = e.key.toLowerCase();
                if (key === "v") {
                    // ctrl+shift+v: paste whatever is in the clipboard
                    navigator.clipboard.readText().then((toPaste) => {
                        term.writeText(toPaste);
                    });
                    return false;
                } else if (key === "c" || key === "x") {
                    // ctrl+shift+x: copy whatever is highlighted to clipboard

                    // 'x' is used as an alternate to 'c' because ctrl+c is taken
                    // by the terminal (SIGINT) and ctrl+shift+c is taken by the browser
                    // (open devtools).
                    // I'm not aware of ctrl+shift+x being used by anything in the terminal
                    // or browser
                    const toCopy = term.getSelection();
                    navigator.clipboard.writeText(toCopy);
                    term.focus();
                    return false;
                }
            }
            return true;
        }

        const wait_ms = 50;
        window.onresize = debounce(fitToscreen, wait_ms);
    </script>
</body>

</html>