<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cherry Editor - Markdown Editor</title>
    <style>
        html,
        body {
            margin: 0;
            padding: 0;
            height: 100%;
            overflow: hidden;
        }

        video {
            max-width: 400px;
        }

        #demo-val {
            display: none;
        }

        img {
            max-width: 100%;
        }

        iframe.cherry-dialog-iframe {
            width: 100%;
            height: 100%;
        }
    </style>
    <link href="/aicode/static/external/cherry-markdown.css" rel="stylesheet">
    <link href="/examples/basic.md" rel="preload" as="fetch" crossorigin="anonymous">
</head>

<body>
    <div id="dom_mask" style="position: absolute; top: 40px; height: 20px; width: 100%;"></div>
    <div id="markdown"></div>
    <script src="/aicode/static/external/cherry-markdown.js"></script>
    <script src="/aicode/static/external/echarts.min.js"></script>
    <script src="/aicode/static/external/cherry-table-echarts-plugin.js"></script>
    <script src="/examples/pinyin_dist.js"></script>
    <script src="/examples/index-demo.js"></script>
</body>

</html>