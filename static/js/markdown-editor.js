
/**
 * 通用Markdown编辑器组件
 */
let currentProjectId = null;
let currentViewMode = 'edit';

var saveMenu = Cherry.createMenuHook('保存', {
    noIcon: true, name: '保存',
    onClick: function (selection) {
        saveContent();
        return selection
    }
});
// 判断aiMenu变量是否定义，没有则创建
if (typeof aiMenu === 'undefined') {
    var aiMenu = Cherry.createMenuHook('AI', {
        noIcon: true, name: 'AI'
    });
}

// 根据用户选择，返回对选中文本的处理
function wrap_selection(selection, new_content, action) {
    if (action == 'replace') {
        return new_content;
    } else if (action == 'append') {
        return `${selection}\n${new_content}`;
    } else if (action == 'insert') {
        return `${new_content}\n${selection}`;
    }
    return selection;
}

// 气泡工具栏的通用助手菜单
var askAiMenu = Cherry.createMenuHook('AI助手', {
    iconName: 'question',
    onClick: async function (selection) {
        const request = await showAskAI(`AI助手`, "请输入你的问题");
        if (request) {
            var content = await askAI(selection, request.text, enable_kb, request.deepThinking);
            const ask_button = await showCustomConfirm(`AI结果`, content, selection);
            return wrap_selection(selection, content, ask_button.action);
        }
        return selection;
    }
});

// 气泡工具栏的创建任务菜单
var taskMenu = Cherry.createMenuHook('创建任务', {
    iconName: 'phone',
    onClick: async function (selection) {
        genTask(selection);
        return selection;
    }
});

// 气泡工具栏的AI菜单-多个二级菜单，快速AI应用
var aiBubbleMenu = Cherry.createMenuHook('AI', {
    iconName: "pen", name: 'BubbleAI',
    onClick: async (selection, type) => {
        switch (type) {
            case 'summary':
                var content = await optimizeSelection(selection, "总结", enable_kb);
                const summary_button = await showCustomConfirm(`AI总结`, content, selection);
                return wrap_selection(selection, content, summary_button.action);
            case 'polish':
                //var content = "这是AI润色"; 
                var content = await optimizeSelection(selection, "润色", enable_kb);
                // 弹出框显示 content
                const polish_button = await showCustomConfirm(`AI润色结果`, content, selection);
                // console.log("用户的选择: " + button.action);
                return wrap_selection(selection, content, polish_button.action);
            case 'explain':
                var content = await optimizeSelection(selection, "解释", enable_kb);
                // 弹出框显示 content
                await showCustomConfirm('AI解释', content, selection, false, false);
                return selection
            case 'expand':
                var content = await optimizeSelection(selection, "扩写", enable_kb);
                const expand_button = await showCustomConfirm(`AI扩写结果`, content, selection);
                return wrap_selection(selection, content, expand_button.action);
            case 'translate':
                var content = await optimizeSelection(selection, "翻译", enable_kb);
                // 弹出框显示 content
                await showCustomConfirm('AI翻译', content, selection, false, false);
                return selection
            case 'draw':
                // 弹出框：选择画图类型及补充说明
                var draw_config = await showDrawConfig('绘图', '请选择绘图类型及补充说明', selection);
                var draw_content = await drawAI(selection, draw_config.type, draw_config.prompt, enable_kb)
                if (draw_content != "") {
                    return `${selection}\n${draw_content}`
                }
                return selection
            default:
                return selection;
        }
    },
    subMenuConfig: [
        {
            noIcon: true,
            name: '解释',
            onclick: (event) => {
                cherry.toolbar.menus.hooks.aiBubbleMenuName.fire(null, 'explain');
            },
        },
        {
            noIcon: true,
            name: '翻译',
            onclick: (event) => {
                cherry.toolbar.menus.hooks.aiBubbleMenuName.fire(null, 'translate');
            },
        },
        {
            noIcon: true,
            name: '总结',
            onclick: (event) => {
                cherry.toolbar.menus.hooks.aiBubbleMenuName.fire(null, 'summary');
            },
        },
        {
            noIcon: true,
            name: '润色',
            onclick: (event) => {
                cherry.toolbar.menus.hooks.aiBubbleMenuName.fire(null, 'polish');
            },
        },
        {
            noIcon: true,
            name: '扩写',
            onclick: (event) => {
                cherry.toolbar.menus.hooks.aiBubbleMenuName.fire(null, 'expand');
            },
        },
        {
            noIcon: true,
            name: '绘图',
            onclick: (event) => {
                cherry.toolbar.menus.hooks.aiBubbleMenuName.fire(null, 'draw');
            },
        },
    ],
});


// 首位置的浮动工具栏
var floatAiMenu = Cherry.createMenuHook('AI', {
    noIcon: true, name: 'AI',
    onClick: async function () {
        //genTask();        
        const allLines = cherry.editor.editor.getValue().split('\n'); // 获取所有行
        if (allLines.length < 1) {
            return showAiChat();
        }
        const cursor = cherry.editor.editor.getCursor(); // 获取光标位置
        
        const currentLine = cursor.line;
        // 前10行文本
        const prevLinesStart = Math.max(0, currentLine - 10); // 前10行的起始行号，避免负数
        const prevLines = allLines.slice(prevLinesStart, currentLine).join('\n'); // 获取前10行的文本内容

        // 后10行文本
        const nextLinesEnd = Math.min(allLines.length - 1, currentLine + 10);
        const nextLines = allLines.slice(cursor.line + 1, nextLinesEnd).join('\n');

        return showAiChat(prevLines,nextLines);
    }
});

// 知识库相关功能
let enable_kb = true

// 导入知识库菜单
var kb_Menu = Cherry.createMenuHook('导入知识库', {
    noIcon: true, name: '导入知识库',
    onClick: async function (selection) {
        // 先保存当前内容
        saveContent();

        // 显示确认对话框
        const confirmed = confirm('确定要将当前文档导入到知识库吗？');
        if (!confirmed) {
            return selection;
        }

        try {
            // 调用后端API导入文档到知识库
            const response = await fetch(`/aicode/api/projects/${currentProjectId}/import_to_kb`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    doc_type: docType  // 'requirement' 或 'design'
                })
            });

            const result = await response.json();

            if (result.success) {
                showMessage('文档已成功导入到知识库', 'success');
            } else {
                showMessage('导入失败: ' + result.message, 'danger');
            }
        } catch (error) {
            console.error('导入知识库失败:', error);
            showMessage('导入知识库失败', 'danger');
        }

        return selection;
    }
});
/**
 * 定义带图表表格的按钮
 */
var customMenuTable = Cherry.createMenuHook('图表', {
    iconName: 'trendingUp',
    subMenuConfig: [
        { noIcon: true, name: '折线图', onclick: (event) => { cherry.insert('\n| :line:{x,y} | Header1 | Header2 | Header3 | Header4 |\n| ------ | ------ | ------ | ------ | ------ |\n| Sample1 | 11 | 11 | 4 | 33 |\n| Sample2 | 112 | 111 | 22 | 222 |\n| Sample3 | 333 | 142 | 311 | 11 |\n'); } },
        { noIcon: true, name: '柱状图', onclick: (event) => { cherry.insert('\n| :bar:{x,y} | Header1 | Header2 | Header3 | Header4 |\n| ------ | ------ | ------ | ------ | ------ |\n| Sample1 | 11 | 11 | 4 | 33 |\n| Sample2 | 112 | 111 | 22 | 222 |\n| Sample3 | 333 | 142 | 311 | 11 |\n'); } },
        { noIcon: true, name: '雷达图', onclick: (event) => { cherry.insert('\n| :radar:{x,y} | 技能1 | 技能2 | 技能3 | 技能4 | 技能5 |\n| ------ | ------ | ------ | ------ | ------ | ------ |\n| 用户A | 90 | 85 | 75 | 80 | 88 |\n| 用户B | 75 | 90 | 88 | 85 | 78 |\n| 用户C | 85 | 78 | 90 | 88 | 85 |\n'); } },
        { noIcon: true, name: '热力图', onclick: (event) => { cherry.insert('\n| :heatmap:{x,y,value} | 周一 | 周二 | 周三 | 周四 | 周五 |\n| ------ | ------ | ------ | ------ | ------ | ------ |\n| 上午 | 10 | 20 | 30 | 40 | 50 |\n| 下午 | 15 | 25 | 35 | 45 | 55 |\n| 晚上 | 5 | 15 | 25 | 35 | 45 |\n'); } },
        { noIcon: true, name: '饼图', onclick: (event) => { cherry.insert('\n| :pie:{name,value} | 数值 |\n| ------ | ------ |\n| 苹果 | 40 |\n| 香蕉 | 30 |\n| 橙子 | 20 |\n| 葡萄 | 10 |\n'); } },
        { noIcon: true, name: '地图', onclick: (event) => { cherry.insert('\n<!-- mapDataSource: static/external/100000_full.json -->\n| :map:{name,value} | 数值 |\n| :-: | :-: |\n| 北京 | 100 |\n| 上海 | 200 |\n| 广东 | 300 |\n| 四川 | 150 |\n| 江苏 | 250 |\n| 浙江 | 180 |\n\n**说明：** 修改注释中的URL来自定义地图数据源\n'); } },
    ]
});

var config = {
    id: 'markdownEditor',
    value: '',
    callback: {
        afterAsyncRender: function (md, html) {
        },
        afterChange: function (md, html) {
        }
    },
    event: {
        // 当编辑区内容有实际变化时触发
        // focus: ({ e, cherry }) => {
        //     console.log('focus', e, cherry);
        // },
        // blur: ({ e, cherry }) => {
        //     console.log('blur', e, cherry);
        // },
    },
    // editor: {
    //     defaultModel: 'defaultModel',
    // },
    toolbars: {
        toolbar: [
            'saveMenuName', 'aiMenuName', 'undo', 'redo',
            '|', 'bold', 'italic', 'size',
            '|', 'color', 'header',
            '|', 'ol', 'ul', 'list', 'checklist', 'align', 'panel',
            '|', 'formula',
            {
                insert: ['image', 'audio', 'video', 'link', 'hr', 'br', 'code', 'formula', 'toc', 'table', 'pdf', 'word', 'ruby',],
            },
            'graph',
            'drawIo',
            '|', 'customMenuTableName',
            '|', 'togglePreview',
        ],
        // 气泡工具栏
        bubble: ["bold", "italic", "underline", "strikethrough",
            "|", "size", "color",
            "|", "aiBubbleMenuName", "askAiMenuName", "taskMenuName"
        ],
        float: ['h1', 'h2', 'h3', '|', 'checklist', 'quote', 'table', 'code' , '|', 'floatAiMenuName'],
        toolbarRight: ['export', '|', 'kb_MenuName'],
        customMenu: {
            aiMenuName: aiMenu,
            askAiMenuName: askAiMenu,
            aiBubbleMenuName: aiBubbleMenu,
            taskMenuName: taskMenu,
            saveMenuName: saveMenu,
            customMenuTableName: customMenuTable,
            kb_MenuName: kb_Menu,
            floatAiMenuName: floatAiMenu,
        },
    },
    drawioIframeUrl: '/aicode/drawio_demo.html',
};
var cherry = new Cherry(config);
// 加载内容
function loadContent() {
    fetch(`/aicode/api/projects/${currentProjectId}/${apiEndpoint}`)
        .then(response => response.json())
        .then(data => {
            const content = data[contentField] || '';
            cherry.setValue(content);
        })
        .catch(error => {
            console.error('加载内容失败:', error);
            showMessage('加载内容失败', 'danger');
        });
}

// 保存内容
function saveContent() {
    const content = cherry.getValue();
    const payload = {};
    payload[contentField] = content;

    fetch(`/aicode/api/projects/${currentProjectId}/${apiEndpoint}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
    })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showMessage('保存成功', 'success');
            } else {
                showMessage('保存失败: ' + result.message, 'danger');
            }
        })
        .catch(error => {
            console.error('保存失败:', error);
            showMessage('保存失败', 'danger');
        });
}

// 显示提示信息
function showMessage(message, type = 'info', delay = 3000) {
    const alertId = 'alert-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    const alertHtml = `
                <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show position-fixed" 
                     style="top: 80px; right: 20px; z-index: 1050; min-width: 300px; cursor: pointer;" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

    document.body.insertAdjacentHTML('beforeend', alertHtml);

    const alertElement = document.getElementById(alertId);

    // 点击任何区域关闭alert
    const closeAlert = () => {
        if (alertElement) {
            alertElement.remove();
        }
    };

    alertElement.addEventListener('click', closeAlert);

    // 3秒后自动消失
    if (delay > 0) {
        setTimeout(closeAlert, delay);
    }
}

// 自定义确认对话框（替代原生confirm）
function showCustomConfirm(title, content, originalContent, showOriginal = true, showHeader = true) {
    return new Promise((resolve) => {
        // 生成唯一的模态框ID
        const modalId = 'confirmModal-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);

        // 创建模态框HTML
        const modalHtml = `
            <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}Label" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        ${showHeader ? `
                        <div class="modal-header">
                            <h5 class="modal-title me-auto" id="${modalId}Label">${title}</h5>
                            <div class="d-flex">
                                <button type="button" class="btn btn-secondary btn-sm me-1" id="rejectBtn-${modalId}" data-bs-dismiss="modal">拒绝</button>
                                <button type="button" class="btn btn-warning btn-sm me-1" id="appendBtn-${modalId}">追加</button>
                                <button type="button" class="btn btn-info btn-sm me-1" id="insertBtn-${modalId}">插入</button>
                                <button type="button" class="btn btn-primary btn-sm me-1" id="replaceBtn-${modalId}">替换</button>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                        </div>` : ''}
                        <div class="modal-body">
                            ${showOriginal ? `
                            <div class="mb-3">
                                <label class="form-label fw-bold">原文：</label>
                                <div class="border rounded p-3 bg-light">
                                    <pre class="mb-0" style="white-space: pre-wrap;">${originalContent}</pre>
                                </div>
                            </div>` : ''}
                            <div class="mt-3">
                                <label class="form-label fw-bold">${title}：</label>
                                <div class="border rounded p-3 bg-light">
                                    <pre class="mb-0" style="white-space: pre-wrap;">${content}</pre>
                                </div>
                            </div>
                        </div>
                        ${showHeader ? `
                        <div class="modal-footer d-none">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">拒绝</button>
                            <button type="button" class="btn btn-warning" id="appendBtn-${modalId}Footer">追加</button>
                            <button type="button" class="btn btn-info" id="insertBtn-${modalId}Footer">插入</button>
                            <button type="button" class="btn btn-primary" id="replaceBtn-${modalId}Footer">替换</button>
                        </div>` : ''}
                    </div>
                </div>
            </div>
        `;

        // 将模态框添加到页面中
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 获取模态框元素
        const modalElement = document.getElementById(modalId);
        const replaceBtn = document.getElementById(`replaceBtn-${modalId}`);
        const insertBtn = document.getElementById(`insertBtn-${modalId}`);
        const appendBtn = document.getElementById(`appendBtn-${modalId}`);
        const rejectBtn = document.getElementById(`rejectBtn-${modalId}`);

        // 初始化并显示模态框
        const modal = new bootstrap.Modal(modalElement);
        modal.show();

        // 处理替换按钮点击事件
        if (replaceBtn) {
            replaceBtn.addEventListener('click', () => {
                modal.hide();
                resolve({ action: 'replace', content: content });
            });
        }

        // 处理插入按钮点击事件
        if (insertBtn) {
            insertBtn.addEventListener('click', () => {
                modal.hide();
                resolve({ action: 'insert', content: content });
            });
        }

        // 处理追加按钮点击事件
        if (appendBtn) {
            appendBtn.addEventListener('click', () => {
                modal.hide();
                resolve({ action: 'append', content: content });
            });
        }

        // 处理拒绝按钮点击事件
        if (rejectBtn) {
            rejectBtn.addEventListener('click', () => {
                modal.hide();
                resolve({ action: 'reject', content: originalContent });
            });
        }

        // 监听模态框隐藏事件，清理DOM
        modalElement.addEventListener('hidden.bs.modal', () => {
            // 延迟删除元素，确保动画完成
            setTimeout(() => {
                if (modalElement.parentNode) {
                    modalElement.parentNode.removeChild(modalElement);
                }
            }, 300);

            // 如果用户没有点击任何按钮，不执行任何操作
            resolve({ action: 'cancel' });
        });
    });
}

function showAskAI(title, content) {
    return new Promise((resolve) => {
        // 生成唯一的模态框ID
        const modalId = 'askAiModal-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);

        // 创建模态框HTML
        const modalHtml = `
            <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}Label" aria-hidden="true">
                <div class="modal-dialog modal-md">
                    <div class="modal-content">
                        <div class="modal-header py-2 px-3">
                            <h6 class="modal-title" id="${modalId}Label">${title}</h6>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body py-2 px-3">
                            <div class="mb-2">
                                <textarea class="form-control form-control-sm" id="askAiInput-${modalId}" rows="3" placeholder="请输入您的问题..."></textarea>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="deepThinking-${modalId}">
                                <label class="form-check-label small" for="deepThinking-${modalId}">深度思考</label>
                            </div>
                        </div>
                        <div class="modal-footer py-2 px-3 justify-content-center">
                            <button type="button" class="btn btn-sm btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-sm btn-primary" id="confirmBtn-${modalId}">确认</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 将模态框添加到页面中
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 获取模态框元素
        const modalElement = document.getElementById(modalId);
        const confirmBtn = document.getElementById(`confirmBtn-${modalId}`);
        const inputElement = document.getElementById(`askAiInput-${modalId}`);
        const deepThinkingElement = document.getElementById(`deepThinking-${modalId}`);

        // 初始化并显示模态框
        const modal = new bootstrap.Modal(modalElement);
        modal.show();

        // 处理确认按钮点击事件
        confirmBtn.addEventListener('click', () => {
            const inputValue = inputElement.value;
            const deepThinkingChecked = deepThinkingElement.checked;
            modal.hide();
            resolve({
                text: inputValue,
                deepThinking: deepThinkingChecked
            });
        });

        // 监听模态框隐藏事件，清理DOM
        modalElement.addEventListener('hidden.bs.modal', () => {
            // 延迟删除元素，确保动画完成
            setTimeout(() => {
                if (modalElement.parentNode) {
                    modalElement.parentNode.removeChild(modalElement);
                }
            }, 300);
        });
    });
}

// 优化选中内容
async function optimizeSelection(selection, ai_action, enable_kb) {
    const originalBtnHtml = $('#actionButtons').html();
    var timerId = showAIProgress(ai_action)

    try {
        // 调用后端API进行优化            
        const response = await fetch(`/aicode/api/projects/${currentProjectId}/documentai`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(
                { selection: selection, enable_kb: enable_kb, ai_action: ai_action, doc_type: docType }
            )
        });

        const result = await response.json();

        if (result.success) {
            return result.message;
        } else {
            Utils.showAlert('优化失败: ' + result.message, 'danger');
            return selection;
        }
    } catch (error) {
        Utils.showAlert('优化请求失败', 'danger');
        return selection;
    }
    finally {
        // 清除计时器并恢复按钮
        clearInterval(timerId);
        $('#actionButtons').html(originalBtnHtml);
    };
}

async function askAI(selection, user_input, enable_kb, deep_think) {
    const originalBtnHtml = $('#actionButtons').html();
    var timerId = showAIProgress('AI助手处理')

    try {
        // 调用后端API进行优化            
        const response = await fetch(`/aicode/api/projects/${currentProjectId}/askai`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(
                { selection: selection, enable_kb: enable_kb, user_input: user_input, doc_type: docType, deep_think: deep_think }
            )
        });

        const result = await response.json();

        if (result.success) {
            return result.message;
        } else {
            Utils.showAlert('AI处理失败: ' + result.message, 'danger');
            return selection;
        }
    } catch (error) {
        Utils.showAlert('AI处理失败', 'danger');
        return selection;
    }
    finally {
        // 清除计时器并恢复按钮
        clearInterval(timerId);
        $('#actionButtons').html(originalBtnHtml);
    };
}

// 任务创建对话框
function showGenTaskDialog(selection) {
    return new Promise((resolve) => {
        // 生成唯一的模态框ID
        const modalId = 'genTaskModal-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);

        // 创建模态框HTML
        const modalHtml = `
            <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}Label" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header py-2 px-3">
                            <h6 class="modal-title" id="${modalId}Label">创建任务</h6>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body py-3">
                            <div class="mb-3">
                                <div class="border rounded p-2 bg-light">
                                    <pre class="mb-0" style="white-space: pre-wrap; max-height: 150px; overflow-y: auto;">${selection}</pre>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer py-2 px-3 justify-content-center">
                            <button type="button" class="btn btn-sm btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-sm btn-primary" id="createTaskBtn-${modalId}">创建任务</button>
                            <button type="button" class="btn btn-sm btn-success" id="createAndRunTaskBtn-${modalId}">创建并执行</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 将模态框添加到页面中
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 获取模态框元素
        const modalElement = document.getElementById(modalId);
        const createTaskBtn = document.getElementById(`createTaskBtn-${modalId}`);
        const createAndRunTaskBtn = document.getElementById(`createAndRunTaskBtn-${modalId}`);

        // 初始化并显示模态框
        const modal = new bootstrap.Modal(modalElement);
        modal.show();

        // 处理创建任务按钮点击事件
        createTaskBtn.addEventListener('click', () => {
            modal.hide();
            resolve({ action: 'create', run: false });
        });

        // 处理创建并执行按钮点击事件
        createAndRunTaskBtn.addEventListener('click', () => {
            modal.hide();
            resolve({ action: 'create', run: true });
        });

        // 监听模态框隐藏事件，清理DOM
        modalElement.addEventListener('hidden.bs.modal', () => {
            // 延迟删除元素，确保动画完成
            setTimeout(() => {
                if (modalElement.parentNode) {
                    modalElement.parentNode.removeChild(modalElement);
                }
            }, 300);

            // 如果用户没有点击任何按钮，不执行任何操作
            resolve({ action: 'cancel' });
        });
    });
}

// 更新genTask函数以使用新的对话框
async function genTask(selection) {
    const result = await showGenTaskDialog(selection);

    // 如果用户取消操作，则不执行任何操作
    if (result.action === 'cancel') {
        return;
    }

    fetch(`/aicode/api/projects/${currentProjectId}/gen_task`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(
            { selection: selection, doc_type: docType, enable_kb: enable_kb, is_run: result.run }
        )
    })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                Utils.showAlert('任务创建成功', 'success');
            } else {
                Utils.showAlert('任务创建失败: ' + result.message, 'danger');
            }
        })
        .catch(error => {
            Utils.showAlert('任务创建失败', 'danger');
        })
}

// 绘图配置对话框
function showDrawConfig(title, description, selection) {
    return new Promise((resolve) => {
        // 生成唯一的模态框ID
        const modalId = 'drawConfigModal-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);

        // 创建模态框HTML
        const modalHtml = `
            <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}Label" aria-hidden="true">
                <div class="modal-dialog modal-md">
                    <div class="modal-content">
                        <div class="modal-header py-2 px-3">
                            <h6 class="modal-title" id="${modalId}Label">${title}</h6>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body py-2 px-3">
                            <div class="mb-2">
                                <label class="form-label small mb-1">绘图类型</label>
                                <select class="form-select form-select-sm" id="drawType-${modalId}">
                                    <option value="流程图">流程图</option>
                                    <option value=">时序图">时序图</option>
                                    <option value="类图">类图</option>
                                    <option value="状态图">状态图</option>
                                    <option value="实体关系图">实体关系图</option>
                                    <option value="甘特图">甘特图</option>
                                </select>
                            </div>
                            <div class="mb-2">
                                <label class="form-label small mb-1">补充说明</label>
                                <textarea class="form-control form-control-sm" id="drawPrompt-${modalId}" rows="2" placeholder="请详细描述你想要绘制的图表内容..." style="font-size: 0.875rem;"></textarea>
                            </div>
                        </div>
                        <div class="modal-footer py-2 px-3 justify-content-center">
                            <button type="button" class="btn btn-sm btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-sm btn-primary" id="confirmDrawBtn-${modalId}">确认生成</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 将模态框添加到页面中
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 获取模态框元素
        const modalElement = document.getElementById(modalId);
        const confirmBtn = document.getElementById(`confirmDrawBtn-${modalId}`);
        const drawTypeElement = document.getElementById(`drawType-${modalId}`);
        const drawPromptElement = document.getElementById(`drawPrompt-${modalId}`);

        // 初始化并显示模态框
        const modal = new bootstrap.Modal(modalElement);
        modal.show();

        // 处理确认按钮点击事件
        confirmBtn.addEventListener('click', () => {
            const drawType = drawTypeElement.value;
            const drawPrompt = drawPromptElement.value;
            modal.hide();
            resolve({
                type: drawType,
                prompt: drawPrompt
            });
        });

        // 监听模态框隐藏事件，清理DOM
        modalElement.addEventListener('hidden.bs.modal', () => {
            // 延删除元素，确保动画完成
            setTimeout(() => {
                if (modalElement.parentNode) {
                    modalElement.parentNode.removeChild(modalElement);
                }
            }, 300);
        });
    });
}
async function drawAI(selection, draw_type, prompt, enable_kb, deep_think = false) {
    const originalBtnHtml = $('#actionButtons').html();
    var timerId = showAIProgress('AI绘图')

    try {
        // 调用后端API进行优化            
        const response = await fetch(`/aicode/api/projects/${currentProjectId}/drawai`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(
                { selection: selection, enable_kb: enable_kb, draw_type: draw_type, prompt: prompt, deep_think: deep_think }
            )
        });

        const result = await response.json();

        if (result.success) {
            return result.message;
        } else {
            Utils.showAlert('AI处理失败: ' + result.message, 'danger');
            return "";
        }
    } catch (error) {
        Utils.showAlert('AI处理失败', 'danger');
        return "";
    }
    finally {
        // 清除计时器并恢复按钮
        clearInterval(timerId);
        $('#actionButtons').html(originalBtnHtml);
    };
}
// 引导式需求生成
function showAiChat(prevLines = "", nextLines = "") {
    return new Promise((resolve) => {
        // 生成唯一的模态框ID
        const modalId = 'aiChatModal-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);

        // 创建模态框HTML
        const modalHtml = `
            <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}Label" aria-hidden="true">
                <div class="modal-dialog modal-xl modal-dialog-scrollable">
                    <div class="modal-content" style="height: 85vh;">
                        <div class="modal-header py-2 px-3">
                            <h6 class="modal-title" id="${modalId}Label">AI引导式需求生成</h6>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body py-2 px-3 d-flex flex-column" style="overflow: hidden;">
                            <div id="chatMessages-${modalId}" class="flex-grow-1 overflow-auto mb-3 border rounded p-3 bg-light" style="min-height: 0;">
                                <div class="text-muted text-center py-5">
                                    <p>欢迎使用AI引导式需求生成功能</p>
                                    <p>请描述您的需求，AI将引导您完善需求规格</p>
                                </div>
                            </div>
                            <div class="d-flex gap-2">
                                <textarea class="form-control form-control-sm" id="chatInput-${modalId}" rows="2" placeholder="请输入您的需求..." style="resize: none;"></textarea>
                                <div class="d-flex flex-column gap-1">
                                    <button type="button" class="btn btn-sm btn-primary" id="sendBtn-${modalId}">发送</button>
                                    <button type="button" class="btn btn-sm btn-success d-none" id="acceptBtn-${modalId}">采纳</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 将模态框添加到页面中
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 获取模态框元素
        const modalElement = document.getElementById(modalId);
        const chatMessages = document.getElementById(`chatMessages-${modalId}`);
        const chatInput = document.getElementById(`chatInput-${modalId}`);
        const sendBtn = document.getElementById(`sendBtn-${modalId}`);
        const acceptBtn = document.getElementById(`acceptBtn-${modalId}`);

        // 初始化并显示模态框
        const modal = new bootstrap.Modal(modalElement);
        modal.show();

        // 会话ID
        let sessionId = 'req_' + Date.now();
        let finalRequirement = '';

        // 添加消息到聊天框
        function addMessage(role, content, isLoading = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `mb-3 ${role === 'user' ? 'text-end' : ''}`;

            const bubble = document.createElement('div');
            bubble.className = `d-inline-block p-2 rounded ${role === 'user' ? 'bg-primary text-white' : 'bg-white border'}`;
            bubble.style.maxWidth = '80%';
            bubble.style.textAlign = 'left';
            bubble.style.whiteSpace = 'pre-wrap';

            if (isLoading) {
                bubble.innerHTML = '<span class="text-muted"><i class="fas fa-spinner fa-spin"></i> AI思考中...</span>';
                messageDiv.setAttribute('data-loading', 'true');
            } else {
                bubble.textContent = content;
            }

            messageDiv.appendChild(bubble);
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;

            return messageDiv;
        }

        // 发送消息
        async function sendMessage() {
            const userMessage = chatInput.value.trim();
            if (!userMessage) return;

            // 清空第一次的欢迎消息
            if (chatMessages.children.length === 1 && chatMessages.children[0].classList.contains('text-muted')) {
                chatMessages.innerHTML = '';
            }

            // 添加用户消息
            addMessage('user', userMessage);
            chatInput.value = '';
            sendBtn.disabled = true;
            chatInput.disabled = true;

            // 添加加载中的AI消息
            const loadingMsg = addMessage('assistant', '', true);

            try {
                const response = await fetch(`/aicode/api/projects/${currentProjectId}/chatai`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        session_id: sessionId,
                        message: userMessage,
                        enable_kb: enable_kb,
                        prev_lines: prevLines || '',
                        next_lines : nextLines || '',
                    })
                });

                const result = await response.json();

                // 删除加载消息
                loadingMsg.remove();

                if (result.success) {
                    // 添加AI响应
                    addMessage('assistant', result.message);

                    // 检查是否是最终需求规格
                    if (result.is_final) {
                        finalRequirement = result.message;
                        acceptBtn.classList.remove('d-none');
                    }
                } else {
                    addMessage('assistant', '抱歉，处理失败：' + result.message);
                }
            } catch (error) {
                loadingMsg.remove();
                addMessage('assistant', '抱歉，请求失败，请重试');
            } finally {
                sendBtn.disabled = false;
                chatInput.disabled = false;
                chatInput.focus();
            }
        }

        // 发送按钮点击事件
        sendBtn.addEventListener('click', sendMessage);

        // 回车发送（Ctrl+Enter换行）
        chatInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.ctrlKey && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // 采纳按钮点击事件
        acceptBtn.addEventListener('click', () => {
            modal.hide();
            resolve(finalRequirement);
        });

        // 监听模态框隐藏事件，清理DOM
        modalElement.addEventListener('hidden.bs.modal', () => {
            setTimeout(() => {
                if (modalElement.parentNode) {
                    modalElement.parentNode.removeChild(modalElement);
                }
            }, 300);

            // 如果用户没有采纳，返回空
            if (!finalRequirement) {
                resolve('');
            }
        });
    });
}