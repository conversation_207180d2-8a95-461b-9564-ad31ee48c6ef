/**
 * 任务AI对话相关JavaScript
 */

// 任务AI对话管理模块
window.TaskChatManager = {
    currentProjectId: null,
    currentTaskId: null,
    isLoadingHistory: false,
    hasMoreHistory: true,
    currentOffset: 0,
    initialLoadCount: 20,
    loadMoreCount: 20,
    isWaitingForResponse: false,
    streamingMessageId: null,
    maxMessageNumber: 0,  // 跟踪最大的消息序号
    autoRefreshInterval: null, // 自动刷新定时器

    /**
     * 初始化对话页面
     */
    initChatPage: function(projectId, taskId) {
        this.currentProjectId = projectId;
        this.currentTaskId = taskId;
        this.currentOffset = 0;
        this.hasMoreHistory = true;
        this.isLoadingHistory = false;
        this.isWaitingForResponse = false;
        this.streamingMessageId = null;
        this.maxMessageNumber = 0;  // 初始化最大消息序号

        this.loadInitialHistory();
        this.loadTaskInfo();
        
        // 绑定自动刷新复选框事件
        $('#autoRefreshCheckbox').on('change', (e) => {
            this.toggleAutoRefresh(e.target.checked);
        });
    },

    /**
     * 切换自动刷新功能
     */
    toggleAutoRefresh: function(enabled) {
        if (enabled) {
            // 第一次启动自动刷新时，先执行一次全量刷新
            this.refreshMessage(false);

            // 启动自动刷新，每5秒执行一次增量刷新
            this.autoRefreshInterval = setInterval(() => {
                // 只有在没有等待响应时才执行增量刷新
                if (!this.isWaitingForResponse) {
                    this.refreshMessage(true);  // true表示增量刷新
                }
            }, 5000);
        } else {
            // 停止自动刷新
            if (this.autoRefreshInterval) {
                clearInterval(this.autoRefreshInterval);
                this.autoRefreshInterval = null;
            }
        }
    },

    /**
     * 加载任务信息
     */
    loadTaskInfo: function() {
        if (this.currentTaskId < 0){
            return;
        }
        $.ajax({
            url: `/aicode/api/projects/${this.currentProjectId}/tasks/${this.currentTaskId}`,
            method: 'GET',
            success: (response) => {
                if (response.success && response.task) {
                    const task = response.task;
                    const statusColors = {
                        'pending': 'bg-secondary',
                        'running': 'bg-primary',
                        'completed': 'bg-success',
                        'failed': 'bg-danger',
                        'disabled': 'bg-dark'
                    };

                    const statusTexts = {
                        'pending': '等待中',
                        'running': '运行中',
                        'completed': '已完成',
                        'failed': '失败',
                        'disabled': '已禁用'
                    };

                    $('#taskStatus')
                        .removeClass()
                        .addClass(`badge ${statusColors[task.status] || 'bg-secondary'}`)
                        .text(statusTexts[task.status] || task.status);
                }
            },
            error: () => {
                $('#taskStatus').removeClass().addClass('badge bg-danger').text('获取失败');
            }
        });
    },

    /**
     * 加载初始历史记录（最近的5条）
     */
    loadInitialHistory: function() {
        this.isLoadingHistory = true;

        $.ajax({
            url: `/aicode/api/projects/${this.currentProjectId}/tasks/${this.currentTaskId}/llm-logs`,
            method: 'GET',
            data: {
                limit: this.initialLoadCount,
                offset: 0
            },
            success: (response) => {
                this.isLoadingHistory = false;
                this.handleHistoryResponse(response);
            },
            error: (xhr) => {
                this.isLoadingHistory = false;
                this.handleHistoryError(xhr);
            }
        });
    },

    /**
     * 加载更多历史记录
     */
    loadMoreMessages: function() {
        if (this.isLoadingHistory || !this.hasMoreHistory) {
            return;
        }

        this.isLoadingHistory = true;
        $('#loadMoreBtn')
            .prop('disabled', true)
            .html('<i class="fas fa-spinner fa-spin"></i> 加载中...');

        $.ajax({
            url: `/aicode/api/projects/${this.currentProjectId}/tasks/${this.currentTaskId}/llm-logs`,
            method: 'GET',
            data: {
                limit: this.loadMoreCount,
                offset: this.currentOffset
            },
            success: (response) => {
                this.isLoadingHistory = false;
                this.handleMoreHistoryResponse(response);
            },
            error: (xhr) => {
                this.isLoadingHistory = false;
                this.handleMoreHistoryError(xhr);
            }
        });
    },

    /**
     * 处理初始历史记录响应
     */
    handleHistoryResponse: function(response) {
        if (response.success && response.logs) {
            this.currentOffset = response.logs.length;
            this.hasMoreHistory = response.has_more || false;

            this.renderMessages(response.logs);
            this.updateStats(response.logs.length);

            // 更新加载更多按钮显示状态
            this.updateLoadMoreButton();

            // 滚动到底部显示最新消息
            this.scrollToBottom();
        } else {
            this.showEmptyState();
        }
    },

    /**
     * 处理更多历史记录响应
     */
    handleMoreHistoryResponse: function(response) {
        $('#loadMoreBtn')
            .prop('disabled', false)
            .html('<i class="fas fa-history"></i> 显示更多历史记录');

        if (response.success && response.logs) {
            this.currentOffset += response.logs.length;
            this.hasMoreHistory = response.has_more || false;

            // 保存当前滚动位置和第一个可见消息
            const container = document.getElementById('chatContainer');
            const scrollHeight = container.scrollHeight;
            const scrollTop = container.scrollTop;
            const firstVisibleElement = this.getFirstVisibleMessage();

            // 在顶部插入新消息
            this.prependMessages(response.logs);
            this.updateStats($('#messagesList .message-entry').length);

            // 更新加载更多按钮
            this.updateLoadMoreButton();

            // 智能滚动：如果之前在底部，保持到底部；否则保持第一个可见消息的位置
            if (this.isScrolledToBottom()) {
                this.scrollToBottom();
            } else {
                // 计算新的滚动位置以保持第一个可见消息在相同位置
                const newScrollHeight = container.scrollHeight;
                const heightDiff = newScrollHeight - scrollHeight;
                container.scrollTop = scrollTop + heightDiff;
            }
            
            // 更新所有消息的序号
            this.updateMessageNumbers();
        }
    },

    /**
     * 获取第一个可见的消息元素
     */
    getFirstVisibleMessage: function() {
        const container = document.getElementById('chatContainer');
        const containerTop = container.scrollTop;
        const messageElements = container.querySelectorAll('.message-entry');

        for (let element of messageElements) {
            const elementTop = element.offsetTop;
            if (elementTop >= containerTop) {
                return element;
            }
        }
        return null;
    },

    /**
     * 检查是否滚动到底部
     */
    isScrolledToBottom: function() {
        const container = document.getElementById('chatContainer');
        return container.scrollTop + container.clientHeight >= container.scrollHeight - 10;
    },

    /**
     * 处理历史记录错误
     */
    handleHistoryError: function(xhr) {
        const response = xhr.responseJSON || {};
        $('#chatContainer').html(`
            <div class="text-center text-danger py-5">
                <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                <div>加载对话历史失败: ${response.error || response.message || '未知错误'}</div>
                <button class="btn btn-outline-primary mt-3" onclick="TaskChatManager.loadInitialHistory()">重试</button>
            </div>
        `);
    },

    /**
     * 处理更多历史记录错误
     */
    handleMoreHistoryError: function(xhr) {
        const response = xhr.responseJSON || {};
        Utils.showAlert('加载更多历史记录失败: ' + (response.error || response.message || '未知错误'), 'danger');

        $('#loadMoreBtn')
            .prop('disabled', false)
            .html('<i class="fas fa-history"></i> 显示更多历史记录');
    },

    /**
     * 更新加载更多按钮状态
     */
    updateLoadMoreButton: function() {
        if (this.hasMoreHistory) {
            $('#loadMoreSection').show();
        } else {
            $('#loadMoreSection').hide();
        }
    },

    /**
     * 渲染消息列表
     */
    renderMessages: function(logs) {
        const container = $('#messagesList');
        container.empty();

        if (!logs || logs.length === 0) {
            container.html(`
                <div class="text-center text-muted py-5">
                    <i class="fas fa-comments fa-2x mb-3"></i>
                    <div>暂无消息记录</div>
                </div>
            `);
            this.updateStats(0);
            return;
        }

        let html = '';
        logs.forEach((log, index) => {
            // 使用后端提供的序号，如果没有则使用index+1
            const messageNumber = log.sequence_number || (index + 1);
            // 更新最大消息序号
            if (messageNumber > this.maxMessageNumber) {
                this.maxMessageNumber = messageNumber;
            }
            html += this.createMessageHtmlWithNumber(log, messageNumber);
        });

        container.html(html);
        this.setupContentToggles();
        this.updateStats(logs.length);
        this.scrollToBottom();
    },

    /**
     * 在消息列表顶部插入历史消息
     */
    prependMessages: function(logs) {
        if (!logs || logs.length === 0) return;

        const container = $('#messagesList');
        let html = '';

        // 从前端消息序号开始，确保历史消息序号正确
        const currentMessageCount = container.find('.message-entry').length;

        // 反向遍历日志，因为我们是从顶部插入
        for (let i = logs.length - 1; i >= 0; i--) {
            const log = logs[i];
            // 对于历史消息，优先使用后端提供的序号，否则使用计算的序号
            const messageNumber = log.sequence_number || (currentMessageCount - (logs.length - i));
            // 更新最大消息序号
            if (messageNumber > this.maxMessageNumber) {
                this.maxMessageNumber = messageNumber;
            }
            html = this.createMessageHtmlWithNumber(log, messageNumber) + html;
        }

        container.prepend(html);
        this.setupContentToggles();
        this.updateStats(container.find('.message-entry').length);
    },

    /**
     * 在消息列表底部追加新消息（用于增量刷新）
     */
    appendNewMessages: function(logs) {
        if (!logs || logs.length === 0) return;

        const container = $('#messagesList');
        let html = '';

        // 记录滚动状态
        const wasAtBottom = this.isScrolledToBottom();

        // 正向遍历日志，因为我们是从底部追加
        for (let i = 0; i < logs.length; i++) {
            const log = logs[i];
            // 使用后端提供的序号
            const messageNumber = log.sequence_number;

            // 更新最大消息序号
            if (messageNumber > this.maxMessageNumber) {
                this.maxMessageNumber = messageNumber;
            }

            html += this.createMessageHtmlWithNumber(log, messageNumber);
        }

        // 追加到消息列表底部
        container.append(html);
        this.setupContentToggles();
        this.updateStats(container.find('.message-entry').length);

        // 如果之前在底部，追加后自动滚动到底部；否则保持当前位置
        if (wasAtBottom) {
            this.scrollToBottom();
        }
    },

    /**
     * 设置内容折叠展开功能
     */
    setupContentToggles: function() {
        const that = this;

        $('.message-content').each(function() {
            const $content = $(this);
            const text = $content.text();
            const lines = text.split('\n');
            const chars = text.length;

            // 检查是否需要折叠：超过5行或超过200字符
            if (lines.length > 5 || chars > 200) {
                const messageEntry = $content.closest('.message-entry');
                const messageId = messageEntry.attr('data-message-id') || 'msg-' + Date.now() + '-' + Math.random();
                messageEntry.attr('data-message-id', messageId);

                // 添加折叠状态
                $content.addClass('collapsed');

                // 添加展开/折叠按钮
                if (!messageEntry.find('.toggle-content-btn').length) {
                    const toggleBtn = $(`
                        <button type="button" class="toggle-content-btn"
                                onclick="TaskChatManager.toggleContent('${messageId}')">
                            <i class="fas fa-chevron-down"></i> 展开全文
                        </button>
                    `);
                    $content.after(toggleBtn);
                }
            }
        });
    },

    /**
     * 切换内容展开/折叠状态
     */
    toggleContent: function(messageId) {
        const messageEntry = $(`.message-entry[data-message-id="${messageId}"]`);
        const content = messageEntry.find('.message-content');
        const toggleBtn = messageEntry.find('.toggle-content-btn');

        if (messageEntry.hasClass('expanded')) {
            // 折叠内容
            messageEntry.removeClass('expanded');
            content.addClass('collapsed');
            toggleBtn.html('<i class="fas fa-chevron-down"></i> 展开全文');
        } else {
            // 展开内容
            messageEntry.addClass('expanded');
            content.removeClass('collapsed');
            toggleBtn.html('<i class="fas fa-chevron-up"></i> 收起');
        }
    },

    /**
     * 创建消息HTML
     */
    createMessageHtml: function(log, index) {
        const messageTypes = {
            'Request': { class: 'message-user', icon: 'fas fa-user text-primary', label: '用户' },
            'Assistant': { class: 'message-assistant', icon: 'fas fa-robot text-success', label: 'AI助手' },
            'User': { class: 'message-user', icon: 'fas fa-user text-info', label: '用户' },
            'Result': { class: 'message-system', icon: 'fas fa-check-circle text-warning', label: '结果' },
            'Stream': { class: 'message-streaming', icon: 'fas fa-stream text-secondary', label: '流式响应' }
        };

        const messageType = messageTypes[log.log_type] || messageTypes['Assistant'];
        const content = this.formatMessageContent(log.content);
        const messageId = this.generateMessageId(log, index);
        // 使用后端提供的序号，如果没有则使用传入的index作为消息序号
        const messageNumber = log.sequence_number || (index + 1);

        return `
            <div class="message-entry ${messageType.class} p-3 mb-3 rounded" data-message-id="${messageId}">
                <div class="d-flex align-items-center mb-2">
                    <i class="${messageType.icon} me-2"></i>
                    <strong class="me-2">${messageType.label}</strong>
                    <span class="badge bg-secondary me-2">${log.progress || ''}</span>
                    <small class="message-time">${log.timestamp} - ${messageNumber}</small>
                </div>
                <div class="message-content">${content}</div>
            </div>
        `;
    },

    /**
     * 创建带指定序号的消息HTML
     */
    createMessageHtmlWithNumber: function(log, messageNumber) {
        const messageTypes = {
            'Request': { class: 'message-user', icon: 'fas fa-user text-primary', label: '用户' },
            'Assistant': { class: 'message-assistant', icon: 'fas fa-robot text-success', label: 'AI助手' },
            'User': { class: 'message-user', icon: 'fas fa-user text-info', label: '用户' },
            'Result': { class: 'message-system', icon: 'fas fa-check-circle text-warning', label: '结果' },
            'Stream': { class: 'message-streaming', icon: 'fas fa-stream text-secondary', label: '流式响应' }
        };

        const messageType = messageTypes[log.log_type] || messageTypes['Assistant'];
        const content = this.formatMessageContent(log.content);
        const messageId = this.generateMessageId(log, messageNumber);

        return `
            <div class="message-entry ${messageType.class} p-3 mb-3 rounded" data-message-id="${messageId}">
                <div class="d-flex align-items-center mb-2">
                    <i class="${messageType.icon} me-2"></i>
                    <strong class="me-2">${messageType.label}</strong>
                    <span class="badge bg-secondary me-2">${log.progress || ''}</span>
                    <small class="message-time">${log.timestamp} - ${messageNumber}</small>
                </div>
                <div class="message-content">${content}</div>
            </div>
        `;
    },

    /**
     * 生成唯一消息ID
     */
    generateMessageId: function(log, index) {
        const baseId = log.timestamp || Date.now();
        const contentHash = this.simpleHash(log.content || '');
        return `msg-${baseId}-${contentHash}-${index}`;
    },

    /**
     * 简单的字符串哈希函数
     */
    simpleHash: function(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return Math.abs(hash).toString(36);
    },

    /**
     * 格式化消息内容
     */
    formatMessageContent: function(content) {
        // 处理换行符
        let formatted = content.replace(/↵/g, '\n');

        // 转义HTML
        formatted = Utils.escapeHtml(formatted);

        // 处理代码块（简单版本）
        formatted = formatted.replace(/```([^`]+)```/g, '<pre class="bg-light p-2 rounded"><code>$1</code></pre>');

        // 处理行内代码
        formatted = formatted.replace(/`([^`]+)`/g, '<code class="bg-light px-1 rounded">$1</code>');

        // 处理换行
        formatted = formatted.replace(/\n/g, '<br>');

        return formatted;
    },

    /**
     * 显示空状态
     */
    showEmptyState: function() {
        $('#messagesList').html(`
            <div class="text-center text-muted py-5">
                <i class="fas fa-comments fa-3x mb-3"></i>
                <div>暂无对话记录</div>
                <small class="text-muted">开始与AI助手对话吧！</small>
            </div>
        `);
        $('#loadMoreSection').hide();
    },

    /**
     * 更新统计信息
     */
    updateStats: function(count) {
        $('#totalMessagesCount').text(count);
    },

    /**
     * 滚动到底部
     */
    scrollToBottom: function() {
        const container = document.getElementById('chatContainer');
        setTimeout(() => {
            container.scrollTop = container.scrollHeight;
        }, 100);
    },

    /**
     * 发送消息
     */
    sendMessage: function() {        
        // 一旦有消息发送，就不能刷新页面，否则消息交互会中断
        $('#refreshBtn').prop('disabled', true);
        // 禁用自动刷新
        $('#autoRefreshCheckbox').prop('checked', false);
        this.toggleAutoRefresh(false);

        const input = $('#messageInput');
        const message = input.val().trim();

        if (!message || this.isWaitingForResponse) {
            return;
        }

        // 禁用输入和发送按钮
        this.setWaitingState(true);

        // 添加用户消息到界面
        this.addUserMessage(message);

        // 清空输入框
        input.val('');
        $('#charCount').text('0/2000');

        // 发送到后端
        this.sendChatRequest(message);
    },
    
    /**
     * 增量加载新日志
     */
    loadIncrementalLogs: function() {
        // 如果当前没有任何消息，则执行全量加载
        if (this.maxMessageNumber === 0) {
            this.refreshMessage(false);
            return;
        }

        $.ajax({
            url: `/aicode/api/projects/${this.currentProjectId}/tasks/${this.currentTaskId}/llm-logs`,
            method: 'GET',
            data: {
                since_sequence: this.maxMessageNumber  // 从当前最大序号之后获取
            },
            success: (response) => {
                if (response.success && response.logs && response.logs.length > 0) {
                    // 追加新日志到消息列表
                    this.appendNewMessages(response.logs);
                }
                // 如果没有新日志，则静默返回，不做任何操作
            },
            error: (xhr) => {
                // 增量加载失败时，不显示错误，避免干扰用户
                console.error('增量加载日志失败:', xhr);
            }
        });
    },

    /**
     * 刷新消息
     * @param {boolean} incremental - 是否增量刷新，true为增量刷新，false或undefined为全量刷新
     */
    refreshMessage: function(incremental = false) {
        if (incremental) {
            // 增量刷新：从当前最大序号之后获取新日志
            this.loadIncrementalLogs();
        } else {
            // 全量刷新：清空并重新加载所有日志
            // 清空消息列表
            $('#messagesList').empty();

            // 重置状态
            this.currentOffset = 0;
            this.hasMoreHistory = true;
            this.isLoadingHistory = false;
            this.maxMessageNumber = 0;  // 重置最大消息序号

            // 重新加载初始历史记录
            this.loadInitialHistory();
        }

        // 更新任务信息
        this.loadTaskInfo();
    },

    /**
     * 设置等待状态
     */
    setWaitingState: function(isWaiting) {
        this.isWaitingForResponse = isWaiting;
        $('#sendBtn').prop('disabled', isWaiting);
        $('#messageInput').prop('disabled', isWaiting);
        
        // 当等待响应时，禁用自动刷新复选框
        if (isWaiting) {
            $('#autoRefreshCheckbox').prop('disabled', true);
        } else {
            $('#autoRefreshCheckbox').prop('disabled', false);
        }

        if (isWaiting) {
            $('#connectionStatus').text('思考中...').removeClass('text-success').addClass('text-warning');
            $('#connectionStatusIndicator').removeClass('status-connected').addClass('status-typing');

            // 重置进度消息ID
            this.progressMessageId = null;
        } else {
            $('#connectionStatus').text('已连接').removeClass('text-warning').addClass('text-success');
            $('#connectionStatusIndicator').removeClass('status-typing').addClass('status-connected');
        }
    },

    /**
     * 添加用户消息
     */
    addUserMessage: function(message) {
        const messageId = 'user-' + Date.now();
        // 计算消息序号 - 在最大消息序号基础上加1
        this.maxMessageNumber += 1;
        const messageNumber = this.maxMessageNumber;
        
        const html = `
            <div class="message-entry message-user p-3 mb-3 rounded message-new" data-message-id="${messageId}">
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-user text-primary me-2"></i>
                    <strong class="me-2">用户</strong>
                    <small class="message-time">${new Date().toLocaleString()} - ${messageNumber}</small>
                </div>
                <div class="message-content">${this.formatMessageContent(message)}</div>
            </div>
        `;

        $('#messagesList').append(html);
        this.setupContentToggles();
        this.scrollToBottom();
        this.updateStats($('#messagesList .message-entry').length);
    },

    /**
     * 添加流式响应占位符
     */
    addStreamingPlaceholder: function() {
        const placeholderId = 'streaming-' + Date.now();
        this.streamingMessageId = placeholderId;
        // 计算消息序号 - 在最大消息序号基础上加1
        this.maxMessageNumber += 1;
        const messageNumber = this.maxMessageNumber;

        const html = `
            <div class="message-entry message-streaming p-3 mb-3 rounded message-new" id="${placeholderId}" data-message-id="${placeholderId}">
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-robot text-success me-2"></i>
                    <strong class="me-2">AI助手</strong>
                    <span class="typing-indicator loading-dots">正在思考</span>
                    <small class="message-time">${new Date().toLocaleString()} - ${messageNumber}</small>
                </div>
                <div class="message-content">
                    <span class="text-muted">AI助手正在生成回复...</span>
                </div>
            </div>
        `;

        $('#messagesList').append(html);
        this.setupContentToggles();
        this.scrollToBottom();
        this.updateStats($('#messagesList .message-entry').length);
    },

    /**
     * 添加独立的流式响应消息框
     */
    addStreamingMessage: function(title, content) {        
        const messageId = 'streaming-msg-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
        // 计算消息序号 - 在最大消息序号基础上加1
        this.maxMessageNumber += 1;
        const messageNumber = this.maxMessageNumber;
        
        const html = `
            <div class="message-entry message-assistant p-3 mb-3 rounded" data-message-id="${messageId}">
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-robot text-success me-2"></i>
                    <strong class="me-2">AI助手</strong>
                    <span class="badge bg-secondary me-2">${title}</span>
                    <small class="message-time">${new Date().toLocaleString()} - ${messageNumber}</small>
                </div>
                <div class="message-content">${this.formatMessageContent(content)}</div>
            </div>
        `;

        // 在流式占位符之前插入新消息
        if (this.streamingMessageId) {
            const placeholder = $(`#${this.streamingMessageId}`);
            $(html).insertBefore(placeholder);
        } else {
            $('#messagesList').append(html);
        }
        
        this.setupContentToggles();
        this.scrollToBottom();
        this.updateStats($('#messagesList .message-entry').length);
    },

    /**
     * 完成流式响应
     */
    completeStreamingResponse: function(finalContent) {
        // 如果消息内容为空，则移除占位符并直接返回
        if (!finalContent || finalContent.trim() === '') {
            this.removeStreamingPlaceholder();
            return;
        }
        
        if (!this.streamingMessageId) return;

        const placeholder = $(`#${this.streamingMessageId}`);
        if (placeholder.length > 0) {
            // 计算消息序号
            const messageNumber = $('#messagesList .message-entry').length;
            // 替换为完整的助手消息
            placeholder.removeClass('message-streaming').addClass('message-assistant');
            placeholder.find('.message-content').html(this.formatMessageContent(finalContent));
            placeholder.find('.typing-indicator').remove();
            // 添加消息序号到时间戳
            placeholder.find('.message-time').html(`${new Date().toLocaleString()} - ${messageNumber}`);

            // 设置折叠功能
            this.setupContentToggles();
        }

        this.streamingMessageId = null;
        this.updateStats($('#messagesList .message-entry').length);
    },

    /**
     * 移除流式响应占位符
     */
    removeStreamingPlaceholder: function() {
        if (this.streamingMessageId) {
            $(`#${this.streamingMessageId}`).remove();
            this.streamingMessageId = null;
        }
    },

    /**
     * 发送聊天请求
     */
    sendChatRequest: function(message) {
        // 使用流式聊天 API
        this.sendStreamingChatRequest(message);
    },

    /**
     * 发送流式聊天请求
     */
    sendStreamingChatRequest: async function(message) {
        // 添加流式响应占位符
        this.addStreamingPlaceholder();

        try {
            // 使用fetch来发送POST请求并处理流式响应
            const response = await fetch(`/aicode/api/projects/${this.currentProjectId}/tasks/${this.currentTaskId}/chat`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';
            let accumulatedResponse = '';

            while (true) {
                const { done, value } = await reader.read();
                
                if (done) {
                    TaskChatManager.setWaitingState(false);
                    break;
                }

                // 将新数据添加到缓冲区
                buffer += decoder.decode(value, { stream: true });

                // 按行处理数据
                const lines = buffer.split('\n');
                // 保留最后一个不完整的行在缓冲区中
                buffer = lines.pop() || '';

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.substring(6));
                            switch (data.type) {
                                case 'start':
                                    TaskChatManager.addStreamingMessage('开始', 'AI助手正在思考...');
                                    break;

                                case 'progress':
                                    // 显示进度信息
                                    TaskChatManager.addStreamingMessage(data.title, data.content);
                                    break;

                                case 'complete':
                                    // 显示最终响应
                                    accumulatedResponse = data.response;
                                    TaskChatManager.completeStreamingResponse(accumulatedResponse);
                                    TaskChatManager.setWaitingState(false);
                                    return;

                                case 'error':
                                    TaskChatManager.removeStreamingPlaceholder();
                                    TaskChatManager.showErrorMessage(data.error);
                                    TaskChatManager.setWaitingState(false);
                                    return;
                            }
                        } catch (e) {
                            console.error('解析流式响应数据失败:', e, '行内容:', line);
                        }
                    }
                }
            }
        } catch (error) {
            console.error('流式请求失败:', error);
            TaskChatManager.removeStreamingPlaceholder();
            TaskChatManager.showErrorMessage('发送消息失败: ' + error.message);
            TaskChatManager.setWaitingState(false);
        }
    },


    /**
     * 显示错误消息
     */
    showErrorMessage: function(error) {
        const messageId = 'error-' + Date.now();
        // 计算消息序号
        const messageNumber = $('#messagesList .message-entry').length + 1;
        const html = `
            <div class="message-entry message-system p-3 mb-3 rounded" data-message-id="${messageId}">
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                    <strong class="me-2">错误</strong>
                    <small class="message-time">${new Date().toLocaleString()} - ${messageNumber}</small>
                </div>
                <div class="message-content text-danger">${Utils.escapeHtml(error)}</div>
            </div>
        `;

        $('#messagesList').append(html);
        this.setupContentToggles();
        this.scrollToBottom();
        this.updateStats($('#messagesList .message-entry').length);
    },

    /**
     * 更新所有消息的序号
     */
    updateMessageNumbers: function() {
        // 不再需要重新分配序号，因为每条消息的序号已经由后端提供或前端正确计算
        // 这里保留空函数以维持接口一致性
    }
};