/**
 * 项目管理相关JavaScript
 */

// API模块扩展
API.config = {
    getProviders: function(apiType) {
        var url = '/aicode/api/config/providers';
        if (apiType) {
            url += '?api_type=' + encodeURIComponent(apiType);
        }
        return $.ajax({
            url: url,
            method: 'GET',
            dataType: 'json'
        });
    },
    
    getDataDir: function() {
        return $.ajax({
            url: '/aicode/api/config/data-dir',
            method: 'GET',
            dataType: 'json'
        });
    }
};

// 项目管理模块
window.ProjectManager = {
    providers: [],
    dataDir: '/tmp/projects',  // 默认值

    /**
     * 从Git地址提取仓库名称
     */
    getRepoNameFromUrl: function(gitUrl) {
        if (!gitUrl) {
            return '';
        }
        let cleaned = gitUrl.trim();
        if (!cleaned) {
            return '';
        }
        cleaned = cleaned.replace(/\/+$/, '');
        if (cleaned.includes(':') && !/^https?:\/\//i.test(cleaned) && !/^ssh:\/\//i.test(cleaned)) {
            cleaned = cleaned.split(':').pop();
        }
        const parts = cleaned.split('/');
        let repo = parts.pop() || '';
        repo = repo.replace(/\.git$/i, '');
        return repo;
    },

    /**
     * 根据Git管理开关切换表单字段显示
     */
    toggleGitFormFields: function(prefix, show) {
        const $row = $('#' + prefix + 'GitFieldsRow');
        const $gitUrl = $('#' + prefix + 'ProjectGitUrl');
        const $gitToken = $('#' + prefix + 'ProjectGitToken');
        const $gitBranch = $('#' + prefix + 'ProjectGitBranch');

        $row.toggleClass('d-none', !show);
        $gitUrl.prop('required', show);

        if (!show) {
            $gitUrl.val('');
            $gitToken.val('');
            $gitBranch.val('');
        }
    },

    /**
     * 初始化项目列表页面
     */
    initProjectsPage: function() {
        this.loadDataDir();
        this.loadProviders();
        this.loadProjects();
        this.bindEvents();
    },

    /**
     * 加载数据目录配置
     */
    loadDataDir: function() {
        API.config.getDataDir()
            .done(function(data) {
                ProjectManager.dataDir = data.data_dir;
            })
            .fail(function(xhr) {
                console.error('加载数据目录配置失败:', xhr);
            });
    },

    /**
     * 加载Providers配置
     */
    loadProviders: function() {
        API.config.getProviders('claude')
            .done(function(providers) {
                console.log('加载Providers配置成功:', providers);
                ProjectManager.providers = providers;
                ProjectManager.updateProviderSelects();
                // 保存providers到全局变量，以便其他地方使用
                window.AppConfig.providers = providers;
            })
            .fail(function(xhr) {
                console.error('加载Providers配置失败:', xhr);
                // 如果加载失败，使用默认配置
                ProjectManager.providers = [
                ];
                ProjectManager.updateProviderSelects();
                window.AppConfig.providers = ProjectManager.providers;
            });
    },

    /**
     * 更新Provider选择框
     */
    updateProviderSelects: function() {
        const providerOptions = ProjectManager.providers.map(provider => 
            `<option value="${provider.key}">${provider.name}</option>`
        ).join('');

        $('#createProjectProvider').html(providerOptions);
        $('#editProjectProvider').html(providerOptions);
    },

    /**
     * 加载项目列表
     */
    loadProjects: function() {
        API.projects.list()
            .done(function(response) {
                // 适配新的返回结构：{index_title: "...", projects: [...]}
                const projects = response.projects || response; // 兼容旧格式
                const indexTitle = response.index_title;

                // 如果有index_title，更新页面标题
                if (indexTitle) {
                    document.title = indexTitle + ' - 项目管理';
                }

                ProjectManager.renderProjectsList(projects);
            })
            .fail(function(xhr) {
                const response = xhr.responseJSON || {};
                Utils.showAlert('加载项目列表失败: ' + (response.message || '未知错误'), 'danger');
            });
    },

    /**
     * 渲染项目列表
     */
    renderProjectsList: function(projects) {
        const container = $('#projects-container');
        
        if (projects.length === 0) {
            container.html(`
                <div class="text-center py-5">
                    <i class="fas fa-folder-open fa-5x text-muted mb-4"></i>
                    <h3 class="text-muted">还没有项目</h3>
                    <p class="text-muted mb-4">创建您的第一个项目来开始使用全栈助手</p>
                    <button class="btn btn-primary btn-lg" onclick="ProjectManager.showCreateModal()">
                        <i class="fas fa-plus"></i> 创建项目
                    </button>
                </div>
            `);
            return;
        }

        let html = `
            <div class="card">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 20%">项目名称</th>
                                    <th style="width: 15%">创建时间</th>
                                    <th style="width: 15%">LLM Provider</th>
                                    <th style="width: 15%">项目类型</th>
                                    <th style="width: 10%">Git管理</th>
                                    <th style="width: 25%">工作目录</th>
                                    <th style="width: 20%">操作</th>
                                </tr>
                            </thead>
                            <tbody>
        `;

        projects.forEach(project => {
            html += `
                <tr class="project-row" data-project-id="${project.project_id}">
                    <td>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-folder text-primary me-2"></i>
                            <div>
                                <div class="fw-bold">
                                    <a class="text-decoration-none" style="cursor: pointer; color: #0d6efd;" onclick="ProjectManager.switchToProject('${project.project_id}')">
                                        ${project.name}
                                    </a>
                                </div>
                                <small class="text-muted">
                                    ${project.description ? 
                                        (project.description.length > 50 ? 
                                            project.description.substring(0, 50) + '...' : 
                                            project.description) : 
                                        '暂无描述'}
                                </small>
                            </div>
                        </div>
                    </td>
                    <td>
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            ${Utils.formatDateTime(project.created_at)}
                        </small>
                    </td>
                    <td>${Utils.getProviderBadge(project.provider)}</td>
                    <td>${Utils.getProjectTypeBadge(project.project_type)}</td>
                    <td>${Utils.getGitManagedBadge(project.git_managed)}</td>
                    <td><small class="text-muted">${project.work_dir || ''}</small></td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary"
                                    onclick="ProjectManager.viewDetail('${project.project_id}')"
                                    title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary"
                                    onclick="ProjectManager.showEditModal('${project.project_id}')"
                                    title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button type="button" class="btn btn-outline-danger"
                                    onclick="ProjectManager.deleteProject('${project.project_id}')"
                                    title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                            <button type="button" class="btn btn-outline-info"
                                    onclick="ProjectManager.openProjectApp('${project.project_id}')"
                                    title="访问项目应用">
                                <i class="fas fa-external-link-alt"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });

        html += `
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;

        container.html(html);
    },

    /**
     * 绑定事件
     */
    bindEvents: function() {
        // 创建项目按钮
        $(document).on('click', '#create-project-btn', function() {
            ProjectManager.showCreateModal();
        });

        // 刷新按钮
        $(document).on('click', '#refresh-projects-btn', function() {
            ProjectManager.loadProjects();
        });
    },

    /**
     * 显示创建项目模态框
     */
    showCreateModal: function() {
        const form = document.getElementById('createProjectForm');
        if (form) {
            form.reset();
        }

        // 重置提交按钮状态，确保不是"创建中..."状态
        const submitBtn = $('#createProjectModal .btn-primary');
        if (submitBtn.data('original-html')) {
            Utils.setButtonLoading(submitBtn, false);
        } else {
            submitBtn.prop('disabled', false).html('创建项目');
        }

        const $modal = $('#createProjectModal');
        const $nameInput = $('#createProjectName');
        const $gitManagedSelect = $('#createProjectGitManaged');
        const $gitUrlInput = $('#createProjectGitUrl');
        const $workDirInput = $('#createProjectWorkDir');

        $workDirInput.data('autoFilled', false);

        const updateWorkDir = (fromGitField = false) => {
            const gitManaged = parseInt($gitManagedSelect.val(), 10) || 0;
            const currentValue = ($workDirInput.val() || '').trim();
            const autoFilled = $workDirInput.data('autoFilled');

            const shouldUpdate =
                !currentValue ||
                autoFilled ||
                (gitManaged !== 0 && fromGitField);

            if (!shouldUpdate) {
                return;
            }

            let nextValue = '';
            if (gitManaged === 0) {
                const name = $nameInput.val().trim();
                if (name) {
                    nextValue = `${ProjectManager.dataDir}/projects/${name.replace(/\s+/g, '_')}`;
                }
            } else {
                const repoName = ProjectManager.getRepoNameFromUrl($gitUrlInput.val());
                if (repoName) {
                    nextValue = `${ProjectManager.dataDir}/projects/${repoName}`;
                }
            }

            if (nextValue) {
                $workDirInput.val(nextValue);
                $workDirInput.data('autoFilled', true);
            }
        };

        const toggleGitFields = () => {
            const gitManaged = parseInt($gitManagedSelect.val(), 10) || 0;
            ProjectManager.toggleGitFormFields('create', gitManaged !== 0);
            updateWorkDir(true);
        };

        $workDirInput.off('input').on('input', function() {
            $(this).data('autoFilled', false);
        });

        $nameInput.off('input').on('input', function() {
            const gitManaged = parseInt($gitManagedSelect.val(), 10) || 0;
            if (gitManaged === 0) {
                updateWorkDir();
            }
        });

        $gitManagedSelect.off('change').on('change', function() {
            toggleGitFields();
        });

        $gitUrlInput.off('input').on('input', function() {
            updateWorkDir(true);
        });

        toggleGitFields();
        updateWorkDir();
        $modal.modal('show');
    },

    /**
     * 提交创建项目
     */
    submitCreate: function() {
        const formData = {
            name: $('#createProjectName').val().trim(),
            work_dir: $('#createProjectWorkDir').val().trim(),
            description: $('#createProjectDescription').val().trim(),
            provider: $('#createProjectProvider').val(),
            project_type: $('#createProjectTaskType').val(),
            git_managed: parseInt($('#createProjectGitManaged').val()) || 0,
            exclude_patterns: $('#createExcludePatterns').val().trim(),
            include_patterns: $('#createIncludePatterns').val().trim(),
            git_url: $('#createProjectGitUrl').val().trim(),
            git_access_token: $('#createProjectGitToken').val().trim(),
            git_branch: $('#createProjectGitBranch').val().trim()
        };

        // 检查必填字段
        if (!formData.name) {
            Utils.showAlert('请输入项目名称', 'warning');
            return;
        }

        if (!formData.work_dir) {
            Utils.showAlert('请选择或输入工作目录', 'warning');
            return;
        }

        const submitBtn = $('#createProjectModal .btn-primary');
        Utils.setButtonLoading(submitBtn, true, '创建中...');

        API.projects.create(formData)
            .done(function(response) {
                if (response.success) {
                    Utils.showAlert('项目创建成功！', 'success');
                    $('#createProjectModal').modal('hide');
                    setTimeout(() => {
                        ProjectManager.loadProjects();
                        // 重新加载主页的项目选择器
                        ProjectManager.reloadIndexProjectSelector();
                    }, 1000);
                } else {
                    Utils.showAlert('项目创建失败: ' + response.message, 'danger');
                }
            })
            .fail(function(xhr) {
                const response = xhr.responseJSON || {};
                Utils.showAlert('项目创建失败: ' + (response.message || '未知错误'), 'danger');
            })
            .always(function() {
                Utils.setButtonLoading(submitBtn, false);
            });
    },

    /**
     * 查看项目详情
     */
    viewDetail: function(projectId) {
        API.projects.get(projectId)
            .done(function(project) {
                const descriptionHtml = project.description
                    ? Utils.escapeHtml(project.description).replace(/\n/g, '<br>')
                    : '<span class="text-muted">暂无描述</span>';
                const gitManaged = parseInt(project.git_managed, 10) || 0;
                const hasGit = gitManaged !== 0;
                const gitInfoHtml = hasGit ? `
                    <div class="row g-3 mb-3">
                        <div class="col-md-4">
                            <div class="border rounded p-3 bg-light h-100">
                                <div class="text-muted small mb-1">Git地址</div>
                                <div class="text-break">${Utils.escapeHtml(project.git_url || '-')}</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="border rounded p-3 bg-light h-100">
                                <div class="text-muted small mb-1">Access Token</div>
                                <div>${project.git_access_token ? '已配置' : '未设置'}</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="border rounded p-3 bg-light h-100">
                                <div class="text-muted small mb-1">当前分支</div>
                                <div class="text-break">${Utils.escapeHtml(project.git_branch || '-')}</div>
                            </div>
                        </div>
                    </div>
                ` : '';

                const totalTasks = project.total_tasks || 0;
                const completedTasks = project.completed_tasks || 0;
                const completionRate = totalTasks > 0
                    ? Math.round(completedTasks / totalTasks * 100)
                    : 0;

                const content = `
                    <div class="row g-3 mb-3">
                        <div class="col-md-4">
                            <div class="border rounded p-3 bg-light h-100">
                                <div class="text-muted small mb-1">项目名称</div>
                                <div class="fw-semibold text-break">${Utils.escapeHtml(project.name || '-')}</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="border rounded p-3 bg-light h-100">
                                <div class="text-muted small mb-1">项目类型</div>
                                <div>${Utils.getProjectTypeBadge(project.project_type)}</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="border rounded p-3 bg-light h-100">
                                <div class="text-muted small mb-1">Git管理</div>
                                <div>${Utils.getGitManagedBadge(project.git_managed)}</div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="text-muted small mb-1">项目描述</div>
                        <div class="border rounded p-3 bg-white">${descriptionHtml}</div>
                    </div>
                    ${gitInfoHtml}
                    <div class="row g-3 mb-3">
                        <div class="col-md-4">
                            <div class="border rounded p-3 bg-light h-100">
                                <div class="text-muted small mb-1">工作目录</div>
                                <div class="text-break">${Utils.escapeHtml(project.work_dir || '-')}</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="border rounded p-3 bg-light h-100">
                                <div class="text-muted small mb-1">排除模式</div>
                                <div>${project.exclude_patterns ? Utils.escapeHtml(project.exclude_patterns) : '无'}</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="border rounded p-3 bg-light h-100">
                                <div class="text-muted small mb-1">包含模式</div>
                                <div>${project.include_patterns ? Utils.escapeHtml(project.include_patterns) : '无'}</div>
                            </div>
                        </div>
                    </div>
                    <div class="row g-3 mb-3">
                        <div class="col-md-4">
                            <div class="border rounded p-3 bg-light h-100">
                                <div class="text-muted small mb-1">LLM Provider</div>
                                <div>${Utils.getProviderBadge(project.provider)}</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="border rounded p-3 bg-light h-100">
                                <div class="text-muted small mb-1">创建时间</div>
                                <div>${Utils.formatDateTime(project.created_at)}</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="border rounded p-3 bg-light h-100">
                                <div class="text-muted small mb-1">更新时间</div>
                                <div>${Utils.formatDateTime(project.updated_at)}</div>
                            </div>
                        </div>
                    </div>
                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="border rounded p-3 bg-white h-100">
                                <div class="text-muted small mb-1">总任务数</div>
                                <div><span class="badge bg-info">${totalTasks}</span></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="border rounded p-3 bg-white h-100">
                                <div class="text-muted small mb-1">已完成</div>
                                <div><span class="badge bg-success">${completedTasks}</span></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="border rounded p-3 bg-white h-100">
                                <div class="text-muted small mb-1">完成率</div>
                                <div>${completionRate}%</div>
                            </div>
                        </div>
                    </div>
                `;
                $('#projectDetailContent').html(content);
                $('#projectDetailModal').modal('show');
                $('#projectDetailModal').data('project-id', projectId);
            })
            .fail(function() {
                Utils.showAlert('获取项目信息失败', 'danger');
            });
    },

    /**
     * 显示编辑项目模态框
     */
    showEditModal: function(projectId) {
        API.projects.get(projectId)
            .done(function(project) {
                // 填充表单
                $('#editProjectId').val(project.project_id);
                $('#editProjectName').val(project.name);
                $('#editProjectDescription').val(project.description || '');
                $('#editProjectWorkDir').val(project.work_dir || '');
                $('#editProjectProvider').val(project.provider || 'local');
                $('#editProjectTaskType').val(project.project_type || '产品迭代');
                const gitManagedValue = parseInt(project.git_managed, 10) || 0;
                $('#editProjectGitManaged').val(gitManagedValue);
                $('#editExcludePatterns').val(project.exclude_patterns || '');
                $('#editIncludePatterns').val(project.include_patterns || '');
                $('#editProjectGitUrl').val(project.git_url || '');
                $('#editProjectGitBranch').val(project.git_branch || '');
                $('#editProjectGitToken').val('');

                ProjectManager.toggleGitFormFields('edit', gitManagedValue !== 0);

                $('#editProjectGitManaged').off('change').on('change', function() {
                    const value = parseInt($(this).val(), 10) || 0;
                    ProjectManager.toggleGitFormFields('edit', value !== 0);
                });
                
                $('#editProjectModal').modal('show');
            })
            .fail(function() {
                Utils.showAlert('获取项目信息失败', 'danger');
            });
    },

    /**
     * 提交编辑项目
     */
    submitEdit: function() {
        const projectId = $('#editProjectId').val();
        const gitManaged = parseInt($('#editProjectGitManaged').val(), 10) || 0;
        const workDir = $('#editProjectWorkDir').val().trim();
        const gitUrl = $('#editProjectGitUrl').val().trim();
        const gitBranch = $('#editProjectGitBranch').val().trim();
        const gitTokenInput = $('#editProjectGitToken').val().trim();

        const formData = {
            name: $('#editProjectName').val().trim(),
            description: $('#editProjectDescription').val().trim(),
            provider: $('#editProjectProvider').val(),
            project_type: $('#editProjectTaskType').val(),
            git_managed: gitManaged,
            exclude_patterns: $('#editExcludePatterns').val().trim(),
            include_patterns: $('#editIncludePatterns').val().trim()
        };

        if (!formData.name) {
            Utils.showAlert('项目名称不能为空', 'warning');
            return;
        }

        if (gitManaged !== 0 && !gitUrl) {
            Utils.showAlert('启用Git管理时需要填写Git地址', 'warning');
            return;
        }

        if (gitManaged === 0 && !workDir) {
            Utils.showAlert('非Git项目必须填写工作目录', 'warning');
            return;
        }

        if (gitManaged === 0 || workDir) {
            formData.work_dir = workDir;
        }

        if (gitManaged === 0) {
            formData.git_url = '';
            formData.git_branch = '';
            formData.git_access_token = '';
        } else {
            formData.git_url = gitUrl;
            formData.git_branch = gitBranch;
            if (gitTokenInput) {
                formData.git_access_token = gitTokenInput;
            }
        }

        const submitBtn = $('#editProjectModal .btn-primary');
        Utils.setButtonLoading(submitBtn, true, '保存中...');

        API.projects.update(projectId, formData)
            .done(function(response) {
                if (response.success) {
                    Utils.showAlert('项目更新成功！', 'success');
                    $('#editProjectModal').modal('hide');
                    setTimeout(() => {
                        ProjectManager.loadProjects();
                    }, 1000);
                } else {
                    Utils.showAlert('项目更新失败: ' + response.message, 'danger');
                }
            })
            .fail(function(xhr) {
                const response = xhr.responseJSON || {};
                Utils.showAlert('项目更新失败: ' + (response.message || '未知错误'), 'danger');
            })
            .always(function() {
                Utils.setButtonLoading(submitBtn, false);
            });
    },

    /**
     * 删除项目
     */
    deleteProject: function(projectId) {
        Utils.confirm('确定要删除这个项目吗？此操作将同时删除项目下的所有任务。', function() {
            API.projects.delete(projectId)
                .done(function(response) {
                    if (response.success) {
                        Utils.showAlert('项目删除成功！', 'success');
                        setTimeout(() => {
                            ProjectManager.loadProjects();
                            // 重新加载主页的项目选择器
                            ProjectManager.reloadIndexProjectSelector();
                        }, 1000);
                    } else {
                        Utils.showAlert('项目删除失败: ' + response.message, 'danger');
                    }
                })
                .fail(function(xhr) {
                    const response = xhr.responseJSON || {};
                    Utils.showAlert('项目删除失败: ' + (response.message || '未知错误'), 'danger');
                });
        });
    },

    /**
     * 打开项目HTML应用
     */
    openProjectApp: function(projectId) {
        // 打开新窗口访问项目应用
        const appUrl = `/aicode/project_app/${projectId}/`;
        window.open(appUrl, '_blank');
    },

    /**
     * 重新加载主页的项目选择器
     */
    reloadIndexProjectSelector: function() {
        // 如果在项目管理iframe中，通知父窗口更新
        try {
            if (window.parent && window.parent !== window) {
                if (typeof window.parent.loadDashboardData === 'function') {
                    window.parent.loadDashboardData();
                }
            }
        } catch (e) {
            // 跨域访问可能会报错，这种情况下直接调用API更新选择器
            console.warn('无法直接访问父窗口，将通过API更新项目选择器:', e);
            this.updateProjectSelectorViaAPI();
        }
    },

    /**
     * 通过API直接更新项目选择器
     */
    updateProjectSelectorViaAPI: function() {
        // 如果在主页中（不是在iframe中），直接调用更新函数
        if (typeof loadDashboardData === 'function' && window.location.pathname.includes('index.html')) {
            loadDashboardData();
        }

        // 如果能访问到父窗口（嵌入在iframe中），尝试调用父窗口的更新函数
        try {
            if (window.parent && window.parent !== window && typeof window.parent.loadDashboardData === 'function') {
                window.parent.loadDashboardData();
            }
        } catch (e) {
            console.warn('无法访问父窗口:', e);
        }
    },

    /**
     * 切换到指定项目
     */
    switchToProject: function(projectId) {
        // 设置父窗口的项目选择器值并触发change事件
        parent.document.getElementById('currentProjectSelector').value = projectId;
        const event = new Event('change');
        parent.document.getElementById('currentProjectSelector').dispatchEvent(event);
    }
};

// 页面加载完成后初始化
$(document).ready(function() {
    //ProjectManager.init();
});
