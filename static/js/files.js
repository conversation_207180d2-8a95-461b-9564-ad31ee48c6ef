/**
 * 文件管理相关JavaScript
 */

// 文件管理模块
window.FileManager = {
    currentProjectId: null,
    currentProject: null,
    selectedFile: null,

    /**
     * 初始化文件页面
     */
    initFilesPage: function(projectId) {
        this.currentProjectId = projectId;
        this.loadProject();
        this.loadFileTree();
        this.bindEvents();
    },

    /**
     * 加载项目信息
     */
    loadProject: function() {
        API.projects.get(this.currentProjectId)
            .done(function(project) {
                FileManager.currentProject = project;
                FileManager.renderProjectInfo(project);
            })
            .fail(function(xhr) {
                const response = xhr.responseJSON || {};
                Utils.showAlert('加载项目信息失败: ' + (response.message || '未知错误'), 'danger');
            });
    },

    /**
     * 渲染项目信息
     */
    renderProjectInfo: function(project) {
        $('#project-name').text(project.name);
        $('#project-id').text(project.project_id);
    },

    /**
     * 加载文件树
     */
    loadFileTree: function() {
        $('#fileTree').html('<div class="loading"><i class="fas fa-spinner fa-spin"></i> 加载中...</div>');
        
        API.files.list(this.currentProjectId)
            .done(function(response) {
                if (response.files) {
                    FileManager.renderFileTree(response.files);
                } else {
                    $('#fileTree').html('<div class="text-muted p-3">暂无文件</div>');
                }
            })
            .fail(function(xhr) {
                const response = xhr.responseJSON || {};
                $('#fileTree').html(`<div class="text-danger p-3">加载失败: ${response.error || '未知错误'}</div>`);
            });
    },

    /**
     * 渲染文件树
     */
    renderFileTree: function(files) {
        const html = this.buildTreeHtml(files);
        $('#fileTree').html(`<ul>${html}</ul>`);
    },

    /**
     * 构建文件树HTML
     */
    buildTreeHtml: function(items) {
        let html = '';
        for (const item of items) {
            if (item.type === 'directory') {
                html += `
                    <li>
                        <div class="file-item" onclick="FileManager.toggleDirectory(this)">
                            <i class="fas fa-folder"></i>
                            <span>${item.name}</span>
                        </div>
                        <ul style="display: none;">${this.buildTreeHtml(item.children || [])}</ul>
                    </li>
                `;
            } else {
                const icon = Utils.getFileIcon(item.name);
                html += `
                    <li>
                        <div class="file-item" onclick="FileManager.selectFile('${item.path}', '${item.name}', this)">
                            <i class="${icon}"></i>
                            <span>${item.name}</span>
                            <small class="text-muted ms-auto">${Utils.formatFileSize(item.size)}</small>
                        </div>
                    </li>
                `;
            }
        }
        return html;
    },

    /**
     * 切换目录展开/收起
     */
    toggleDirectory: function(element) {
        const ul = $(element).next('ul');
        const icon = $(element).find('i');
        
        if (ul.is(':visible')) {
            ul.hide();
            icon.removeClass('fa-folder-open').addClass('fa-folder');
        } else {
            ul.show();
            icon.removeClass('fa-folder').addClass('fa-folder-open');
        }
    },

    /**
     * 选择文件
     */
    selectFile: function(filePath, fileName, element) {
        // 更新选中状态
        $('.file-item').removeClass('selected');
        $(element).addClass('selected');
        
        this.selectedFile = filePath;
        $('#fileName').text(` - ${fileName}`);
        
        // 加载文件内容
        this.loadFileContent(filePath);
    },

    /**
     * 加载文件内容
     */
    loadFileContent: function(filePath) {
        $('#fileContent').html('<div class="loading"><i class="fas fa-spinner fa-spin"></i> 加载中...</div>');
        
        API.files.content(this.currentProjectId, filePath)
            .done(function(response) {
                FileManager.renderFileContent(response);
            })
            .fail(function(xhr) {
                const response = xhr.responseJSON || {};
                $('#fileContent').html(`<div class="text-danger p-3">加载失败: ${response.error || '未知错误'}</div>`);
            });
    },

    /**
     * 渲染文件内容
     */
    renderFileContent: function(fileData) {
        const { content, extension, size, path } = fileData;
        
        // 文件信息
        const fileInfo = `
            <div class="file-info">
                <strong>文件:</strong> ${path} | 
                <strong>大小:</strong> ${Utils.formatFileSize(size)} | 
                <strong>编码:</strong> ${fileData.encoding}
            </div>
        `;
        
        let contentHtml = '';
        
        // 根据文件类型渲染内容
        if (extension === '.md') {
            // Markdown文件
            contentHtml = `
                <div class="markdown-content">
                    ${marked.parse(content)}
                </div>
            `;
        } else if (extension === '.json') {
            // JSON文件
            try {
                const formatted = JSON.stringify(JSON.parse(content), null, 2);
                contentHtml = `<pre class="json-content">${Utils.escapeHtml(formatted)}</pre>`;
            } catch (e) {
                contentHtml = `<textarea class="code-editor" readonly>${content}</textarea>`;
            }
        } else {
            // 其他文本文件
            contentHtml = `<textarea class="code-editor" readonly>${content}</textarea>`;
        }
        
        $('#fileContent').html(fileInfo + contentHtml);
    },

    /**
     * 绑定事件
     */
    bindEvents: function() {
        // 刷新文件树按钮
        $(document).on('click', '#refresh-files-btn', function() {
            FileManager.refreshFileTree();
        });
    },

    /**
     * 刷新文件树
     */
    refreshFileTree: function() {
        this.loadFileTree();
        $('#fileContent').html('<div class="loading"><i class="fas fa-info-circle"></i> 请选择左侧文件进行预览</div>');
        $('#fileName').text('');
        this.selectedFile = null;
    }
};
