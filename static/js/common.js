/**
 * 通用JavaScript函数库
 * 用于前后端分离的全栈助手
 */

// 全局变量
window.AppConfig = {
    apiBase: '/aicode/api',
    currentProject: null,
    currentUser: null
};

// 通用工具函数
window.Utils = {
    /**
     * 显示提示消息 - 增强版
     */
    showAlert: function(message, type = 'info', duration = 3000) {
        let alertHtml = '';
        if (type === 'success') {
            // 为success类型消息添加更突出的样式
            alertHtml = `
            <div class="alert alert-${type}-modern alert-modern alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        } else if (type === 'danger') {
            alertHtml = `
            <div class="alert alert-${type}-modern alert-modern alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        } else if (type === 'warning') {
            alertHtml = `
            <div class="alert alert-${type}-modern alert-modern alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        } else {
            alertHtml = `
            <div class="alert alert-${type}-modern alert-modern alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        }

        // 查找alert容器，如果没有则创建
        let container = $('#alert-container');
        if (container.length === 0) {
            container = $('<div id="alert-container" class="position-fixed" style="top: 20px; right: 20px; z-index: 9999; max-width: 400px;"></div>');
            $('body').append(container);
        }

        const alertDiv = $(alertHtml);
        container.prepend(alertDiv);

        // 添加音效（如果浏览器支持）
        this.playNotificationSound(type);

        // 错误类型的信息延长时间
        if (type == 'warning') {
            duration *= 2;
        } else if (type == 'danger') {
            duration *= 4;
        }
        
        if (duration >0 ) {
            setTimeout(() => {
                alertDiv.fadeOut(300, function() {
                    $(this).alert('close');
                });
            }, duration);
        }
    },

    /**
     * 格式化日期时间
     */
    formatDateTime: function(isoString) {
        if (!isoString) return '';
        const date = new Date(isoString);
        return date.toLocaleString('zh-CN');
    },

    /**
     * 获取状态徽章HTML
     */
    getStatusBadge: function(status) {
        const statusMap = {
            'pending': { class: 'secondary', text: '待处理' },
            'in_progress': { class: 'primary', text: '进行中' },
            'running': { class: 'info', text: '运行中' },
            'completed': { class: 'success', text: '已完成' },
            'failed': { class: 'danger', text: '失败' },
            'cancelled': { class: 'warning', text: '已取消' },
            'stopped': { class: 'warning', text: '已停止' },
            'disable': { class: 'dark', text: '已禁用' }
        };
        const info = statusMap[status] || { class: 'secondary', text: status };
        return `<span class="badge bg-${info.class}">${info.text}</span>`;
    },

    /**
     * 获取优先级徽章HTML
     */
    getPriorityBadge: function(priority) {
        const priorityMap = {
            'high': { class: 'danger', text: '高' },
            'medium': { class: 'warning', text: '中' },
            'low': { class: 'secondary', text: '低' }
        };
        const info = priorityMap[priority] || { class: 'secondary', text: priority };
        return `<span class="badge bg-${info.class}">${info.text}</span>`;
    },

    /**
     * 获取Provider徽章HTML
     */
    getProviderBadge: function(provider) {
        const providerMap = {
            'local': { class: 'secondary', text: '内网' },
            'zhipu': { class: 'info text-dark', text: '智谱' },
            'claude': { class: 'primary', text: 'Claude' }
        };
        const info = providerMap[provider] || { class: 'light text-dark', text: provider };
        return `<span class="badge bg-${info.class}">${info.text}</span>`;
    },

    /**
     * 获取项目类型徽章HTML
     */
    getProjectTypeBadge: function(projectType) {
        const typeMap = {
            'BUG修复': { class: 'warning', text: 'BUG修复' },
            'PMO': { class: 'info', text: 'PMO' },
            '产品迭代': { class: 'success', text: '产品迭代' },
            '代码分析': { class: 'danger', text: '代码分析' }
        };
        const info = typeMap[projectType] || { class: 'secondary', text: projectType };
        return `<span class="badge bg-${info.class}">${info.text}</span>`;
    },

    /**
     * 获取Git管理徽章HTML
     */
    getGitManagedBadge: function(gitManaged) {
        const gitMap = {
            0: { class: 'secondary', text: '无' },
            1: { class: 'info', text: '提交' },
            2: { class: 'success', text: '推送' }
        };
        const info = gitMap[gitManaged] || { class: 'secondary', text: '无' };
        return `<span class="badge bg-${info.class}">${info.text}</span>`;
    },

    /**
     * 获取Git管理文本描述
     */
    getGitManagedText: function(gitManaged) {
        const gitMap = {
            0: '无Git管理',
            1: '提交代码',
            2: '提交代码并推送'
        };
        return gitMap[gitManaged] || '无Git管理';
    },

    /**
     * 格式化文件大小
     */
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    },

    /**
     * 获取文件图标
     */
    getFileIcon: function(filename) {
        const ext = filename.split('.').pop().toLowerCase();
        const iconMap = {
            'js': 'fab fa-js-square text-warning',
            'py': 'fab fa-python text-primary',
            'java': 'fab fa-java text-danger',
            'html': 'fab fa-html5 text-danger',
            'css': 'fab fa-css3-alt text-primary',
            'json': 'fas fa-code text-success',
            'md': 'fab fa-markdown text-dark',
            'txt': 'fas fa-file-alt text-secondary',
            'xml': 'fas fa-code text-warning',
            'yml': 'fas fa-cog text-info',
            'yaml': 'fas fa-cog text-info',
            'c': 'fas fa-code text-primary',
            'cpp': 'fas fa-code text-primary',
            'h': 'fas fa-code text-info'
        };
        return iconMap[ext] || 'fas fa-file text-secondary';
    },

    /**
     * 转义HTML
     */
    escapeHtml: function(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },

    /**
     * 设置按钮加载状态
     */
    setButtonLoading: function(button, loading = true, loadingText = '处理中...') {
        const $btn = $(button);
        if (loading) {
            $btn.data('original-html', $btn.html());
            $btn.prop('disabled', true).html(`<i class="fas fa-spinner fa-spin"></i> ${loadingText}`);
        } else {
            $btn.prop('disabled', false).html($btn.data('original-html') || $btn.html());
        }
    },

    /**
     * 确认对话框
     */
    confirm: function(message, callback) {
        if (confirm(message)) {
            callback();
        }
    }
};

// API调用封装
window.API = {
    /**
     * 通用AJAX请求
     */
    request: function(options) {
        const defaults = {
            method: 'GET',
            contentType: 'application/json',
            dataType: 'json'
        };
        
        const config = $.extend({}, defaults, options);
        
        // 如果是POST/PUT/DELETE且有data，转换为JSON字符串
        if (config.data && typeof config.data === 'object' && 
            ['POST', 'PUT', 'DELETE'].includes(config.method.toUpperCase())) {
            config.data = JSON.stringify(config.data);
        }
        
        return $.ajax(config);
    },

    /**
     * 项目相关API
     */
    projects: {
        list: () => API.request({ url: '/aicode/api/projects' }),
        get: (id) => API.request({ url: `/aicode/api/projects/${id}` }),
        create: (data) => API.request({ url: '/aicode/api/projects', method: 'POST', data }),
        update: (id, data) => API.request({ url: `/aicode/api/projects/${id}`, method: 'PUT', data }),
        delete: (id) => API.request({ url: `/aicode/api/projects/${id}`, method: 'DELETE' }),
        summary: (id) => API.request({ url: `/aicode/api/projects/${id}/summary` }),
        generateTasks: (id, data) => API.request({ url: `/aicode/api/projects/${id}/generate_tasks`, method: 'POST', data }),
        runTasks: (id, data) => API.request({ url: `/aicode/api/projects/${id}/run_tasks`, method: 'POST', data }),
        stopExecution: (id) => API.request({ url: `/aicode/api/projects/${id}/stop_execution`, method: 'POST' }),
        resetTasks: (id) => API.request({ url: `/aicode/api/projects/${id}/tasks/reset`, method: 'POST' })
    },

    /**
     * 任务相关API
     */
    tasks: {
        list: (projectId) => API.request({ url: `/aicode/api/projects/${projectId}/tasks` }),
        get: (projectId, taskId) => API.request({ url: `/aicode/api/projects/${projectId}/tasks/${taskId}` }),
        create: (projectId, data) => API.request({ url: `/aicode/api/projects/${projectId}/tasks`, method: 'POST', data }),
        update: (projectId, taskId, data) => API.request({ url: `/aicode/api/projects/${projectId}/tasks/${taskId}`, method: 'PUT', data }),
        delete: (projectId, taskId) => API.request({ url: `/aicode/api/projects/${projectId}/tasks/${taskId}`, method: 'DELETE' }),
        run: (projectId, taskId, data) => API.request({ url: `/aicode/api/projects/${projectId}/tasks/${taskId}/run`, method: 'POST', data }),
        logs: (projectId, taskId) => API.request({ url: `/aicode/api/projects/${projectId}/tasks/${taskId}/logs` }),
        llmLogs: (projectId, taskId, since) => {
            const url = since ? 
                `/aicode/api/projects/${projectId}/tasks/${taskId}/llm-logs?since=${encodeURIComponent(since)}` :
                `/aicode/api/projects/${projectId}/tasks/${taskId}/llm-logs`;
            return API.request({ url });
        },
        toggle: (projectId, taskId) => API.request({ url: `/aicode/api/projects/${projectId}/tasks/${taskId}/toggle`, method: 'POST' })
    },

    /**
     * 文件相关API
     */
    files: {
        list: (projectId) => API.request({ url: `/aicode/api/projects/${projectId}/files` }),
        content: (projectId, path) => API.request({ url: `/aicode/api/projects/${projectId}/files/content?path=${encodeURIComponent(path)}` }),
        stats: (projectId) => API.request({ url: `/aicode/api/projects/${projectId}/files/stats` })
    }
};

// 页面路由管理
window.Router = {
    /**
     * 导航到指定页面
     */
    navigate: function(path) {
        window.location.href = path;
    },

    /**
     * 获取URL参数
     */
    getParam: function(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    },

    /**
     * 获取路径参数
     */
    getPathParam: function(index) {
        const pathParts = window.location.pathname.split('/').filter(part => part);
        return pathParts[index] || null;
    },

    /**
     * 播放通知音效
     */
    playNotificationSound: function(type) {
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            // 根据类型设置不同的音调
            switch(type) {
                case 'success':
                    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                    oscillator.frequency.setValueAtTime(1000, audioContext.currentTime + 0.1);
                    break;
                case 'danger':
                    oscillator.frequency.setValueAtTime(400, audioContext.currentTime);
                    oscillator.frequency.setValueAtTime(300, audioContext.currentTime + 0.1);
                    break;
                case 'warning':
                    oscillator.frequency.setValueAtTime(600, audioContext.currentTime);
                    break;
                default:
                    oscillator.frequency.setValueAtTime(500, audioContext.currentTime);
            }

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.2);
        } catch (e) {
            // 静默失败，不影响主要功能
        }
    },

    /**
     * 添加页面进入动画
     */
    addPageEnterAnimation: function() {
        document.body.classList.add('page-enter');

        // 为卡片添加延迟动画
        const cards = document.querySelectorAll('.card');
        cards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
            card.classList.add('card-enter');
        });

        // 为侧边栏添加动画
        const sidebar = document.querySelector('.sidebar');
        if (sidebar) {
            sidebar.classList.add('sidebar-enter');
        }

        // 为导航栏添加动画
        const navbar = document.querySelector('.navbar');
        if (navbar) {
            navbar.classList.add('navbar-enter');
        }
    },

    /**
     * 数字动画效果
     */
    animateNumber: function(element, targetNumber, duration = 1000) {
        const startNumber = parseInt(element.textContent) || 0;
        const increment = (targetNumber - startNumber) / (duration / 16);
        let currentNumber = startNumber;

        const timer = setInterval(() => {
            currentNumber += increment;
            if ((increment > 0 && currentNumber >= targetNumber) ||
                (increment < 0 && currentNumber <= targetNumber)) {
                currentNumber = targetNumber;
                clearInterval(timer);
            }
            element.textContent = Math.floor(currentNumber);
        }, 16);
    },

    /**
     * 添加按钮点击波纹效果
     */
    addRippleEffect: function() {
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('btn') || e.target.closest('.btn')) {
                const button = e.target.classList.contains('btn') ? e.target : e.target.closest('.btn');
                const ripple = document.createElement('span');
                const rect = button.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                ripple.classList.add('ripple');

                button.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            }
        });
    }
};

// 页面加载完成后的初始化
$(document).ready(function() {
    // 初始化提示框容器
    if ($('#alert-container').length === 0) {
        $('body').append('<div id="alert-container" class="position-fixed" style="top: 20px; right: 20px; z-index: 9999; max-width: 400px;"></div>');
    }
});

// makrdown编辑器使用的ai进度
function showAIProgress(ai_action) {
    // 添加计时器功能
    let seconds = 0;
    const timerId = setInterval(() => {
        seconds++;
        $('#actionButtons').html(`<span class="text-primary"><i class="fas fa-spinner fa-spin"></i> ${ai_action}中，${seconds}秒...</span>`);
    }, 1000);

    $('#actionButtons').html(`<span class="text-primary"><i class="fas fa-spinner fa-spin"></i> ${ai_action}中，${seconds}秒...</span>`);
    return timerId
}