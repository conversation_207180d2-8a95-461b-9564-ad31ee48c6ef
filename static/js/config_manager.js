/**
 * 配置管理相关JavaScript
 */

// API模块扩展
API.configManager = {
    /**
     * 获取所有配置
     */
    getAllConfig: function() {
        return $.ajax({
            url: '/aicode/api/config/all',
            method: 'GET',
            dataType: 'json'
        });
    },

    /**
     * 保存配置
     */
    saveConfig: function(configData) {
        return $.ajax({
            url: '/aicode/api/config/save',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(configData)
        });
    }
};

// 配置管理模块
window.ConfigManager = {
    configData: {},
    providers: {},
    otherConfig: {},

    /**
     * 初始化配置管理页面
     */
    init: function() {
        this.loadConfig();
        this.bindEvents();
    },

    /**
     * 加载配置
     */
    loadConfig: function() {
        API.configManager.getAllConfig()
            .done((function(data) {
                console.log('加载配置成功:', data);
                this.configData = data;
                this.parseConfigData();
                this.renderProviders();
                this.renderOtherConfig();
            }).bind(this))
            .fail((function(xhr) {
                console.error('加载配置失败:', xhr);
                Utils.showAlert('加载配置失败: ' + (xhr.responseJSON?.message || '未知错误'), 'danger');
            }).bind(this));
    },

    /**
     * 解析配置数据
     */
    parseConfigData: function() {
        console.log('开始解析配置数据:', this.configData);

        this.providers = {};
        this.otherConfig = {};

        for (const [key, value] of Object.entries(this.configData)) {
            console.log(`解析配置项: ${key} = ${value}`);

            if (key.startsWith('PROVIDER_')) {
                // 解析 Provider 配置
                const parts = key.split('_');
                if (parts.length >= 3) {
                    const providerName = parts[1];
                    const configKey = parts.slice(2).join('_');

                    console.log(`Provider配置: ${providerName}.${configKey} = ${value}`);

                    if (!this.providers[providerName]) {
                        this.providers[providerName] = {
                            name: providerName,
                            configs: {}
                        };
                    }

                    this.providers[providerName].configs[configKey] = value;
                } else {
                    console.warn('Provider配置格式不正确:', key);
                }
            } else {
                // 其他配置
                console.log(`其他配置: ${key} = ${value}`);
                this.otherConfig[key] = value;
            }
        }

        console.log('解析后的Providers:', this.providers);
        console.log('解析后的其他配置:', this.otherConfig);
    },

    /**
     * 渲染 Providers
     */
    renderProviders: function() {
        const container = $('#providersContainer');
        container.empty();

        if (Object.keys(this.providers).length === 0) {
            container.html(`
                <div class="text-center py-4">
                    <i class="fas fa-info-circle fa-2x text-muted mb-2"></i>
                    <p class="text-muted">暂无 LLM Provider 配置</p>
                </div>
            `);
            return;
        }

        for (const [providerName, provider] of Object.entries(this.providers)) {
            const card = $(`
                <div class="card provider-card mb-3" data-provider="${providerName}">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-cloud me-2"></i>
                            ${provider.configs.NAME || providerName}
                            <span class="badge bg-secondary ms-2">${providerName}</span>
                        </h6>
                        <div>
                            <button class="btn btn-sm btn-outline-danger remove-provider-btn" data-provider="${providerName}">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            ${this.renderProviderConfigs(providerName, provider.configs)}
                        </div>
                    </div>
                </div>
            `);
            container.append(card);
        }
    },

    /**
     * 渲染单个 Provider 的配置项
     */
    renderProviderConfigs: function(providerName, configs) {
        let html = '';
        const configOrder = ['NAME', 'API_TYPE', 'BASE_URL', 'AUTH_TOKEN', 'MODEL', 'API_KEY', 'MAX_TOKENS', 'FALLBACK'];
        
        // 按照指定顺序排列配置项
        for (const key of configOrder) {
            if (configs.hasOwnProperty(key)) {
                html += this.renderConfigItem(providerName, key, configs[key], true);
            }
        }
        
        // 添加剩余的配置项
        for (const [key, value] of Object.entries(configs)) {
            if (!configOrder.includes(key)) {
                html += this.renderConfigItem(providerName, key, value, true);
            }
        }
        
        return html;
    },

    /**
     * 渲染单个配置项
     */
    renderConfigItem: function(providerName, key, value, isProviderConfig = false) {
        // HTML转义函数
        const escapeHtml = (text) => {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        };

        // 转义值以防止XSS
        const escapedValue = escapeHtml(value || '');
        const escapedKey = escapeHtml(key || '');

        let dataAttr = '';
        if (isProviderConfig && providerName) {
            const escapedProviderName = escapeHtml(providerName);
            dataAttr = `data-provider="${escapedProviderName}" data-key="${escapedKey}"`;
        } else if (!isProviderConfig) {
            dataAttr = `data-key="${escapedKey}"`;
        }

        console.log('渲染配置项:', {
            providerName: providerName,
            key: key,
            value: value,
            isProviderConfig: isProviderConfig,
            dataAttr: dataAttr
        });

        return `
            <div class="col-md-6 mb-2">
                <div class="input-group">
                    <span class="input-group-text config-key">${escapedKey}</span>
                    <input type="text" class="form-control config-value"
                           value="${escapedValue}" ${dataAttr}>
                </div>
            </div>
        `;
    },

    /**
     * 渲染其他配置
     */
    renderOtherConfig: function() {
        const container = $('#otherConfigContainer');
        container.empty();

        if (Object.keys(this.otherConfig).length === 0) {
            container.html(`
                <div class="text-center py-4">
                    <i class="fas fa-info-circle fa-2x text-muted mb-2"></i>
                    <p class="text-muted">暂无其他配置项</p>
                </div>
            `);
            return;
        }

        let html = '<div class="row">';
        for (const [key, value] of Object.entries(this.otherConfig)) {
            html += this.renderConfigItem(null, key, value, false);
        }
        html += '</div>';
        
        container.html(html);
    },

    /**
     * 绑定事件
     */
    bindEvents: function() {
        // 保存配置按钮
        $(document).on('click', '#saveConfigBtn', (function() {
            this.saveConfig();
        }).bind(this));

        // 添加 Provider 按钮
        $(document).on('click', '#addProviderBtn', function() {
            $('#addProviderModal').modal('show');
        });

        // 确认添加 Provider 按钮
        $(document).on('click', '#confirmAddProviderBtn', (function() {
            this.addProvider();
        }).bind(this));

        // 删除 Provider 按钮
        $(document).on('click', '.remove-provider-btn', (function(e) {
            const providerName = $(e.target).closest('button').data('provider');
            this.removeProvider(providerName);
        }).bind(this));

        // 添加其他配置项按钮
        $(document).on('click', '#addOtherConfigBtn', (function() {
            this.addOtherConfigItem();
        }).bind(this));
    },

    /**
     * 保存配置
     */
    saveConfig: function() {
        console.log('开始保存配置...');

        // 重置 configData
        this.configData = {};

        // 收集 Provider 配置
        const providerInputs = $('input.config-value[data-provider]');
        console.log('找到Provider配置输入框数量:', providerInputs.length);

        providerInputs.each((function(index, element) {
            const $this = $(element);
            const providerName = $this.data('provider');
            const key = $this.data('key');
            const value = $this.val() || '';

            console.log(`Provider配置 ${index + 1}:`, {
                providerName: providerName,
                key: key,
                value: value,
                element: element
            });

            // 确保providerName和key都存在
            if (providerName && key) {
                const configKey = `PROVIDER_${providerName}_${key}`;
                this.configData[configKey] = value;
                console.log(`添加配置: ${configKey} = ${value}`);
            } else {
                console.warn('Provider配置项缺少必要属性:', {
                    providerName: providerName,
                    key: key,
                    element: element
                });
            }
        }).bind(this));

        // 收集其他配置
        const otherInputs = $('input.config-value:not([data-provider])');
        console.log('找到其他配置输入框数量:', otherInputs.length);

        otherInputs.each((function(index, element) {
            const $this = $(element);
            const key = $this.data('key');
            const value = $this.val() || '';

            console.log(`其他配置 ${index + 1}:`, {
                key: key,
                value: value,
                element: element
            });

            // 确保key存在
            if (key) {
                this.configData[key] = value;
                console.log(`添加配置: ${key} = ${value}`);
            } else {
                console.warn('其他配置项缺少key属性:', {
                    key: key,
                    element: element
                });
            }
        }).bind(this));

        console.log('收集到的配置数据:', this.configData);
        console.log('配置数据键数量:', Object.keys(this.configData).length);

        // 检查配置数据是否有效
        if (!this.configData || Object.keys(this.configData).length === 0) {
            console.error('没有收集到任何配置数据');

            // 调试信息：检查页面上的所有输入框
            const allInputs = $('input.config-value');
            console.log('页面上所有config-value输入框:', allInputs.length);
            allInputs.each(function(index, element) {
                const $el = $(element);
                console.log(`输入框 ${index + 1}:`, {
                    element: element,
                    provider: $el.data('provider'),
                    key: $el.data('key'),
                    value: $el.val(),
                    hasProviderAttr: $el.attr('data-provider') !== undefined,
                    hasKeyAttr: $el.attr('data-key') !== undefined
                });
            });

            Utils.showAlert('没有配置数据需要保存，请检查页面是否正确加载了配置项', 'warning');
            const saveBtn = $('#saveConfigBtn');
            saveBtn.prop('disabled', false).html('<i class="fas fa-save"></i> 保存配置');
            return;
        }

        // 发送到后端保存
        const saveBtn = $('#saveConfigBtn');
        saveBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 保存中...');

        console.log('发送配置数据到后端:', this.configData);

        API.configManager.saveConfig(this.configData)
            .done((function(response) {
                console.log('后端响应:', response);
                if (response.success) {
                    Utils.showAlert('配置保存成功！', 'success');
                } else {
                    Utils.showAlert('配置保存失败: ' + response.message, 'danger');
                }
            }).bind(this))
            .fail((function(xhr) {
                console.error('保存配置失败:', xhr);
                Utils.showAlert('配置保存失败: ' + (xhr.responseJSON?.message || '未知错误'), 'danger');
            }).bind(this))
            .always((function() {
                saveBtn.prop('disabled', false).html('<i class="fas fa-save"></i> 保存配置');
            }).bind(this));
    },

    /**
     * 添加 Provider
     */
    addProvider: function() {
        const providerName = $('#providerName').val().trim().toUpperCase();
        const displayName = $('#providerDisplayName').val().trim();

        if (!providerName || !displayName) {
            Utils.showAlert('Provider 名称和显示名称不能为空', 'warning');
            return;
        }

        // 检查是否已存在
        if (this.providers[providerName]) {
            Utils.showAlert('Provider 已存在', 'warning');
            return;
        }

        // 添加到 providers
        this.providers[providerName] = {
            name: providerName,
            configs: {
                'NAME': displayName,
                'API_TYPE': 'claude',
                'BASE_URL': '',
                'AUTH_TOKEN': '',
                'MODEL': '',
                'API_KEY': '',
                'MAX_TOKENS': '15000'
            }
        };

        // 添加到 configData
        for (const [key, value] of Object.entries(this.providers[providerName].configs)) {
            this.configData[`PROVIDER_${providerName}_${key}`] = value;
        }

        // 重新渲染
        this.renderProviders();
        $('#addProviderModal').modal('hide');
        $('#addProviderForm')[0].reset();
        
        Utils.showAlert(`Provider "${displayName}" 添加成功`, 'success');
    },

    /**
     * 删除 Provider
     */
    removeProvider: function(providerName) {
        Utils.confirm(`确定要删除 Provider "${providerName}" 吗？此操作将删除所有相关配置。`, (function() {
            // 从 providers 中删除
            delete this.providers[providerName];
            
            // 从 configData 中删除
            for (const key of Object.keys(this.configData)) {
                if (key.startsWith(`PROVIDER_${providerName}_`)) {
                    delete this.configData[key];
                }
            }
            
            // 重新渲染
            this.renderProviders();
            Utils.showAlert(`Provider "${providerName}" 删除成功`, 'success');
        }).bind(this));
    },

    /**
     * 添加其他配置项
     */
    addOtherConfigItem: function() {
        const key = prompt('请输入配置项名称:');
        if (!key) return;
        
        if (this.otherConfig[key] !== undefined) {
            Utils.showAlert('配置项已存在', 'warning');
            return;
        }
        
        this.otherConfig[key] = '';
        this.configData[key] = '';
        
        // 重新渲染
        this.renderOtherConfig();
        Utils.showAlert(`配置项 "${key}" 添加成功`, 'success');
    }
};