function loadDashboardData() {
    // 加载项目列表
    API.projects.list()
        .done(function (response) {
            // 返回结构：{index_title: "...", projects: [...]}
            const projects = response.projects; 
            const indexTitle = response.index_title;
            const runningAgentsCount = response.running_agents_count || 0;

            // 如果有index_title，更新各个显示位置
            if (indexTitle) {
                // 更新页面标题
                document.title = indexTitle + ' - 全栈助手';

                // 更新导航栏标题
                const navbarBrand = document.querySelector('.navbar-brand');
                if (navbarBrand) {
                    navbarBrand.innerHTML = `<i class="fas fa-tasks"></i> 全栈助手`;
                }

                // 更新滚动显示的标题
                const indexTitleText = document.getElementById('indexTitleText');
                if (indexTitleText) {
                    indexTitleText.textContent = indexTitle;
                }

                // 重启动画以应用新文本
                const indexTitleScroll = document.getElementById('indexTitleScroll');
                if (indexTitleScroll) {
                    // 移除动画类
                    const newText = indexTitleScroll.innerHTML;
                    indexTitleScroll.innerHTML = newText;
                }
            }

            updateDashboardStats(projects, runningAgentsCount);
            renderRecentProjects(projects);
            updateProjectSelector(projects);
        })
        .fail(function (xhr) {
            const response = xhr.responseJSON || {};
            Utils.showAlert('加载仪表板数据失败: ' + (response.message || '未知错误'), 'danger');
        });
}

function updateDashboardStats(projects, runningAgentsCount = 0) {
    const totalProjects = projects.length;
    const activeProjects = projects.filter(p => p.requirement && p.requirement.trim()).length;
    const totalTasks = projects.reduce((sum, p) => sum + (p.total_tasks || 0), 0);
    const runningTasks = runningAgentsCount; // 使用从API获取的运行中Agent数量

    // 使用动画效果更新数字
    Utils.animateNumber(document.getElementById('total-projects'), totalProjects, 1500);
    Utils.animateNumber(document.getElementById('active-projects'), activeProjects, 1800);
    Utils.animateNumber(document.getElementById('total-tasks'), totalTasks, 2000);
    Utils.animateNumber(document.getElementById('running-tasks'), runningTasks, 2200);
}

function renderRecentProjects(projects) {
    const container = $('#recent-projects-container');

    if (projects.length === 0) {
        container.html(`
                    <div class="text-center py-4">
                        <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                        <p class="text-muted">还没有项目，<a href="#" onclick="showCreateProjectModal()">创建第一个项目</a></p>
                    </div>
                `);
        return;
    }

    const recentProjects = projects.slice(0, 5);
    let html = `
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>项目名称</th>
                                <th>任务数量</th>
                                <th>创建时间</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

    recentProjects.forEach(project => {
        html += `
                    <tr>
                        <td>
                            <a href="#" class="text-decoration-none" style="cursor: pointer; color: #0d6efd;" onclick="switchCurrentProject('${project.project_id}')">
                                <strong>${project.name}</strong>
                            </a>
                            ${project.description ?
                `<br><small class="text-muted">${project.description.substring(0, 50)}${project.description.length > 50 ? '...' : ''}</small>` :
                ''}
                        </td>
                        <td>
                            <span class="badge bg-info">${project.total_tasks || 0}</span>
                        </td>
                        <td>${Utils.formatDateTime(project.created_at)}</td>
                    </tr>
                `;
    });

    html += `
                        </tbody>
                    </table>
                </div>
            `;

    container.html(html);
}

function refreshDashboard() {
    loadDashboardData();
}
// 加载项目选择器
function updateProjectSelector(projects) {
    const selector = document.getElementById('currentProjectSelector');
    selector.innerHTML = '<option value="">请选择项目...</option>';

    projects.forEach(project => {
        const option = document.createElement('option');
        option.value = project.project_id;
        option.textContent = project.name;
        selector.appendChild(option);
        g_xterminalUrl = project.xterm_url;
    });
}

function showCreateProjectModal() {
    ProjectManager.showCreateModal();
}

function showQuickTask() {
    Utils.showAlert('快速任务功能正在开发中...', 'info');
}

// 全局变量
let currentProjectId = null;
// 当前菜单
let activeFunction = null;
// 终端端口
let g_xterminalPort = null;

// 切换当前项目
function switchCurrentProject(projectId = null) {
    // 如果没有传入projectId，则从选择器获取
    if (!projectId) {
        const selector = document.getElementById('currentProjectSelector');
        projectId = selector.value;
    }

    if (projectId) {
        // 设置选择器的值
        const selector = document.getElementById('currentProjectSelector');
        selector.value = projectId;
        
        currentProjectId = projectId;
        // 保存到localStorage
        localStorage.setItem('currentProjectId', projectId);
        document.getElementById('currentProjectMenu').style.display = 'block';
        Utils.showAlert(`已切换到项目: ${selector.options[selector.selectedIndex].text}`, 'success');
        if (activeFunction) {
            if (activeFunction === '需求管理') {
                showRequirementManager()
            } else if (activeFunction === '设计管理') {
                showDesignManager()
            } else if (activeFunction === '规则管理') {
                showRulesManager()
            } else if (activeFunction === '知识库管理') {
                showKnowledgeManager()
            } else if (activeFunction === '任务管理') {
                showTaskManager()
            } else if (activeFunction === '文件管理') {
                showFileManager()
            } else if (activeFunction === '终端管理') {
                showTerminal()
            }else if (activeFunction === 'AiChat') {
                showAiChat()
            }
        }
    } else {
        currentProjectId = null;
        localStorage.removeItem('currentProjectId');
        document.getElementById('currentProjectMenu').style.display = 'none';
    }
}

// 恢复上次选择的项目
function restoreLastProject() {
    const lastProjectId = localStorage.getItem('currentProjectId');
    if (lastProjectId) {
        const selector = document.getElementById('currentProjectSelector');
        // 等待项目列表加载完成后再设置
        setTimeout(() => {
            if (selector.querySelector(`option[value="${lastProjectId}"]`)) {
                selector.value = lastProjectId;
                currentProjectId = lastProjectId;
                document.getElementById('currentProjectMenu').style.display = 'block';
                //console.log('已恢复上次选择的项目:', lastProjectId);
            }
        }, 100);
    }
}
function showIfrmame(divId, divName, url) {
    // 直接在主内容区域创建内容
    const mainArea = document.getElementById('mainContent');
    mainArea.innerHTML = '';

    // 创建容器
    const rightDiv = document.createElement('div');
    rightDiv.id = divId;
    rightDiv.style.cssText = `
                display: block !important;
                background-color: #f8f9fa !important;
                border: 1px solid #007bff !important;
                min-height: 400px !important;
                padding: 0 !important;
                margin: 0 !important;
                border-radius: 2px !important;
                height: 90vh !important;
                overflow: hidden !important;
            `;
    // 创建iframe
    const iframe = document.createElement('iframe');
    iframe.id = `${divId}Frame`;

    // 正确处理URL参数，如果URL已经包含查询参数，则使用&连接project_id
    const separator = url.includes('?') ? '&' : '?';
    iframe.src = `${url}${separator}project_id=${currentProjectId}`;

    iframe.style.cssText = `
                width: 100% !important;
                height: 100% !important;
                border: none !important;
                display: block !important;
            `;

    rightDiv.appendChild(iframe);

    // 添加到主内容区域
    mainArea.appendChild(iframe);

    // 切换当前活动菜单
    updateActiveMenu(divName);
}

// 显示项目管理
function showProjectManagement() {
    showIfrmame('projectManagement', '项目管理', 'projects.html')
    return
}

// 显示配置管理
function showConfigManagement() {
    showIfrmame('configManagement', '配置管理', 'config_manager.html')
    return
}

// 显示需求管理
function showRequirementManager() {
    if (!currentProjectId) {
        Utils.showAlert('请先选择一个项目', 'warning');
        return;
    }
    showIfrmame('requirementManagement', '需求管理', 'requirement_manager.html')
    return;
}

// 显示设计管理
function showDesignManager() {
    if (!currentProjectId) {
        Utils.showAlert('请先选择一个项目', 'warning');
        return;
    }
    showIfrmame('designManagement', '设计管理', 'design_manager.html')
    return;
}

// 显示规则管理
function showRulesManager() {
    if (!currentProjectId) {
        Utils.showAlert('请先选择一个项目', 'warning');
        return;
    }
    showIfrmame('ruleManagement', '规则管理', 'rules_manager.html')
    return;
}

// 显示任务管理
function showTaskManager() {
    if (!currentProjectId) {
        Utils.showAlert('请先选择一个项目', 'warning');
        return;
    }
    showIfrmame('taskManagement', '任务管理', 'project_tasks.html')
    return;
}

// 显示知识库管理
function showKnowledgeManager() {
    if (!currentProjectId) {
        Utils.showAlert('请先选择一个项目', 'warning');
        return;
    }
    showIfrmame('knowledgeManagement', '知识库管理', 'knowledge_manager.html')
    return;
}

// 显示文件管理
function showFileManager() {
    if (!currentProjectId) {
        Utils.showAlert('请先选择一个项目', 'warning');
        return;
    }
    showIfrmame('fileManagement', '文件管理', 'file_manager.html')
    return;
}

// 隐藏所有内容区域
function hideAllContent() {
    document.getElementById('dashboardContent').style.display = 'none';
    document.getElementById('projectManagementContent').style.display = 'none';
    document.getElementById('requirementContent').style.display = 'none';
    document.getElementById('designContent').style.display = 'none';
    document.getElementById('rulesContent').style.display = 'none';
    document.getElementById('taskContent').style.display = 'none';
}

// 更新活动菜单
function updateActiveMenu(activeText) {
    activeFunction = activeText;
    // 移除所有活动状态
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });

    // 添加活动状态到当前菜单
    document.querySelectorAll('.nav-link').forEach(link => {
        if (link.textContent.trim().includes(activeText)) {
            link.classList.add('active');
        }
    });

    // 更新页面标题
    const titleElement = document.querySelector('title');
    if (titleElement) {
        if (activeText === '仪表板') {
            titleElement.textContent = '仪表板 - 全栈助手';
        } else {
            titleElement.textContent = `${activeText} - 全栈助手`;
        }
    }

    // 更新导航栏中的活动菜单显示
    const activeMenuDisplay = document.getElementById('activeMenuDisplay');
    if (activeMenuDisplay) {
        if (activeText === '仪表板') {
            activeMenuDisplay.textContent = '';
        } else {
            activeMenuDisplay.textContent = activeText;
        }
    }
}
function showTerminal() {
    if (!currentProjectId) {
        Utils.showAlert('请先选择一个项目', 'warning');
        return;
    }

    // 获取当前页面的协议和主机名
    const protocol = window.location.protocol;
    const hostname = window.location.hostname;

    return showIfrmame('xtermManagement', '终端管理', `${g_xterminalUrl}`)
}

/**
 * 显示AI聊天界面
 * 打开task_id=-1的AI聊天界面
 */
function showAiChat() {
    if (!currentProjectId) {
        Utils.showAlert('请先选择一个项目', 'warning');
        return;
    }
    // 在右侧区域显示AI聊天界面，task_id设置为-1表示通用AI聊天
    showIfrmame('aiChatManagement', 'AiChat', 'task_chat.html?task_id=-1');
}

function showTestPage() {
    // 直接在主内容区域创建内容
    const mainArea = document.getElementById('mainContent');
    mainArea.innerHTML = '';

    // 创建需求管理容器
    const rightDiv = document.createElement('div');
    rightDiv.id = "testdiv";
    rightDiv.style.cssText = `
                display: block !important;
                background-color: #f8f9fa !important;
                border: 1px solid #007bff !important;
                min-height: 400px !important;
                padding: 0 !important;
                margin: 0 !important;
                border-radius: 2px !important;
                height: 90vh !important;
                overflow: hidden !important;
            `;
    // 创建iframe
    const iframe = document.createElement('iframe');
    iframe.id = `testFrame`;
    iframe.src = `simple_test.html`;
    iframe.style.cssText = `
                width: 100% !important;
                height: 100% !important;
                border: none !important;
                display: block !important;
            `;

    rightDiv.appendChild(iframe);

    // 添加到主内容区域
    mainArea.appendChild(rightDiv);

    //updateActiveMenu(divName);
}

// 切换视图模式
function switchViewMode(containerId, mode) {
    const editorPane = document.getElementById(`editorPane_${containerId}`);
    const previewPane = document.getElementById(`previewPane_${containerId}`);

    if (mode === 'edit') {
        editorPane.style.display = 'block';
        previewPane.style.display = 'none';
        editorPane.style.flex = '1';
    } else if (mode === 'preview') {
        editorPane.style.display = 'none';
        previewPane.style.display = 'block';
        previewPane.style.flex = '1';
        updatePreview(containerId);
    } else if (mode === 'split') {
        editorPane.style.display = 'block';
        previewPane.style.display = 'block';
        editorPane.style.flex = '1';
        previewPane.style.flex = '1';
        updatePreview(containerId);
    }
}

// 更新预览
function updatePreview(containerId) {
    const editor = document.getElementById(`editor_${containerId}`);
    const preview = document.getElementById(`preview_${containerId}_content`);

    if (editor && preview) {
        const markdownText = editor.value;
        const htmlContent = marked.parse(markdownText);
        preview.innerHTML = htmlContent;
    }
}

// 加载编辑器内容
function loadEditorContent(containerId, apiEndpoint) {
    //console.log('loadEditorContent called:', containerId, apiEndpoint, 'currentProjectId:', currentProjectId);
    if (!currentProjectId) {
        console.error('No currentProjectId set');
        return;
    }

    const fieldMap = {
        'requirement': 'requirement',
        'design': 'design',
        'rules': 'rules_constraint'
    };

    const url = `/aicode/api/projects/${currentProjectId}/${apiEndpoint}`;
    //console.log('Fetching:', url);

    fetch(url)
        .then(response => {
            //console.log('Response status:', response.status);
            return response.json();
        })
        .then(data => {
            //console.log('Response data:', data);
            const editor = document.getElementById(`editor_${containerId}`);
            //console.log('Editor element:', editor);
            if (editor) {
                const fieldName = fieldMap[apiEndpoint];
                const content = data[fieldName] || '';
                console.log('Setting content:', content);
                editor.value = content;
                updatePreview(containerId);
            } else {
                console.error('Editor element not found:', `editor_${containerId}`);
            }
        })
        .catch(error => {
            console.error('加载内容失败:', error);
            Utils.showAlert('加载内容失败', 'error');
        });
}

// 保存内容
function saveContent(apiEndpoint) {
    if (!currentProjectId) return;

    const containerId = apiEndpoint + 'Content';
    const editor = document.getElementById(`editor_${containerId}`);
    if (!editor) return;

    const fieldMap = {
        'requirement': 'requirement',
        'design': 'design',
        'rules': 'rules_constraint'
    };

    const fieldName = fieldMap[apiEndpoint];
    const content = editor.value;

    const data = {};
    data[fieldName] = content;

    fetch(`/aicode/api/projects/${currentProjectId}/${apiEndpoint}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                Utils.showAlert('保存成功', 'success');
            } else {
                Utils.showAlert('保存失败: ' + result.message, 'error');
            }
        })
        .catch(error => {
            console.error('保存失败:', error);
            Utils.showAlert('保存失败', 'error');
        });
}

// 侧边栏切换功能
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');
    const toggleBtn = document.getElementById('sidebarToggle');
    const toggleIcon = toggleBtn.querySelector('i');

    sidebar.classList.toggle('collapsed');

    if (sidebar.classList.contains('collapsed')) {
        // 收起状态
        mainContent.classList.remove('col-md-9', 'col-lg-10');
        mainContent.classList.add('col-md-11', 'col-lg-11');
        toggleBtn.title = '展开侧边栏';
        toggleIcon.classList.remove('fa-chevron-left');
        toggleIcon.classList.add('fa-chevron-right');
        // 保存状态到 localStorage
        localStorage.setItem('sidebarCollapsed', 'true');
    } else {
        // 展开状态
        mainContent.classList.remove('col-md-11', 'col-lg-11');
        mainContent.classList.add('col-md-9', 'col-lg-10');
        toggleBtn.title = '收起侧边栏';
        toggleIcon.classList.remove('fa-chevron-right');
        toggleIcon.classList.add('fa-chevron-left');
        // 保存状态到 localStorage
        localStorage.setItem('sidebarCollapsed', 'false');
    }
}

// 恢复侧边栏状态
function restoreSidebarState() {
    const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    if (isCollapsed) {
        toggleSidebar();
    }
}

// ==================== 用户相关功能 ====================

/**
 * 显示用户信息页面
 */
function showUserProfile() {
    window.location.href = '/aicode/user_profile.html';
}

/**
 * 退出登录
 */
function logout() {
    if (!confirm('确定要退出登录吗？')) {
        return;
    }

    $.ajax({
        url: '/aicode/api/auth/logout',
        method: 'POST',
        success: function (response) {
            Utils.showAlert('已退出登录', 'success');
            setTimeout(() => {
                window.location.href = '/aicode/login.html';
            }, 500);
        },
        error: function (xhr) {
            Utils.showAlert('退出登录失败', 'danger');
        }
    });
}