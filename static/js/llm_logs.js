/**
 * LLM日志相关JavaScript
 */

// LLM日志管理模块
window.LLMLogsManager = {
    currentProjectId: null,
    currentTaskId: null,
    autoRefreshInterval: null,
    lastTimestamp: null,
    isAutoRefreshEnabled: false,

    /**
     * 初始化LLM日志页面
     */
    initLogsPage: function(projectId, taskId) {
        this.currentProjectId = projectId;
        this.currentTaskId = taskId;
        this.loadLogs();
        this.bindEvents();
    },

    /**
     * 加载日志
     */
    loadLogs: function(incremental = false) {
        const url = incremental && this.lastTimestamp ? 
            `/aicode/api/projects/${this.currentProjectId}/tasks/${this.currentTaskId}/llm-logs?since=${encodeURIComponent(this.lastTimestamp)}` :
            `/aicode/api/projects/${this.currentProjectId}/tasks/${this.currentTaskId}/llm-logs`;
        
        $.ajax({
            url: url,
            method: 'GET',
            success: function(response) {
                $('#connectionStatus').text('已连接').removeClass('text-danger').addClass('text-success');
                
                if (response.success && response.logs) {
                    if (incremental && response.logs.length > 0) {
                        // 增量更新：追加新日志
                        LLMLogsManager.appendLogs(response.logs);
                    } else if (!incremental) {
                        // 全量更新：替换所有日志
                        LLMLogsManager.renderLogs(response.logs);
                    }
                    
                    // 更新统计信息
                    LLMLogsManager.updateStats(response.logs);
                    
                    // 更新最后时间戳
                    if (response.logs.length > 0) {
                        LLMLogsManager.lastTimestamp = response.logs[response.logs.length - 1].timestamp;
                    }
                } else if (!incremental) {
                    $('#logsContainer').html(`
                        <div class="text-center text-muted py-5">
                            <i class="fas fa-file-alt fa-2x mb-3"></i>
                            <div>暂无日志记录</div>
                        </div>
                    `);
                }
            },
            error: function(xhr) {
                $('#connectionStatus').text('连接失败').removeClass('text-success').addClass('text-danger');
                
                if (!incremental) {
                    const response = xhr.responseJSON || {};
                    $('#logsContainer').html(`
                        <div class="text-center text-danger py-5">
                            <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                            <div>加载日志失败: ${response.error || response.message || '未知错误'}</div>
                            <button class="btn btn-outline-primary mt-3" onclick="LLMLogsManager.loadLogs()">重试</button>
                        </div>
                    `);
                }
            }
        });
    },

    /**
     * 渲染日志
     */
    renderLogs: function(logs) {
        if (logs.length === 0) {
            $('#logsContainer').html(`
                <div class="text-center text-muted py-5">
                    <i class="fas fa-file-alt fa-2x mb-3"></i>
                    <div>暂无日志记录</div>
                </div>
            `);
            return;
        }

        let html = '';
        logs.forEach((log, index) => {
            html += this.createLogEntryHtml(log, index);
        });
        
        $('#logsContainer').html(html);
        
        // 滚动到底部
        const container = document.getElementById('logsContainer');
        if (container) {
            container.scrollTop = container.scrollHeight;
        }
    },

    /**
     * 追加日志
     */
    appendLogs: function(logs) {
        if (logs.length === 0) return;
        
        const container = $('#logsContainer');
        const currentCount = container.find('.log-entry').length;
        
        let html = '';
        logs.forEach((log, index) => {
            html += this.createLogEntryHtml(log, currentCount + index);
        });
        
        container.append(html);
        
        // 滚动到底部
        const containerElement = document.getElementById('logsContainer');
        if (containerElement) {
            containerElement.scrollTop = containerElement.scrollHeight;
        }
    },

    /**
     * 创建日志条目HTML
     */
    createLogEntryHtml: function(log, index) {
        const logTypeColors = {
            'Request': 'bg-light border-start border-primary border-3',
            'Assistant': 'bg-light border-start border-success border-3', 
            'User': 'bg-light border-start border-info border-3',
            'Result': 'bg-light border-start border-warning border-3',
            'Stream': 'bg-light border-start border-secondary border-3'
        };
        
        const logTypeIcons = {
            'Request': 'fas fa-paper-plane text-primary',
            'Assistant': 'fas fa-robot text-success',
            'User': 'fas fa-user text-info', 
            'Result': 'fas fa-check-circle text-warning',
            'Stream': 'fas fa-stream text-secondary'
        };
        
        const colorClass = logTypeColors[log.log_type] || 'bg-light border-start border-secondary border-3';
        const iconClass = logTypeIcons[log.log_type] || 'fas fa-circle text-secondary';
        
        // 处理内容中的换行符
        const content = log.content.replace(/↵/g, '\n');
        const lines = content.split('\n');
        const totalChars = content.length;

        // 判断是否需要折叠：多行（>3行）或者长字符（>500字符）
        const isMultiLine = lines.length > 3;
        const isLongContent = totalChars > 500;
        const needsCollapse = isMultiLine || isLongContent;

        let displayContent = '';
        if (needsCollapse) {
            let previewContent = '';
            let buttonText = '';

            if (isMultiLine) {
                // 多行日志：显示前3行
                previewContent = lines.slice(0, 3).join('\n');
                const remainingLines = lines.length - 3;
                buttonText = `...还有${remainingLines}行`;
            } else {
                // 单行但字符很多：显示前500字符
                previewContent = content.substring(0, 500);
                const remainingChars = totalChars - 500;
                buttonText = `...还有${remainingChars}个字符`;
            }

            displayContent = `
                <div class="log-content-preview" id="preview-${index}">
                    <pre class="mb-0">${Utils.escapeHtml(previewContent)}</pre>
                    <div class="mt-2">
                        <button class="btn btn-sm btn-outline-secondary" onclick="LLMLogsManager.toggleLogContent(${index})">
                            <i class="fas fa-chevron-down"></i> ${buttonText}
                        </button>
                    </div>
                </div>
                <div class="log-content-full d-none" id="full-${index}">
                    <pre class="mb-0">${Utils.escapeHtml(content)}</pre>
                    <div class="mt-2">
                        <button class="btn btn-sm btn-outline-secondary" onclick="LLMLogsManager.toggleLogContent(${index})">
                            <i class="fas fa-chevron-up"></i> 收起
                        </button>
                    </div>
                </div>
            `;
        } else {
            displayContent = `<pre class="mb-0">${Utils.escapeHtml(content)}</pre>`;
        }
        
        return `
            <div class="log-entry mb-3 p-3 rounded ${colorClass}">
                <div class="d-flex align-items-center mb-2">
                    <i class="${iconClass} me-2"></i>
                    <strong class="me-3">${log.log_type}</strong>
                    <span class="badge bg-secondary me-2">${log.progress}</span>
                    <small class="text-muted">${log.timestamp}</small>
                </div>
                <div class="log-content">
                    ${displayContent}
                </div>
            </div>
        `;
    },

    /**
     * 切换日志内容显示
     */
    toggleLogContent: function(index) {
        const preview = $(`#preview-${index}`);
        const full = $(`#full-${index}`);
        
        if (preview.hasClass('d-none')) {
            preview.removeClass('d-none');
            full.addClass('d-none');
        } else {
            preview.addClass('d-none');
            full.removeClass('d-none');
        }
    },

    /**
     * 更新统计信息
     */
    updateStats: function(logs) {
        const totalCount = $('#logsContainer .log-entry').length;
        $('#totalLogsCount').text(totalCount);
        $('#lastUpdateTime').text(new Date().toLocaleTimeString());
    },

    /**
     * 绑定事件
     */
    bindEvents: function() {
        // 刷新按钮
        $(document).on('click', '#refresh-logs-btn', function() {
            LLMLogsManager.refreshLogs();
        });

        // 自动刷新切换按钮
        $(document).on('click', '#toggle-auto-refresh-btn', function() {
            LLMLogsManager.toggleAutoRefresh();
        });

        // 清空日志按钮
        $(document).on('click', '#clear-logs-btn', function() {
            LLMLogsManager.clearLogs();
        });
    },

    /**
     * 刷新日志
     */
    refreshLogs: function() {
        this.lastTimestamp = null;
        this.loadLogs(false);
    },

    /**
     * 切换自动刷新
     */
    toggleAutoRefresh: function() {
        if (this.isAutoRefreshEnabled) {
            // 停止自动刷新
            clearInterval(this.autoRefreshInterval);
            this.autoRefreshInterval = null;
            this.isAutoRefreshEnabled = false;
            $('#autoRefreshIcon').removeClass('fa-stop').addClass('fa-play');
            $('#autoRefreshText').text('开启自动刷新');
            $('#autoRefreshStatus').text('关闭');
        } else {
            // 开启自动刷新
            this.autoRefreshInterval = setInterval(() => {
                this.loadLogs(true); // 增量加载
            }, 3000); // 每3秒刷新一次
            this.isAutoRefreshEnabled = true;
            $('#autoRefreshIcon').removeClass('fa-play').addClass('fa-stop');
            $('#autoRefreshText').text('停止自动刷新');
            $('#autoRefreshStatus').text('开启');
        }
    },

    /**
     * 清空日志显示
     */
    clearLogs: function() {
        Utils.confirm('确定要清空当前显示的日志吗？这不会删除服务器上的日志文件。', function() {
            $('#logsContainer').html(`
                <div class="text-center text-muted py-5">
                    <i class="fas fa-file-alt fa-2x mb-3"></i>
                    <div>日志已清空，点击刷新重新加载</div>
                </div>
            `);
            $('#totalLogsCount').text('0');
            LLMLogsManager.lastTimestamp = null;
        });
    }
};

// 页面卸载时清理定时器
$(window).on('beforeunload', function() {
    if (LLMLogsManager.autoRefreshInterval) {
        clearInterval(LLMLogsManager.autoRefreshInterval);
    }
});
