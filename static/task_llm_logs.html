<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LLM日志 - 全栈助手</title>

    <!-- Bootstrap CSS -->
    <link href="/aicode/static/external/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="/aicode/static/external/all.min.css" rel="stylesheet">
    <!-- Modern Theme -->
    <link href="/aicode/static/css/modern-theme.css" rel="stylesheet">

    <style>
        .logs-container {
            max-height: 75vh;
            overflow-y: auto;
            border: 1.5px solid var(--border-color);
            border-radius: var(--border-radius);
            background-color: var(--bg-secondary);
            padding: 1rem;
        }

        .log-entry {
            border-radius: var(--border-radius-sm);
            margin-bottom: 1rem;
            box-shadow: var(--shadow);
            transition: var(--transition);
        }

        .log-entry:hover {
            box-shadow: var(--shadow-md);
        }

        .log-content pre {
            background-color: transparent;
            border: none;
            margin: 0;
            padding: 0;
            font-size: 0.875rem;
            white-space: pre-wrap;
            word-wrap: break-word;
            line-height: 1.6;
            font-family: 'Monaco', 'Menlo', 'Courier New', monospace;
        }

        .log-content-preview {
            position: relative;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 0.5rem;
            box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
        }

        .status-connected {
            background-color: var(--success-color);
        }

        .status-disconnected {
            background-color: #dc3545;
        }

        .auto-refresh-indicator {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                opacity: 1;
            }

            50% {
                opacity: 0.5;
            }

            100% {
                opacity: 1;
            }
        }
    </style>
</head>

<body>
    <!-- 主内容区域 -->
    <div class="pt-2">
        <div
            class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group me-2">
                    <button type="button" class="btn btn-outline-info" id="back-to-tasks-btn">
                        <i class="fas fa-tasks"></i> 返回任务
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="refresh-logs-btn">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                    <button type="button" class="btn btn-outline-primary" id="toggle-auto-refresh-btn-top">
                        <i class="fas fa-play" id="autoRefreshIconTop"></i> <span id="autoRefreshTextTop">开启自动刷新</span>
                    </button>
                    <div class="row align-items-right">
                        <div class="col-md-3">
                            <strong>连接状态:</strong>
                            <span class="status-indicator status-connected" id="connectionStatusIndicator"></span>
                            <span id="connectionStatus" class="text-success">已连接</span>
                        </div>
                        <div class="col-md-3">
                            <strong>日志数量:</strong>
                            <span id="totalLogsCount" class="badge bg-info">0</span>
                        </div>
                        <div class="col-md-3">
                            <strong>最后更新:</strong>
                            <span id="lastUpdateTime" class="text-muted">-</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 日志容器 -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body p-0">
                        <div class="logs-container p-3" id="logsContainer" style="height: calc(100vh - 250px);">
                            <div class="text-center py-5">
                                <i class="fas fa-spinner fa-spin fa-3x text-muted mb-3"></i>
                                <div>加载日志中...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="/aicode/static/external/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="/aicode/static/external/jquery-3.6.0.min.js"></script>

    <!-- 自定义JS -->
    <script src="/aicode/static/js/common.js"></script>
    <script src="/aicode/static/js/llm_logs.js"></script>

    <script>
        // 页面初始化
        $(document).ready(function () {
            const projectId = Router.getParam('project_id');
            const taskId = Router.getParam('task_id');

            if (!projectId || !taskId) {
                Utils.showAlert('缺少必要参数', 'danger');
                Router.navigate('projects.html');
                return;
            }

            LLMLogsManager.initLogsPage(projectId, taskId);

            // 绑定返回任务按钮
            $('#back-to-tasks-btn').on('click', function () {
                Router.navigate(`project_tasks.html?project_id=${projectId}`);
            });

            // 同步顶部和侧边栏的自动刷新按钮
            $('#toggle-auto-refresh-btn-top').on('click', function () {
                LLMLogsManager.toggleAutoRefresh();
                updateAutoRefreshButtons();
            });

            $('#toggle-auto-refresh-btn').on('click', function () {
                LLMLogsManager.toggleAutoRefresh();
                updateAutoRefreshButtons();
            });
        });

        // 更新自动刷新按钮状态
        function updateAutoRefreshButtons() {
            const isEnabled = LLMLogsManager.isAutoRefreshEnabled;
            const icon = isEnabled ? 'fa-stop' : 'fa-play';
            const text = isEnabled ? '停止自动刷新' : '开启自动刷新';
            const status = isEnabled ? '开启' : '关闭';

            // 更新图标和文本
            $('#autoRefreshIcon, #autoRefreshIconTop').removeClass('fa-play fa-stop').addClass(icon);
            $('#autoRefreshText, #autoRefreshTextTop').text(text);
            $('#autoRefreshStatus').text(status);

            // 更新指示器
            if (isEnabled) {
                $('#autoRefreshIndicator').show();
                $('#autoRefreshStatus').removeClass('text-secondary').addClass('text-success');
            } else {
                $('#autoRefreshIndicator').hide();
                $('#autoRefreshStatus').removeClass('text-success').addClass('text-secondary');
            }
        }

        // 重写LLMLogsManager的toggleAutoRefresh方法以同步按钮状态
        const originalToggleAutoRefresh = LLMLogsManager.toggleAutoRefresh;
        LLMLogsManager.toggleAutoRefresh = function () {
            originalToggleAutoRefresh.call(this);
            updateAutoRefreshButtons();
        };
    </script>
</body>

</html>