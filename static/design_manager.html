<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计管理 - 全栈助手</title>

    <!-- Bootstrap CSS -->
    <link href="/aicode/static/external/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="/aicode/static/external/all.min.css" rel="stylesheet">
    <!-- Bootstrap JS -->
    <script src="/aicode/static/external/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="/aicode/static/external/jquery-3.6.0.min.js"></script>
    <!-- markdown-editor -->
    <link href="/aicode/static/external/cherry-markdown.css" rel="stylesheet">
    <script src="/aicode/static/external/cherry-markdown.js"></script>
    <script src="/aicode/static/external/echarts.min.js"></script>
    <script src="/aicode/static/external/cherry-table-echarts-plugin.js"></script>

    <script src="/aicode/static/js/common.js"></script>
    <script src="/aicode/static/js/tasks.js"></script>
    <style>
        html {
            height: 100%;
        }

        body {
            height: 100%;
            overflow: hidden;
            margin: 0;
        }

        #markdownEditor {
            min-width: 800px;
            max-width: 2600px;
            width: 100%;
            height: 100vh;
            margin: 0 auto;
        }

        iframe.cherry-dialog-iframe {
            width: 100%;
            height: 100%;
        }
    </style>
</head>

<body>
    <div id="actionButtons" class="me-3">
    </div>

    <!-- 编辑区域 -->
    <div id="markdownEditor" placeholder="请输入设计内容...">
    </div>

    <!-- 生成任务确认模态框 -->
    <div class="modal fade" id="generateTasksModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">根据设计生成任务</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="generateTasksContent">
                        <!-- 内容将通过JavaScript加载 -->
                    </div>
                    <div class="mt-3" id="taskCountSection" style="display: none;">
                        <label for="taskCount" class="form-label">任务数量 (可选)</label>
                        <input type="number" class="form-control" id="taskCount" min="1" max="20"
                            placeholder="留空则自动确定任务数量">
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i>
                            建议生成5-10个任务，留空将根据需求自动确定
                        </div>
                    </div>
                    <div class="mt-3">
                        <label for="specialInstruction" class="form-label">特殊说明 (可选)</label>
                        <textarea class="form-control" id="specialInstruction" rows="3"
                            placeholder="添加特殊要求或说明，这些内容将追加到AI生成任务的提示词后面"></textarea>
                        <div class="form-text">
                            <i class="fas fa-lightbulb"></i>
                            可以添加技术栈要求、特殊约束、偏好等信息
                        </div>
                    </div>
                    <input type="hidden" id="generateTasksProjectId">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary"
                        onclick="TaskManager.confirmGenerateTasks('design')">确认生成</button>
                </div>
            </div>
        </div>
    </div>
</body>
<script>
    let apiEndpoint = 'design';
    let contentField = 'design';
    let docType = 'design';
    
    var aiMenu = Cherry.createMenuHook('AI', {
        noIcon: true, name: 'AI',
        onClick: (selection, type) => {
            switch (type) {
                case 'gen_tasks':
                    // 先保存当前的设计文档
                    saveContent();
                    TaskManager.showGenerateTasks(currentProjectId,"design");
                    return `${selection}`;
                case 'run_design':
                    run_design();
                    return `${selection}`;
                default:
                    return selection;
            }
        },
        subMenuConfig: [
            {
                noIcon: true,
                name: '生成任务',
                onclick: (event) => {
                    cherry.toolbar.menus.hooks.aiMenuName.fire(null, 'gen_tasks');
                },
            },
            {
                noIcon: true,
                name: '快速实现',
                onclick: (event) => {
                    cherry.toolbar.menus.hooks.aiMenuName.fire(null, 'run_design');
                },
            },
        ],
    });

    // 获取URL参数
    function getUrlParameter(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    }

    // 初始化
    $(document).ready(function () {
        currentProjectId = getUrlParameter('project_id');
        if (!currentProjectId) {
            alert('缺少项目ID参数');
            window.close();
            return;
        }

        loadContent();
    });

    // 直接把设计发送给claude
    function run_design() {
        // 先保存当前的设计
        saveContent();
        
        // 显示加载提示
        const originalBtnHtml = $('#actionButtons').html();
        $('#actionButtons').html('<span class="text-primary"><i class="fas fa-spinner fa-spin"></i> 设计实现中，可以在任务管理查看详情...</span>');

        // 调用后端API进行优化            
        fetch(`/aicode/api/projects/${currentProjectId}/run_design`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ enable_kb: enable_kb })
        })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    Utils.showAlert('设计实现成功', 'success');
                } else {
                    Utils.showAlert('设计实现失败: ' + result.message, 'danger');
                }
            })
            .catch(error => {
                Utils.showAlert('设计实现失败', 'danger');
            })
            .finally(() => {
                // 恢复按钮
                $('#actionButtons').html(originalBtnHtml);
            });
    }

    // 优化功能
    function optimizeContent() {
        Utils.showAlert('优化功能开发中...', 'info');
    }
</script>
<script src="/aicode/static/js/markdown-editor.js"></script>

</html>