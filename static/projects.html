<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目管理 - 全栈助手</title>

    <!-- Bootstrap CSS -->
    <link href="/aicode/static/external/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="/aicode/static/external/all.min.css" rel="stylesheet">
    <!-- Modern Theme -->
    <link href="/aicode/static/css/modern-theme.css" rel="stylesheet">

    <style>
        .form-label.required:after {
            content: " *";
            color: var(--danger-color);
        }
    </style>
</head>

<body>

    <!-- 主内容区域 -->
    <div
        class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">项目管理</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <div class="btn-group me-2">
                <button type="button" class="btn btn-primary" id="create-project-btn">
                    <i class="fas fa-plus"></i> 新建项目
                </button>
                <button type="button" class="btn btn-outline-secondary" id="refresh-projects-btn">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
            </div>
        </div>
    </div>

    <!-- 项目列表容器 -->
    <div id="projects-container">
        <div class="text-center py-5">
            <i class="fas fa-spinner fa-spin fa-3x text-muted mb-3"></i>
            <div>加载中...</div>
        </div>
    </div>

    <!-- 创建项目模态框 -->
    <div class="modal fade" id="createProjectModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建新项目</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createProjectForm">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="createProjectName" class="form-label required">项目名称</label>
                                    <input type="text" class="form-control" id="createProjectName" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="createProjectTaskType" class="form-label">项目类型</label>
                                    <select class="form-control" id="createProjectTaskType">
                                        <option value="产品迭代">产品迭代</option>
                                        <option value="BUG修复">BUG修复</option>
                                        <option value="PMO">PMO</option>
                                        <option value="代码分析">代码分析</option>
                                        <option value="其他">其他</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="createProjectDescription" class="form-label">项目描述</label>
                            <textarea class="form-control" id="createProjectDescription" rows="3"
                                placeholder="简要说明项目背景与目标"></textarea>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="createProjectGitManaged" class="form-label">Git管理</label>
                                <select class="form-control" id="createProjectGitManaged">
                                    <option value="0">无Git管理</option>
                                    <option value="1">提交代码</option>
                                    <option value="2">提交代码并推送</option>
                                </select>
                            </div>
                        </div>

                        <div class="row g-3 d-none" id="createGitFieldsRow">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="createProjectGitUrl" class="form-label required">Git地址</label>
                                    <input type="text" class="form-control" id="createProjectGitUrl"
                                        placeholder="例如: https://github.com/org/repo.git">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="createProjectGitToken" class="form-label">Access Token</label>
                                    <input type="password" class="form-control" id="createProjectGitToken"
                                        placeholder="私有仓库可选，需具备clone权限">
                                    <div class="form-text">仅用于克隆代码，保存后不会回显</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="createProjectGitBranch" class="form-label">分支名称</label>
                                    <input type="text" class="form-control" id="createProjectGitBranch"
                                        placeholder="例如: main">
                                </div>
                            </div>
                        </div>

                        <div class="row g-3">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="createProjectWorkDir" class="form-label">工作目录</label>
                                    <input type="text" class="form-control" id="createProjectWorkDir"
                                        placeholder="/data/项目目录">
                                    <div class="form-text">Git项目留空将自动生成，其他项目请自行指定</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="createExcludePatterns" class="form-label">排除模式</label>
                                    <input type="text" class="form-control" id="createExcludePatterns"
                                        placeholder="例如: *.log, node_modules/, *.tmp">
                                    <div class="form-text">逗号分隔的文件/目录排除模式</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="createIncludePatterns" class="form-label">包含模式</label>
                                    <input type="text" class="form-control" id="createIncludePatterns"
                                        placeholder="例如: *.py, src/, *.js">
                                    <div class="form-text">逗号分隔的文件/目录包含模式</div>
                                </div>
                            </div>
                        </div>

                        <div class="row g-3">
                            <div class="col-md-4 col-lg-3">
                                <div class="mb-3">
                                    <label for="createProjectProvider" class="form-label">LLM Provider</label>
                                    <select class="form-control" id="createProjectProvider">
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="ProjectManager.submitCreate()">创建项目</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑项目模态框 -->
    <div class="modal fade" id="editProjectModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑项目</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editProjectForm">
                        <input type="hidden" id="editProjectId">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="editProjectName" class="form-label required">项目名称</label>
                                    <input type="text" class="form-control" id="editProjectName" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="editProjectTaskType" class="form-label">项目类型</label>
                                    <select class="form-control" id="editProjectTaskType">
                                        <option value="产品迭代">产品迭代</option>
                                        <option value="BUG修复">BUG修复</option>
                                        <option value="PMO">PMO</option>
                                        <option value="代码分析">代码分析</option>
                                        <option value="其他">其他</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="editProjectGitManaged" class="form-label">Git管理</label>
                                    <select class="form-control" id="editProjectGitManaged">
                                        <option value="0">无Git管理</option>
                                        <option value="1">提交代码</option>
                                        <option value="2">提交代码并推送</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="editProjectDescription" class="form-label">项目描述</label>
                            <textarea class="form-control" id="editProjectDescription" rows="3"></textarea>
                        </div>

                        <div class="row g-3 d-none" id="editGitFieldsRow">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="editProjectGitUrl" class="form-label required">Git地址</label>
                                    <input type="text" class="form-control" id="editProjectGitUrl"
                                        placeholder="例如: https://github.com/org/repo.git">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="editProjectGitToken" class="form-label">Access Token</label>
                                    <input type="password" class="form-control" id="editProjectGitToken"
                                        placeholder="私有仓库可选，需具备clone权限">
                                    <div class="form-text">修改时如留空将保留原值</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="editProjectGitBranch" class="form-label">分支名称</label>
                                    <input type="text" class="form-control" id="editProjectGitBranch"
                                        placeholder="例如: main_20240101">
                                </div>
                            </div>
                        </div>

                        <div class="row g-3">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="editProjectWorkDir" class="form-label">工作目录</label>
                                    <input type="text" class="form-control" id="editProjectWorkDir">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="editExcludePatterns" class="form-label">排除模式</label>
                                    <input type="text" class="form-control" id="editExcludePatterns"
                                        placeholder="例如: *.log, node_modules/, *.tmp">
                                    <div class="form-text">逗号分隔的文件/目录排除模式</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="editIncludePatterns" class="form-label">包含模式</label>
                                    <input type="text" class="form-control" id="editIncludePatterns"
                                        placeholder="例如: *.py, src/, *.js">
                                    <div class="form-text">逗号分隔的文件/目录包含模式</div>
                                </div>
                            </div>
                        </div>

                        <div class="row g-3">
                            <div class="col-md-4 col-lg-3">
                                <div class="mb-3">
                                    <label for="editProjectProvider" class="form-label">LLM Provider</label>
                                    <select class="form-control" id="editProjectProvider">
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="ProjectManager.submitEdit()">保存修改</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 项目详情模态框 -->
    <div class="modal fade" id="projectDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">项目详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="projectDetailContent">
                        <!-- 项目详情内容将通过JavaScript加载 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="editProjectFromDetail()">编辑项目</button>
                </div>
            </div>
        </div>
    </div>
    <!-- Bootstrap JS -->
    <script src="/aicode/static/external/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="/aicode/static/external/jquery-3.6.0.min.js"></script>

    <!-- 自定义JS -->
    <script src="/aicode/static/js/common.js"></script>
    <script src="/aicode/static/js/projects.js"></script>

    <script>
        // 页面初始化
        $(document).ready(function () {
            ProjectManager.initProjectsPage();
        });

        function editProjectFromDetail() {
            const projectId = $('#projectDetailModal').data('project-id');
            $('#projectDetailModal').modal('hide');
            setTimeout(() => ProjectManager.showEditModal(projectId), 300);
        }
    </script>
</body>

</html>