<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置管理 - 全栈助手</title>
    <!-- Bootstrap CSS -->
    <link href="/aicode/static/external/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="/aicode/static/external/all.min.css" rel="stylesheet">
    <!-- Modern Theme -->
    <link href="/aicode/static/css/modern-theme.css" rel="stylesheet">

    <style>
        .provider-card {
            transition: var(--transition);
        }
        .provider-card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-4px);
        }
        .config-key {
            font-weight: 600;
            color: var(--text-primary);
        }
        .config-value {
            word-break: break-all;
            color: var(--text-secondary);
        }
        .section-title {
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 0.75rem;
            margin-bottom: 1.5rem;
            font-weight: 700;
        }
    </style>
</head>
<body>
    <div class="container my-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-cog"></i> 系统配置管理</h2>
            <button class="btn btn-primary" id="saveConfigBtn">
                <i class="fas fa-save"></i> 保存配置
            </button>
        </div>

        <!-- 提示信息 -->
        <div id="alert-container"></div>

        <!-- LLM Providers 配置 -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-cloud"></i> LLM Providers 配置</h5>
                <button class="btn btn-sm btn-light" id="addProviderBtn">
                    <i class="fas fa-plus"></i> 添加 Provider
                </button>
            </div>
            <div class="card-body">
                <div id="providersContainer">
                    <!-- Providers 将通过 JavaScript 动态加载 -->
                </div>
            </div>
        </div>

        <!-- 其他配置 -->
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="fas fa-sliders-h"></i> 其他配置参数</h5>
            </div>
            <div class="card-body">
                <div id="otherConfigContainer">
                    <!-- 其他配置将通过 JavaScript 动态加载 -->
                </div>
                <div class="mt-3">
                    <button class="btn btn-sm btn-outline-primary" id="addOtherConfigBtn">
                        <i class="fas fa-plus"></i> 添加配置项
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加 Provider 模态框 -->
    <div class="modal fade" id="addProviderModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加新的 LLM Provider</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addProviderForm">
                        <div class="mb-3">
                            <label for="providerName" class="form-label">Provider 名称</label>
                            <input type="text" class="form-control" id="providerName" required>
                            <div class="form-text">只能包含字母、数字和连字符</div>
                        </div>
                        <div class="mb-3">
                            <label for="providerDisplayName" class="form-label">显示名称</label>
                            <input type="text" class="form-control" id="providerDisplayName" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmAddProviderBtn">添加</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="/aicode/static/external/bootstrap.bundle.min.js"></script>
    <script src="/aicode/static/external/jquery-3.6.0.min.js"></script>
    
    <!-- 自定义JS -->
    <script src="/aicode/static/js/common.js"></script>
    <script src="/aicode/static/js/config_manager.js"></script>
    
    <script>
        // 页面初始化
        $(document).ready(function() {
            ConfigManager.init();
        });
    </script>
</body>
</html>