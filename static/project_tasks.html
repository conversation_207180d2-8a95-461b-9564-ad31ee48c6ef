<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务列表 - 全栈助手</title>

    <!-- Bootstrap CSS -->
    <link href="/aicode/static/external/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="/aicode/static/external/all.min.css" rel="stylesheet">
    <!-- Modern Theme -->
    <link href="/aicode/static/css/modern-theme.css" rel="stylesheet">

    <style>
        .requirement-container {
            max-height: 150px;
            overflow: hidden;
            position: relative;
        }

        .requirement-container.expanded {
            max-height: none;
        }

        .requirement-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 50px;
            background: linear-gradient(transparent, var(--bg-secondary));
            display: flex;
            justify-content: center;
            align-items: end;
            padding-bottom: 8px;
        }

        /* 调整项目信息卡片样式，使其更紧凑 */
        .project-info-card .card-body {
            padding: 0.75rem;
        }

        .project-info-card p {
            margin-bottom: 0.25rem;
        }

        .project-info-card .h4 {
            font-size: 1.25rem;
        }

        /* 保持会话任务的特殊样式 */
        .keep-session-task {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%) !important;
            padding: 10px 12px !important;
            border-radius: 6px !important;
            border-left: 4px solid #28a745 !important;
            box-shadow: 0 2px 4px rgba(40, 167, 69, 0.1) !important;
            transition: all 0.2s ease-in-out !important;
        }

        .keep-session-task:hover {
            background: linear-gradient(135deg, #c3e6cb 0%, #b8dabc 100%) !important;
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.15) !important;
            transform: translateY(-1px) !important;
        }
    </style>
</head>

<body>
    <!-- 主内容区域 - 只保留任务列表 -->
    <div class="container-fluid p-3">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">

            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group me-2">
                    <button type="button" class="btn btn-success" id="run-tasks-btn">
                        <i class="fas fa-play"></i> 运行任务
                    </button>
                    <button type="button" class="btn btn-warning" id="reset-tasks-btn">
                        <i class="fas fa-undo"></i> 重置任务
                    </button>
                    <button type="button" class="btn btn-info" id="stop-execution-btn">
                        <i class="fas fa-stop"></i> 停止运行
                    </button>
                    <button type="button" class="btn btn-danger" id="generate-tasks-btn">
                        <i class="fas fa-redo"></i> 生成任务
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="refresh-tasks-btn">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                    <button type="button" class="btn btn-outline-primary" id="add-task-btn">
                        <i class="fas fa-plus"></i> 添加任务
                    </button>                    
                </div>                
            </div>
            <div class="mb-1 ms-auto"><strong>Agent状态:</strong> <span id="project-run-state">-</span></div>
        </div>

        <!-- 项目信息卡片 -->
        <!-- <div class="row mb-3">
            <div class="col-md-12">
                <div class="card project-info-card">
                    <div class="card-body py-2">
                        <div class="row">
                            <div class="col-md-4">
                                <p class="mb-1"><strong>项目:</strong> <span id="project-name">-</span></p>
                                <p class="mb-1"><strong>LLM:</strong> <span id="project-provider">-</span></p>
                            </div>
                            <div class="col-md-4">
                                <p class="mb-1"><strong>创建时间:</strong> <span id="project-created-at">-</span></p>
                                <p class="mb-1"><strong>Agent状态:</strong> <span id="project-run-state">-</span></p>
                            </div>
                            <div class="col-md-4">
                                <p class="d-inline-block mr-3 mb-1"><strong>总任务:</strong> <span class="h4 mb-0 text-info" id="total-tasks">0</span></p>
                                <p class="d-inline-block mr-3 mb-1"><strong>已完成:</strong> <span class="h4 mb-0 text-success" id="completed-tasks">0</span></p>
                                <p class="d-inline-block mb-1"><strong>失败:</strong> <span class="h4 mb-0 text-danger" id="failed-tasks">0</span></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div> -->

        <!-- 任务列表容器 -->
        <div id="tasks-container">
            <div class="text-center py-5">
                <i class="fas fa-spinner fa-spin fa-3x text-muted mb-3"></i>
                <div>加载中...</div>
            </div>
        </div>
    </div>

    <!-- 添加任务模态框 -->
    <div class="modal fade" id="addTaskModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加新任务</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addTaskForm">
                        <div class="mb-3">
                            <label for="taskTitle" class="form-label">任务标题 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="taskTitle" required placeholder="请输入任务标题">
                        </div>
                        <div class="mb-3">
                            <label for="taskDescription" class="form-label">任务描述</label>
                            <textarea class="form-control" id="taskDescription" rows="4"
                                placeholder="请输入任务描述"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="taskType" class="form-label">任务类型</label>
                            <select class="form-select" id="taskType">
                                <option value="other">其他</option>
                                <option value="feature">功能</option>
                                <option value="bug">BUG</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="taskTestStrategy" class="form-label">测试策略</label>
                            <textarea class="form-control" id="taskTestStrategy" rows="2"
                                placeholder="如何验证任务完成"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="taskDependencies" class="form-label">依赖任务 (可选)</label>
                            <input type="text" class="form-control" id="taskDependencies"
                                placeholder="输入依赖的任务ID，多个用逗号分隔">
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="taskKeepSession">
                            <label class="form-check-label" for="taskKeepSession">
                                保持会话
                                <span class="text-muted">(使用前任务的会话状态)</span>
                            </label>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="executeImmediately">
                            <label class="form-check-label" for="executeImmediately">
                                立即执行
                                <span class="text-muted">(添加后立即开始执行任务)</span>
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmAddTask">添加任务</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑任务模态框 -->
    <div class="modal fade" id="editTaskModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑任务</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editTaskForm">
                        <input type="hidden" id="editTaskId">
                        <div class="mb-3">
                            <label for="editTaskTitle" class="form-label">任务标题 <span
                                    class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editTaskTitle" required>
                        </div>
                        <div class="mb-3">
                            <label for="editTaskDescription" class="form-label">任务描述</label>
                            <textarea class="form-control" id="editTaskDescription" rows="4"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="editTaskTestStrategy" class="form-label">测试策略</label>
                            <textarea class="form-control" id="editTaskTestStrategy" placeholder="如何验证任务完成"
                                rows="2"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="editTaskDependencies" class="form-label">依赖任务 (可选)</label>
                            <input type="text" class="form-control" id="editTaskDependencies">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmEditTask">保存更改</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 生成任务确认模态框 -->
    <div class="modal fade" id="generateTasksModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">生成任务</h5>
                </div>
                <div class="modal-body">
                    <div class="mt-3" id="taskCountSection" style="display: none;">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <label for="taskCount" class="form-label mb-0">任务数量</label>
                            </div>
                            <div class="col-md-9">
                                <input type="number" class="form-control" id="taskCount" min="1" max="20"
                                    placeholder="留空则自动确定任务数量">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">
                                <div class="form-text">
                                    <i class="fas fa-info-circle"></i>
                                    建议生成5-10个任务，留空将根据需求自动确定
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3" id="requirementDisplaySection" style="display: none;">
                        <label class="form-label">需求或问题</label>
                        <textarea class="form-control" id="requirementDisplay" rows="5" ></textarea>
                    </div>
                    <input type="hidden" id="generateTasksProjectId">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary"
                        onclick="TaskManager.confirmGenerateTasks('requirement')">确认生成</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 子任务管理模态框 -->
    <div class="modal fade" id="subtasksModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-tasks"></i> 子任务管理: <span id="subtask-task-title"></span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <button type="button" class="btn btn-success btn-sm"
                                onclick="TaskManager.runTaskFromSubtaskModal()">
                                <i class="fas fa-play"></i> 运行任务
                            </button>
                            <button type="button" class="btn btn-primary btn-sm"
                                onclick="TaskManager.showAddSubtaskForm()">
                                <i class="fas fa-plus"></i> 添加子任务
                            </button>
                            <button type="button" class="btn btn-info btn-sm" onclick="TaskManager.smartSplitTask()">
                                <i class="fas fa-magic"></i> 智能分解
                            </button>
                            <button type="button" class="btn btn-danger btn-sm"
                                onclick="TaskManager.deleteAllSubtasks()">
                                <i class="fas fa-trash-alt"></i> 删除所有子任务
                            </button>
                        </div>
                        <div>
                            <span class="badge bg-secondary" id="subtask-count">0个子任务</span>
                        </div>
                    </div>

                    <!-- 添加子任务表单 -->
                    <div id="add-subtask-form" class="card mb-3" style="display: none;">
                        <div class="card-body">
                            <h6 class="card-title">添加新子任务</h6>
                            <form id="subtaskForm">
                                <input type="hidden" id="current-task-id">
                                <input type="hidden" id="edit-subtask-id">
                                <div class="mb-3">
                                    <label class="form-label">子任务名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="subtask-name" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">子任务描述</label>
                                    <textarea class="form-control" id="subtask-description" rows="3"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">检查项 (每行一项)</label>
                                    <textarea class="form-control" id="subtask-checklist" rows="4"
                                        placeholder="请输入检查项，每行一项"></textarea>
                                </div>
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-primary" onclick="TaskManager.saveSubtask()">
                                        <i class="fas fa-save"></i> 保存
                                    </button>
                                    <button type="button" class="btn btn-secondary"
                                        onclick="TaskManager.cancelSubtaskForm()">
                                        取消
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 子任务列表 -->
                    <div id="subtasks-container">
                        <div class="text-center py-4 text-muted">
                            <i class="fas fa-tasks fa-3x mb-3"></i>
                            <p>暂无子任务，点击"添加子任务"或"智能分解"来创建</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="/aicode/static/external/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="/aicode/static/external/jquery-3.6.0.min.js"></script>

    <!-- 自定义JS -->
    <script src="/aicode/static/js/common.js"></script>
    <script src="/aicode/static/js/projects.js"></script>
    <script src="/aicode/static/js/tasks.js"></script>

    <script>
        // 页面初始化
        $(document).ready(function () {
            const projectId = Router.getParam('project_id');
            if (projectId) {
                TaskManager.initTasksPage(projectId);
            } else {
                Utils.showAlert('未指定项目ID', 'error');
            }
        });
    </script>
</body>

</html>