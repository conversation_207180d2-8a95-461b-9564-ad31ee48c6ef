/**
 * 互联网风格增强样式
 * 添加更多活泼、现代的UI效果
 */

/* ===== 全局动画增强 ===== */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes slideInFromLeft {
    0% { transform: translateX(-100%); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
}

@keyframes slideInFromRight {
    0% { transform: translateX(100%); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
}

@keyframes slideInFromTop {
    0% { transform: translateY(-100%); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
}

@keyframes slideInFromBottom {
    0% { transform: translateY(100%); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
}

@keyframes zoomIn {
    0% { transform: scale(0.8); opacity: 0; }
    100% { transform: scale(1); opacity: 1; }
}

@keyframes rainbow {
    0% { filter: hue-rotate(0deg); }
    100% { filter: hue-rotate(360deg); }
}

/* ===== 页面加载动画 ===== */
.page-enter {
    animation: slideInFromBottom 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-enter {
    animation: zoomIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-enter {
    animation: slideInFromLeft 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.navbar-enter {
    animation: slideInFromTop 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== 统计卡片增强 ===== */
.stats-card {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.stats-card:hover::before {
    transform: translateX(100%);
}

.stats-card:hover {
    transform: translateY(-10px) scale(1.03);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.stats-icon {
    animation: float 3s ease-in-out infinite;
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 800;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: pulse 2s infinite;
}

/* ===== 按钮特效增强 ===== */
.btn-glow {
    position: relative;
    overflow: hidden;
}

.btn-glow::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.btn-glow:active::after {
    width: 300px;
    height: 300px;
}

.btn-rainbow {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #ffeaa7);
    background-size: 300% 300%;
    animation: rainbow 3s ease infinite;
}

.btn-bounce:hover {
    animation: pulse 0.5s infinite;
}

/* ===== 输入框增强 ===== */
.form-control-modern {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.form-control-modern:focus {
    background: rgba(255, 255, 255, 0.2);
    border-color: #667eea;
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
    transform: translateY(-2px);
}

.form-control-modern::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

/* ===== 加载动画 ===== */
.loading-dots {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 80px;
}

.loading-dots div {
    position: absolute;
    top: 33px;
    width: 13px;
    height: 13px;
    border-radius: 50%;
    background: #667eea;
    animation-timing-function: cubic-bezier(0, 1, 1, 0);
}

.loading-dots div:nth-child(1) {
    left: 8px;
    animation: loading-dots1 0.6s infinite;
}

.loading-dots div:nth-child(2) {
    left: 8px;
    animation: loading-dots2 0.6s infinite;
}

.loading-dots div:nth-child(3) {
    left: 32px;
    animation: loading-dots2 0.6s infinite;
}

.loading-dots div:nth-child(4) {
    left: 56px;
    animation: loading-dots3 0.6s infinite;
}

@keyframes loading-dots1 {
    0% { transform: scale(0); }
    100% { transform: scale(1); }
}

@keyframes loading-dots3 {
    0% { transform: scale(1); }
    100% { transform: scale(0); }
}

@keyframes loading-dots2 {
    0% { transform: translate(0, 0); }
    100% { transform: translate(24px, 0); }
}

/* ===== 通知消息增强 ===== */
.alert-modern {
    border: none;
    border-radius: 15px;
    backdrop-filter: blur(20px);
    border-left: 4px solid;
    position: relative;
    overflow: hidden;
    animation: slideInFromRight 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.alert-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    animation: alertShine 2s infinite;
}

@keyframes alertShine {
    0% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
    100% { transform: translateX(100%); }
}

.alert-success-modern {
    background: rgba(52, 211, 153, 0.1);
    border-left-color: #34d399;
    color: #065f46;
}

.alert-danger-modern {
    background: rgba(248, 113, 113, 0.1);
    border-left-color: #f87171;
    color: #7f1d1d;
}

.alert-warning-modern {
    background: rgba(251, 191, 36, 0.1);
    border-left-color: #fbbf24;
    color: #78350f;
}

.alert-info-modern {
    background: rgba(96, 165, 250, 0.1);
    border-left-color: #60a5fa;
    color: #1e3a8a;
}

/* ===== 工具提示增强 ===== */
.tooltip-modern {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 10px;
    padding: 8px 12px;
    font-size: 12px;
    font-weight: 500;
    animation: zoomIn 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== 进度条增强 ===== */
.progress-modern {
    height: 10px;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
    overflow: hidden;
    position: relative;
}

.progress-bar-modern {
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 10px;
    position: relative;
    overflow: hidden;
}

.progress-bar-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: progressShine 2s infinite;
}

@keyframes progressShine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* ===== 波纹效果 ===== */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* ===== 悬浮卡片效果 ===== */
.hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* ===== 文字打字机效果 ===== */
.typewriter {
    overflow: hidden;
    border-right: 2px solid #667eea;
    white-space: nowrap;
    animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink-caret {
    from, to { border-color: transparent; }
    50% { border-color: #667eea; }
}

/* ===== 响应式动画 ===== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
