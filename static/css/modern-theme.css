/**
 * 现代化主题样式
 * 用于全栈助手项目
 */

/* ===== 全局变量 ===== */
:root {
    /* 主色调 */
    --primary-color: #4f46e5;
    --primary-hover: #4338ca;
    --primary-light: #eef2ff;

    /* 辅助色 */
    --secondary-color: #64748b;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #3b82f6;

    /* 灰度色 */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* 背景色 */
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-sidebar: #1f2937;

    /* 文字颜色 */
    --text-primary: #111827;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;

    /* 边框 */
    --border-color: #e5e7eb;
    --border-radius: 0.75rem;
    --border-radius-sm: 0.5rem;
    --border-radius-lg: 1rem;

    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);

    /* 过渡 */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== 全局重置 ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
                 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    line-height: 1.6;
    font-size: 14px;
}

/* ===== 导航栏优化 ===== */
.navbar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    box-shadow: var(--shadow-md);
    backdrop-filter: blur(10px);
    padding: 0.75rem 1.5rem;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
    letter-spacing: -0.025em;
    transition: var(--transition);
}

.navbar-brand:hover {
    transform: translateY(-1px);
}

.navbar .form-select {
    border-radius: var(--border-radius-sm);
    border: 1px solid rgba(255, 255, 255, 0.2);
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    backdrop-filter: blur(10px);
    transition: var(--transition);
}

.navbar .form-select:focus {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.navbar .form-select option {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.navbar-text {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

/* ===== 侧边栏优化 ===== */
.sidebar {
    background: var(--bg-sidebar);
    min-height: 100vh;
    box-shadow: var(--shadow-lg);
    padding-top: 1rem;
    position: relative;
    transition: all 0.3s ease;
    width: auto;
}

/* 侧边栏收起/展开按钮 */
.sidebar-toggle {
    position: absolute;
    top: 10px;
    right: -15px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    border: none;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1000;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
}

.sidebar-toggle:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
}

.sidebar-toggle i {
    font-size: 14px;
}

/* 侧边栏收起状态 */
.sidebar.collapsed {
    width: 70px !important;
    min-width: 70px !important;
    max-width: 70px !important;
}

.sidebar.collapsed .menu-text {
    display: none;
}

.sidebar.collapsed .sidebar-heading {
    display: none;
}

.sidebar.collapsed .nav-link {
    justify-content: center;
    padding: 0.75rem 0.5rem;
    margin: 0.25rem auto;
    width: 50px;
}

.sidebar.collapsed .nav-link i {
    width: auto;
    margin: 0;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.75);
    padding: 0.5rem 1.25rem;
    margin: 0.125rem 0.75rem;
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    white-space: nowrap;
}

.sidebar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(4px);
}

.sidebar.collapsed .nav-link:hover {
    transform: scale(1.05);
}

.sidebar .nav-link.active {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    box-shadow: var(--shadow);
}

.sidebar .nav-link i {
    width: 1.25rem;
    text-align: center;
    flex-shrink: 0;
}

.menu-text {
    transition: opacity 0.3s ease;
}

.sidebar-heading {
    color: rgba(255, 255, 255, 0.5);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 0.5rem 1.25rem;
    margin-top: 1.5rem;
    transition: var(--transition);
}

/* 主内容区域过渡效果 */
.main-content {
    transition: all 0.3s ease;
}

/* ===== 卡片优化 ===== */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
    background-color: var(--bg-primary);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-4px);
}

.card-header {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    border-bottom: 2px solid var(--border-color);
    padding: 1rem 1.5rem;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

/* 统计卡片 */
.border-left-primary {
    border-left: 4px solid var(--primary-color) !important;
}

.border-left-success {
    border-left: 4px solid var(--success-color) !important;
}

.border-left-info {
    border-left: 4px solid var(--info-color) !important;
}

.border-left-warning {
    border-left: 4px solid var(--warning-color) !important;
}

.border-left-danger {
    border-left: 4px solid var(--danger-color) !important;
}

/* ===== 按钮优化 ===== */
.btn {
    border-radius: var(--border-radius-sm);
    padding: 0.5rem 1.25rem;
    font-weight: 500;
    transition: var(--transition);
    border: none;
    box-shadow: var(--shadow-sm);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-hover), #3730a3);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #059669);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #dc2626);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #d97706);
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color), #2563eb);
}

.btn-outline-secondary,
.btn-outline-primary,
.btn-outline-info {
    background-color: transparent;
    border: 1.5px solid currentColor;
    box-shadow: none;
}

.btn-outline-secondary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    border-color: transparent;
}

.btn-outline-info:hover {
    background: linear-gradient(135deg, var(--info-color), #2563eb);
    border-color: transparent;
}

/* 按钮组 */
.btn-group .btn {
    margin-right: 0.5rem;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

/* ===== 徽章优化 ===== */
.badge {
    padding: 0.375rem 0.75rem;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    font-size: 0.75rem;
    letter-spacing: 0.025em;
}

.bg-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover)) !important;
}

.bg-success {
    background: linear-gradient(135deg, var(--success-color), #059669) !important;
}

.bg-danger {
    background: linear-gradient(135deg, var(--danger-color), #dc2626) !important;
}

.bg-warning {
    background: linear-gradient(135deg, var(--warning-color), #d97706) !important;
}

.bg-info {
    background: linear-gradient(135deg, var(--info-color), #2563eb) !important;
}

.bg-secondary {
    background: linear-gradient(135deg, var(--secondary-color), #475569) !important;
}

/* ===== 表单优化 ===== */
.form-control,
.form-select {
    border-radius: var(--border-radius-sm);
    border: 1.5px solid var(--border-color);
    padding: 0.625rem 1rem;
    transition: var(--transition);
    background-color: var(--bg-primary);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.form-text {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

textarea.form-control {
    min-height: 100px;
    resize: vertical;
}

/* ===== 模态框优化 ===== */
.modal-content {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
}

.modal-header {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    border-bottom: 2px solid var(--border-color);
    padding: 1.25rem 1.5rem;
}

.modal-title {
    font-weight: 700;
    color: var(--text-primary);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    background-color: var(--gray-50);
    border-top: 1px solid var(--border-color);
    padding: 1rem 1.5rem;
}

/* ===== 表格优化 ===== */
.table {
    border-collapse: separate;
    border-spacing: 0;
}

.table thead th {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    border-bottom: 2px solid var(--border-color);
    color: var(--text-primary);
    font-weight: 600;
    padding: 1rem;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
}

.table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--border-color);
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: var(--gray-50);
}

.table-bordered {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
}

/* ===== 提醒消息优化 ===== */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 1rem 1.25rem;
    box-shadow: var(--shadow);
    backdrop-filter: blur(10px);
}

.alert-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: #047857;
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: #b91c1c;
    border-left: 4px solid var(--danger-color);
}

.alert-warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: #b45309;
    border-left: 4px solid var(--warning-color);
}

.alert-info {
    background-color: rgba(59, 130, 246, 0.1);
    color: #1e40af;
    border-left: 4px solid var(--info-color);
}

/* ===== 加载状态 ===== */
.spinner-border {
    border-width: 2px;
}

.fa-spin {
    animation: fa-spin 1s infinite linear;
}

@keyframes fa-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== 页面标题 ===== */
.border-bottom {
    border-bottom: 2px solid var(--border-color) !important;
    padding-bottom: 1rem !important;
    margin-bottom: 1.5rem !important;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    color: var(--text-primary);
    letter-spacing: -0.025em;
}

.h2 {
    font-size: 1.75rem;
}

/* ===== 工具栏 ===== */
.btn-toolbar {
    gap: 0.5rem;
}

/* ===== 任务卡片 ===== */
.task-card {
    transition: var(--transition);
    border-radius: var(--border-radius);
}

.task-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

/* ===== 文件列表 ===== */
.file-item {
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
}

.file-item:hover {
    background-color: var(--gray-50);
}

/* ===== 滚动条优化 ===== */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background-color: var(--gray-100);
}

::-webkit-scrollbar-thumb {
    background-color: var(--gray-400);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background-color: var(--gray-500);
}

/* ===== 响应式优化 ===== */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: 250px;
        height: 100vh;
        z-index: 1000;
        transition: var(--transition);
    }

    .sidebar.show {
        left: 0;
    }

    .card {
        margin-bottom: 1rem;
    }

    .btn-group {
        flex-wrap: wrap;
    }

    .btn-group .btn {
        margin-bottom: 0.5rem;
    }
}

/* ===== 深色模式支持 ===== */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #1f2937;
        --bg-secondary: #111827;
        --bg-sidebar: #0f172a;
        --text-primary: #f9fafb;
        --text-secondary: #9ca3af;
        --border-color: #374151;
    }

    .card {
        background-color: var(--bg-primary);
    }

    .table tbody tr:hover {
        background-color: rgba(255, 255, 255, 0.05);
    }
}

/* ===== 动画效果 ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* ===== 编辑器样式 ===== */
.editor-container {
    height: calc(100vh - 120px);
    display: flex;
    flex-direction: column;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.editor-toolbar {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    border-bottom: 2px solid var(--border-color);
    padding: 0.75rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.editor-content {
    flex: 1;
    display: flex;
    min-height: 0;
}

.editor-pane {
    flex: 1;
    border-right: 1px solid var(--border-color);
}

.editor-pane:last-child {
    border-right: none;
}

.editor-textarea {
    width: 100%;
    height: 100%;
    border: none;
    resize: none;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.6;
    padding: 1.5rem;
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.editor-textarea:focus {
    outline: none;
}

.preview-pane {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem;
    background-color: var(--bg-primary);
}

/* ===== 进度条 ===== */
.progress {
    height: 8px;
    border-radius: var(--border-radius-sm);
    background-color: var(--gray-200);
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
    transition: width 0.3s ease;
}

/* ===== 分隔线 ===== */
hr {
    border: none;
    border-top: 2px solid var(--border-color);
    margin: 1.5rem 0;
}

/* ===== 链接 ===== */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

/* ===== 代码块 ===== */
code {
    background-color: var(--gray-100);
    color: var(--danger-color);
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    font-size: 0.875em;
}

pre {
    background-color: var(--gray-100);
    border-radius: var(--border-radius-sm);
    padding: 1rem;
    overflow-x: auto;
}

pre code {
    background-color: transparent;
    color: var(--text-primary);
    padding: 0;
}
