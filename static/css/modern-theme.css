/**
 * 现代化主题样式
 * 用于全栈助手项目
 */

/* ===== 全局变量 - 互联网风格配色 ===== */
:root {
    /* 主色调 - 活泼的渐变色系 */
    --primary-color: #667eea;
    --primary-hover: #764ba2;
    --primary-light: #f093fb;
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --primary-gradient-hover: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);

    /* 辅助色 - 更加鲜艳活泼 */
    --secondary-color: #a78bfa;
    --success-color: #34d399;
    --danger-color: #f87171;
    --warning-color: #fbbf24;
    --info-color: #60a5fa;

    /* 特殊渐变色 */
    --success-gradient: linear-gradient(135deg, #34d399 0%, #10b981 100%);
    --danger-gradient: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
    --warning-gradient: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    --info-gradient: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);

    /* 灰度色 - 更柔和 */
    --gray-50: #fafafa;
    --gray-100: #f5f5f5;
    --gray-200: #eeeeee;
    --gray-300: #e0e0e0;
    --gray-400: #bdbdbd;
    --gray-500: #9e9e9e;
    --gray-600: #757575;
    --gray-700: #616161;
    --gray-800: #424242;
    --gray-900: #212121;

    /* 背景色 - 更有层次感 */
    --bg-primary: #ffffff;
    --bg-secondary: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
    --bg-sidebar: linear-gradient(180deg, #2d3748 0%, #1a202c 100%);
    --bg-card: rgba(255, 255, 255, 0.95);
    --bg-glass: rgba(255, 255, 255, 0.1);

    /* 文字颜色 */
    --text-primary: #2d3748;
    --text-secondary: #718096;
    --text-light: #a0aec0;
    --text-accent: #667eea;

    /* 边框 - 更圆润 */
    --border-color: #e2e8f0;
    --border-radius: 1rem;
    --border-radius-sm: 0.75rem;
    --border-radius-lg: 1.5rem;
    --border-radius-xl: 2rem;

    /* 阴影 - 更柔和深度 */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.06);
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
    --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.12);
    --shadow-colored: 0 8px 25px rgba(102, 126, 234, 0.15);

    /* 过渡 - 更流畅 */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* ===== 全局重置 ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
                 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    color: var(--text-primary);
    background: var(--bg-secondary);
    line-height: 1.7;
    font-size: 14px;
    overflow-x: hidden;
    position: relative;
}

/* 添加动态背景效果 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(118, 75, 162, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(52, 211, 153, 0.05) 0%, transparent 50%);
    z-index: -1;
    animation: backgroundFloat 20s ease-in-out infinite;
}

@keyframes backgroundFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-10px) rotate(1deg); }
    66% { transform: translateY(5px) rotate(-1deg); }
}

/* ===== 导航栏优化 - 互联网风格 ===== */
.navbar {
    background: var(--primary-gradient);
    box-shadow: var(--shadow-colored);
    backdrop-filter: blur(20px);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

/* 导航栏动态背景效果 */
.navbar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: navbarShine 3s infinite;
}

@keyframes navbarShine {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: 100%; }
}

.navbar-brand {
    font-weight: 800;
    font-size: 1.4rem;
    letter-spacing: -0.025em;
    transition: var(--transition-bounce);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1;
}

.navbar-brand:hover {
    transform: translateY(-2px) scale(1.05);
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.navbar-brand i {
    margin-right: 0.5rem;
    animation: iconPulse 2s infinite;
}

@keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.navbar .form-select {
    border-radius: var(--border-radius);
    border: 2px solid rgba(255, 255, 255, 0.2);
    background: var(--bg-glass);
    color: white;
    backdrop-filter: blur(20px);
    transition: var(--transition-bounce);
    font-weight: 500;
    box-shadow: var(--shadow-sm);
}

.navbar .form-select:focus {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.navbar .form-select:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.navbar .form-select option {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    padding: 0.5rem;
}

.navbar-text {
    color: rgba(255, 255, 255, 0.95);
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* ===== 侧边栏优化 - 现代玻璃效果 ===== */
.sidebar {
    background: var(--bg-sidebar);
    min-height: 100vh;
    box-shadow: var(--shadow-xl);
    padding-top: 1rem;
    position: relative;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    width: auto;
    backdrop-filter: blur(20px);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}

/* 侧边栏装饰性背景 */
.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 10% 20%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 90% 80%, rgba(118, 75, 162, 0.1) 0%, transparent 50%);
    z-index: 0;
}

/* 侧边栏收起/展开按钮 - 更有趣的设计 */
.sidebar-toggle {
    position: absolute;
    top: 15px;
    right: -18px;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: var(--primary-gradient);
    border: 3px solid rgba(255, 255, 255, 0.2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1001;
    box-shadow: var(--shadow-colored);
    transition: var(--transition-bounce);
    backdrop-filter: blur(10px);
}

.sidebar-toggle:hover {
    transform: scale(1.15) rotate(180deg);
    box-shadow: var(--shadow-xl);
    border-color: rgba(255, 255, 255, 0.4);
}

.sidebar-toggle:active {
    transform: scale(0.95);
}

.sidebar-toggle i {
    font-size: 16px;
    transition: var(--transition);
}

/* 侧边栏收起状态 */
.sidebar.collapsed {
    width: 70px !important;
    min-width: 70px !important;
    max-width: 70px !important;
}

.sidebar.collapsed .menu-text {
    display: none;
}

.sidebar.collapsed .sidebar-heading {
    display: none;
}

.sidebar.collapsed .nav-link {
    justify-content: center;
    padding: 0.75rem 0.5rem;
    margin: 0.25rem auto;
    width: 50px;
}

.sidebar.collapsed .nav-link i {
    width: auto;
    margin: 0;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1.25rem;
    margin: 0.25rem 0.75rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    transition: var(--transition-bounce);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    white-space: nowrap;
    position: relative;
    z-index: 1;
    overflow: hidden;
}

/* 悬停效果 - 更有趣的动画 */
.sidebar .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: var(--transition);
    z-index: -1;
}

.sidebar .nav-link:hover::before {
    left: 100%;
}

.sidebar .nav-link:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    transform: translateX(8px) scale(1.02);
    box-shadow: var(--shadow-md);
}

.sidebar.collapsed .nav-link:hover {
    transform: scale(1.1) rotate(5deg);
}

.sidebar .nav-link.active {
    background: var(--primary-gradient);
    color: white;
    box-shadow: var(--shadow-colored);
    transform: translateX(4px);
}

.sidebar .nav-link.active::after {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 60%;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 2px;
}

.sidebar .nav-link i {
    width: 1.25rem;
    text-align: center;
    flex-shrink: 0;
}

.menu-text {
    transition: opacity 0.3s ease;
}

.sidebar-heading {
    color: rgba(255, 255, 255, 0.5);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 0.5rem 1.25rem;
    margin-top: 1.5rem;
    transition: var(--transition);
}

/* 主内容区域过渡效果 */
.main-content {
    transition: all 0.3s ease;
}

/* ===== 卡片优化 - 现代玻璃拟态设计 ===== */
.card {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition-bounce);
    background: var(--bg-card);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    position: relative;
}

/* 卡片装饰性背景 */
.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--primary-gradient);
    z-index: 1;
}

.card:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-8px) scale(1.02);
    border-color: rgba(102, 126, 234, 0.3);
}

.card-header {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1.25rem 1.75rem;
    font-weight: 700;
    backdrop-filter: blur(10px);
    position: relative;
}

.card-header h6 {
    margin: 0;
    color: var(--text-accent);
    font-size: 1.1rem;
}

.card-body {
    padding: 1.75rem;
    position: relative;
}

/* 统计卡片 */
.border-left-primary {
    border-left: 4px solid var(--primary-color) !important;
}

.border-left-success {
    border-left: 4px solid var(--success-color) !important;
}

.border-left-info {
    border-left: 4px solid var(--info-color) !important;
}

.border-left-warning {
    border-left: 4px solid var(--warning-color) !important;
}

.border-left-danger {
    border-left: 4px solid var(--danger-color) !important;
}

/* ===== 按钮优化 - 更有活力的设计 ===== */
.btn {
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: var(--transition-bounce);
    border: none;
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
    text-transform: none;
    letter-spacing: 0.025em;
}

/* 按钮闪光效果 */
.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition);
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: var(--shadow-lg);
}

.btn:active {
    transform: translateY(-1px) scale(1.02);
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: var(--shadow-colored);
}

.btn-primary:hover {
    background: var(--primary-gradient-hover);
    box-shadow: 0 12px 30px rgba(102, 126, 234, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #059669);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #dc2626);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #d97706);
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color), #2563eb);
}

.btn-outline-secondary,
.btn-outline-primary,
.btn-outline-info {
    background-color: transparent;
    border: 1.5px solid currentColor;
    box-shadow: none;
}

.btn-outline-secondary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    border-color: transparent;
}

.btn-outline-info:hover {
    background: linear-gradient(135deg, var(--info-color), #2563eb);
    border-color: transparent;
}

/* 按钮组 */
.btn-group .btn {
    margin-right: 0.5rem;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

/* ===== 徽章优化 ===== */
.badge {
    padding: 0.375rem 0.75rem;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    font-size: 0.75rem;
    letter-spacing: 0.025em;
}

.bg-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover)) !important;
}

.bg-success {
    background: linear-gradient(135deg, var(--success-color), #059669) !important;
}

.bg-danger {
    background: linear-gradient(135deg, var(--danger-color), #dc2626) !important;
}

.bg-warning {
    background: linear-gradient(135deg, var(--warning-color), #d97706) !important;
}

.bg-info {
    background: linear-gradient(135deg, var(--info-color), #2563eb) !important;
}

.bg-secondary {
    background: linear-gradient(135deg, var(--secondary-color), #475569) !important;
}

/* ===== 表单优化 ===== */
.form-control,
.form-select {
    border-radius: var(--border-radius-sm);
    border: 1.5px solid var(--border-color);
    padding: 0.625rem 1rem;
    transition: var(--transition);
    background-color: var(--bg-primary);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.form-text {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

textarea.form-control {
    min-height: 100px;
    resize: vertical;
}

/* ===== 模态框优化 ===== */
.modal-content {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
}

.modal-header {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    border-bottom: 2px solid var(--border-color);
    padding: 1.25rem 1.5rem;
}

.modal-title {
    font-weight: 700;
    color: var(--text-primary);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    background-color: var(--gray-50);
    border-top: 1px solid var(--border-color);
    padding: 1rem 1.5rem;
}

/* ===== 表格优化 ===== */
.table {
    border-collapse: separate;
    border-spacing: 0;
}

.table thead th {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    border-bottom: 2px solid var(--border-color);
    color: var(--text-primary);
    font-weight: 600;
    padding: 1rem;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
}

.table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--border-color);
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: var(--gray-50);
}

.table-bordered {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
}

/* ===== 提醒消息优化 ===== */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 1rem 1.25rem;
    box-shadow: var(--shadow);
    backdrop-filter: blur(10px);
}

.alert-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: #047857;
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: #b91c1c;
    border-left: 4px solid var(--danger-color);
}

.alert-warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: #b45309;
    border-left: 4px solid var(--warning-color);
}

.alert-info {
    background-color: rgba(59, 130, 246, 0.1);
    color: #1e40af;
    border-left: 4px solid var(--info-color);
}

/* ===== 加载状态 ===== */
.spinner-border {
    border-width: 2px;
}

.fa-spin {
    animation: fa-spin 1s infinite linear;
}

@keyframes fa-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== 页面标题 ===== */
.border-bottom {
    border-bottom: 2px solid var(--border-color) !important;
    padding-bottom: 1rem !important;
    margin-bottom: 1.5rem !important;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    color: var(--text-primary);
    letter-spacing: -0.025em;
}

.h2 {
    font-size: 1.75rem;
}

/* ===== 工具栏 ===== */
.btn-toolbar {
    gap: 0.5rem;
}

/* ===== 任务卡片 ===== */
.task-card {
    transition: var(--transition);
    border-radius: var(--border-radius);
}

.task-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

/* ===== 文件列表 ===== */
.file-item {
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
}

.file-item:hover {
    background-color: var(--gray-50);
}

/* ===== 滚动条优化 ===== */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background-color: var(--gray-100);
}

::-webkit-scrollbar-thumb {
    background-color: var(--gray-400);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background-color: var(--gray-500);
}

/* ===== 响应式优化 ===== */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: 250px;
        height: 100vh;
        z-index: 1000;
        transition: var(--transition);
    }

    .sidebar.show {
        left: 0;
    }

    .card {
        margin-bottom: 1rem;
    }

    .btn-group {
        flex-wrap: wrap;
    }

    .btn-group .btn {
        margin-bottom: 0.5rem;
    }
}

/* ===== 深色模式支持 ===== */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #1f2937;
        --bg-secondary: #111827;
        --bg-sidebar: #0f172a;
        --text-primary: #f9fafb;
        --text-secondary: #9ca3af;
        --border-color: #374151;
    }

    .card {
        background-color: var(--bg-primary);
    }

    .table tbody tr:hover {
        background-color: rgba(255, 255, 255, 0.05);
    }
}

/* ===== 动画效果 ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* ===== 编辑器样式 ===== */
.editor-container {
    height: calc(100vh - 120px);
    display: flex;
    flex-direction: column;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.editor-toolbar {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    border-bottom: 2px solid var(--border-color);
    padding: 0.75rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.editor-content {
    flex: 1;
    display: flex;
    min-height: 0;
}

.editor-pane {
    flex: 1;
    border-right: 1px solid var(--border-color);
}

.editor-pane:last-child {
    border-right: none;
}

.editor-textarea {
    width: 100%;
    height: 100%;
    border: none;
    resize: none;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.6;
    padding: 1.5rem;
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.editor-textarea:focus {
    outline: none;
}

.preview-pane {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem;
    background-color: var(--bg-primary);
}

/* ===== 进度条 ===== */
.progress {
    height: 8px;
    border-radius: var(--border-radius-sm);
    background-color: var(--gray-200);
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
    transition: width 0.3s ease;
}

/* ===== 分隔线 ===== */
hr {
    border: none;
    border-top: 2px solid var(--border-color);
    margin: 1.5rem 0;
}

/* ===== 链接 ===== */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

/* ===== 代码块 ===== */
code {
    background-color: var(--gray-100);
    color: var(--danger-color);
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    font-size: 0.875em;
}

pre {
    background-color: var(--gray-100);
    border-radius: var(--border-radius-sm);
    padding: 1rem;
    overflow-x: auto;
}

pre code {
    background-color: transparent;
    color: var(--text-primary);
    padding: 0;
}
