/* 
  _____   ____  ____   ____ _______ 
 |  __ \ / __ \|  _ \ / __ \__   __|
 | |__) | |  | | |_) | |  | | | |   
 |  _  /| |  | |  _ <| |  | | | |   
 | | \ \| |__| | |_) | |__| | | |   
 |_|  \_\\____/|____/ \____/  |_|   
 
  */
@font-face {
  font-family: "graph.iconfont";
  src: url('@{iconfont-path}iconfont/graph.iconfont.eot');
  src: url('@{iconfont-path}iconfont/graph.iconfont.eot?#iefix') format('eot'),
    url('@{iconfont-path}iconfont/graph.iconfont.woff') format('woff'),
    url('@{iconfont-path}iconfont/graph.iconfont.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

.font-graph {
  vertical-align: middle;
}

.font-graph:after {
  display: inline-block;
  font-family: "graph.iconfont";
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.font-graph-lg {
  font-size: 1.3333333333333333em;
  line-height: 0.75em;
  vertical-align: -15%;
}
.font-graph-2x { font-size: 2em; }
.font-graph-3x { font-size: 3em; }
.font-graph-4x { font-size: 4em; }
.font-graph-5x { font-size: 5em; }
.font-graph-fw {
  width: 1.2857142857142858em;
  text-align: center;
}

.font-graph-geSprite-arrow:after { content: "\EA01" }
.font-graph-geSprite-bold:after { content: "\EA02" }
.font-graph-geSprite-bottom:after { content: "\EA03" }
.font-graph-geSprite-center:after { content: "\EA04" }
.font-graph-geSprite-code:after { content: "\EA05" }
.font-graph-geSprite-connection:after { content: "\EA06" }
.font-graph-geSprite-curved:after { content: "\EA07" }
.font-graph-geSprite-delete:after { content: "\EA08" }
.font-graph-geSprite-dots:after { content: "\EA09" }
.font-graph-geSprite-entity:after { content: "\EA0A" }
.font-graph-geSprite-fit:after { content: "\EA0B" }
.font-graph-geSprite-fontbackground:after { content: "\EA0C" }
.font-graph-geSprite-fontcolor:after { content: "\EA0D" }
.font-graph-geSprite-formatpanel:after { content: "\EA0E" }
.font-graph-geSprite-horizontalelbow:after { content: "\EA0F" }
.font-graph-geSprite-horizontalisometric:after { content: "\EA10" }
.font-graph-geSprite-horizontalrule:after { content: "\EA11" }
.font-graph-geSprite-indent:after { content: "\EA12" }
.font-graph-geSprite-italic:after { content: "\EA13" }
.font-graph-geSprite-justifyfull:after { content: "\EA14" }
.font-graph-geSprite-left:after { content: "\EA15" }
.font-graph-geSprite-link:after { content: "\EA16" }
.font-graph-geSprite-linkedge:after { content: "\EA17" }
.font-graph-geSprite-middle:after { content: "\EA18" }
.font-graph-geSprite-orderedlist:after { content: "\EA19" }
.font-graph-geSprite-orthogonal:after { content: "\EA1A" }
.font-graph-geSprite-outdent:after { content: "\EA1B" }
.font-graph-geSprite-plus:after { content: "\EA1C" }
.font-graph-geSprite-redo:after { content: "\EA1D" }
.font-graph-geSprite-removeformat:after { content: "\EA1E" }
.font-graph-geSprite-right:after { content: "\EA1F" }
.font-graph-geSprite-shadow:after { content: "\EA20" }
.font-graph-geSprite-simplearrow:after { content: "\EA21" }
.font-graph-geSprite-straight:after { content: "\EA22" }
.font-graph-geSprite-strokecolor:after { content: "\EA23" }
.font-graph-geSprite-subscript:after { content: "\EA24" }
.font-graph-geSprite-superscript:after { content: "\EA25" }
.font-graph-geSprite-table:after { content: "\EA26" }
.font-graph-geSprite-toback:after { content: "\EA27" }
.font-graph-geSprite-tofront:after { content: "\EA28" }
.font-graph-geSprite-top:after { content: "\EA29" }
.font-graph-geSprite-underline:after { content: "\EA2A" }
.font-graph-geSprite-undo:after { content: "\EA2B" }
.font-graph-geSprite-unorderedlist:after { content: "\EA2C" }
.font-graph-geSprite-vertical:after { content: "\EA2D" }
.font-graph-geSprite-verticalelbow:after { content: "\EA2E" }
.font-graph-geSprite-verticalisometric:after { content: "\EA2F" }
.font-graph-geSprite-zoomin:after { content: "\EA30" }
.font-graph-geSprite-zoomout:after { content: "\EA31" }
.font-graph-geSprite-zz-填充色_icon:after { content: "\EA32" }
.font-graph-geSprite-zz-复选框:after { content: "\EA33" }
.font-graph-geSprite-zz-查看画图2:after { content: "\EA34" }
.font-graph-geSprite-zz-线条颜色_icon:after { content: "\EA35" }
