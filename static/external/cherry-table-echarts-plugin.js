!function(t,r){"object"==typeof exports&&"undefined"!=typeof module?module.exports=r():"function"==typeof define&&define.amd?define(r):(t="undefined"!=typeof globalThis?globalThis:t||self).CherryTableEchartsPlugin=r()}(this,(function(){"use strict";var t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function r(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function e(t,r){return t(r={exports:{}},r.exports),r.exports}function n(t){return t&&t.default||t}var o,a,i=function(t){return t&&t.Math===Math&&t},c=i("object"==typeof globalThis&&globalThis)||i("object"==typeof window&&window)||i("object"==typeof self&&self)||i("object"==typeof t&&t)||i("object"==typeof t&&t)||function(){return this}()||Function("return this")(),u=function(t){try{return!!t()}catch(t){return!0}},l=!u((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),s=l,f=Function.prototype,p=f.apply,h=f.call,d="object"==typeof Reflect&&Reflect.apply||(s?h.bind(p):function(){return h.apply(p,arguments)}),y=Function.prototype,v=y.call,g=s&&y.bind.bind(v,v),b=s?g:function(t){return function(){return v.apply(t,arguments)}},m=b,w=m({}.toString),x=m("".slice),S=function(t){return x(w(t),8,-1)},O="object"==typeof document&&document.all,_=void 0===O&&void 0!==O?function(t){return"function"==typeof t||t===O}:function(t){return"function"==typeof t},j=!u((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})),C=Function.prototype.call,A=s?C.bind(C):function(){return C.apply(C,arguments)},M={}.propertyIsEnumerable,P=Object.getOwnPropertyDescriptor,T=P&&!M.call({1:2},1)?function(t){var r=P(this,t);return!!r&&r.enumerable}:M,k={f:T},E=Object,z=m("".split),D=u((function(){return!E("z").propertyIsEnumerable(0)}))?function(t){return"String"===S(t)?z(t,""):E(t)}:E,R=function(t){return null==t},I=TypeError,F=D,L=function(t){if(R(t))throw new I("Can't call method on "+t);return t},N=_,W={},$=c,B=function(t){return N(t)?t:void 0},U=m({}.isPrototypeOf),H="undefined"!=typeof navigator&&String(navigator.userAgent)||"",V=$.process,G=$.Deno,J=V&&V.versions||G&&G.version,q=J&&J.v8;q&&(a=(o=q.split("."))[0]>0&&o[0]<4?1:+(o[0]+o[1])),!a&&H&&(!(o=H.match(/Edge\/(\d+)/))||o[1]>=74)&&(o=H.match(/Chrome\/(\d+)/))&&(a=+o[1]);var K=a,X=$.String,Y=!!Object.getOwnPropertySymbols&&!u((function(){var t=Symbol("symbol detection");return!X(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&K&&K<41})),Z=Y,Q=Z&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,tt=function(t,r){return arguments.length<2?B(W[t])||B($[t]):W[t]&&W[t][r]||$[t]&&$[t][r]},rt=U,et=Q,nt=Object,ot=et?function(t){return"symbol"==typeof t}:function(t){var r=tt("Symbol");return N(r)&&rt(r.prototype,nt(t))},at=String,it=function(t){try{return at(t)}catch(t){return"Object"}},ct=TypeError,ut=function(t){if(N(t))return t;throw new ct(it(t)+" is not a function")},lt=A,st=function(t){return"object"==typeof t?null!==t:N(t)},ft=TypeError,pt=Object.defineProperty,ht=function(t,r){try{pt($,t,{value:r,configurable:!0,writable:!0})}catch(e){$[t]=r}return r},dt=e((function(t){var r="__core-js_shared__",e=t.exports=$[r]||ht(r,{});(e.versions||(e.versions=[])).push({version:"3.37.1",mode:"pure",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.37.1/LICENSE",source:"https://github.com/zloirock/core-js"})})),yt=dt,vt=Object,gt=function(t){return vt(L(t))},bt=m({}.hasOwnProperty),mt=Object.hasOwn||function(t,r){return bt(gt(t),r)},wt=0,xt=Math.random(),St=m(1..toString),Ot=function(t,r){return yt[t]||(yt[t]=r||{})},_t=mt,jt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+St(++wt+xt,36)},Ct=$.Symbol,At=Ot("wks"),Mt=et?Ct.for||Ct:Ct&&Ct.withoutSetter||jt,Pt=ot,Tt=function(t,r){var e=t[r];return R(e)?void 0:ut(e)},kt=function(t,r){var e,n;if("string"===r&&N(e=t.toString)&&!st(n=lt(e,t)))return n;if(N(e=t.valueOf)&&!st(n=lt(e,t)))return n;if("string"!==r&&N(e=t.toString)&&!st(n=lt(e,t)))return n;throw new ft("Can't convert object to primitive value")},Et=function(t){return _t(At,t)||(At[t]=Z&&_t(Ct,t)?Ct[t]:Mt("Symbol."+t)),At[t]},zt=TypeError,Dt=Et("toPrimitive"),Rt=function(t,r){if(!st(t)||Pt(t))return t;var e,n=Tt(t,Dt);if(n){if(void 0===r&&(r="default"),e=lt(n,t,r),!st(e)||Pt(e))return e;throw new zt("Can't convert object to primitive value")}return void 0===r&&(r="number"),kt(t,r)},It=$.document,Ft=st(It)&&st(It.createElement),Lt=j,Nt=function(t){return Ft?It.createElement(t):{}},Wt=!Lt&&!u((function(){return 7!==Object.defineProperty(Nt("div"),"a",{get:function(){return 7}}).a})),$t=k,Bt=function(t,r){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:r}},Ut=function(t){return F(L(t))},Ht=function(t){var r=Rt(t,"string");return Pt(r)?r:r+""},Vt=Wt,Gt=Object.getOwnPropertyDescriptor,Jt={f:Lt?Gt:function(t,r){if(t=Ut(t),r=Ht(r),Vt)try{return Gt(t,r)}catch(t){}if(_t(t,r))return Bt(!lt($t.f,t,r),t[r])}},qt=/#|\.prototype\./,Kt=function(t,r){var e=Yt[Xt(t)];return e===Qt||e!==Zt&&(N(r)?u(r):!!r)},Xt=Kt.normalize=function(t){return String(t).replace(qt,".").toLowerCase()},Yt=Kt.data={},Zt=Kt.NATIVE="N",Qt=Kt.POLYFILL="P",tr=Kt,rr=function(t){if("Function"===S(t))return m(t)},er=rr(rr.bind),nr=Lt&&u((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),or=String,ar=TypeError,ir=nr,cr=function(t){if(st(t))return t;throw new ar(or(t)+" is not an object")},ur=TypeError,lr=Object.defineProperty,sr=Object.getOwnPropertyDescriptor,fr="enumerable",pr="configurable",hr="writable",dr={f:Lt?ir?function(t,r,e){if(cr(t),r=Ht(r),cr(e),"function"==typeof t&&"prototype"===r&&"value"in e&&hr in e&&!e[hr]){var n=sr(t,r);n&&n[hr]&&(t[r]=e.value,e={configurable:pr in e?e[pr]:n[pr],enumerable:fr in e?e[fr]:n[fr],writable:!1})}return lr(t,r,e)}:lr:function(t,r,e){if(cr(t),r=Ht(r),cr(e),Vt)try{return lr(t,r,e)}catch(t){}if("get"in e||"set"in e)throw new ur("Accessors not supported");return"value"in e&&(t[r]=e.value),t}},yr=d,vr=Jt,gr=tr,br=function(t,r){return ut(t),void 0===r?t:s?er(t,r):function(){return t.apply(r,arguments)}},mr=Lt?function(t,r,e){return dr.f(t,r,Bt(1,e))}:function(t,r,e){return t[r]=e,t},wr=vr.f,xr=function(t){var r=function(e,n,o){if(this instanceof r){switch(arguments.length){case 0:return new t;case 1:return new t(e);case 2:return new t(e,n)}return new t(e,n,o)}return yr(t,this,arguments)};return r.prototype=t.prototype,r},Sr=Array.isArray||function(t){return"Array"===S(t)},Or=function(t,r){var e,n,o,a,i,c,u,l,s,f=t.target,p=t.global,h=t.stat,d=t.proto,y=p?$:h?$[f]:$[f]&&$[f].prototype,v=p?W:W[f]||mr(W,f,{})[f],g=v.prototype;for(a in r)n=!(e=gr(p?a:f+(h?".":"#")+a,t.forced))&&y&&_t(y,a),c=v[a],n&&(u=t.dontCallGetSet?(s=wr(y,a))&&s.value:y[a]),i=n&&u?u:r[a],(e||d||typeof c!=typeof i)&&(l=t.bind&&n?br(i,$):t.wrap&&n?xr(i):d&&N(i)?rr(i):i,(t.sham||i&&i.sham||c&&c.sham)&&mr(l,"sham",!0),mr(v,a,l),d&&(_t(W,o=f+"Prototype")||mr(W,o,{}),mr(W[o],a,i),t.real&&g&&(e||!g[a])&&mr(g,a,i)))},_r=Sr;Or({target:"Array",stat:!0},{isArray:_r});var jr=W.Array.isArray,Cr=e((function(t){t.exports=function(t){if(jr(t))return t},t.exports.__esModule=!0,t.exports.default=t.exports}));r(Cr);var Ar=Math.ceil,Mr=Math.floor,Pr=Math.trunc||function(t){var r=+t;return(r>0?Mr:Ar)(r)},Tr=function(t){var r=+t;return r!=r||0===r?0:Pr(r)},kr=Math.min,Er=function(t){var r=Tr(t);return r>0?kr(r,9007199254740991):0},zr=TypeError,Dr={};Dr[Et("toStringTag")]="z";var Rr="[object z]"===String(Dr),Ir=Et("toStringTag"),Fr=Object,Lr="Arguments"===S(function(){return arguments}()),Nr=Rr?S:function(t){var r,e,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(e=function(t,r){try{return t[r]}catch(t){}}(r=Fr(t),Ir))?e:Lr?S(r):"Object"===(n=S(r))&&N(r.callee)?"Arguments":n},Wr=m(Function.toString);N(yt.inspectSource)||(yt.inspectSource=function(t){return Wr(t)});var $r=yt.inspectSource,Br=Nr,Ur=$r,Hr=function(){},Vr=tt("Reflect","construct"),Gr=/^\s*(?:class|function)\b/,Jr=m(Gr.exec),qr=!Gr.test(Hr),Kr=function(t){if(!N(t))return!1;try{return Vr(Hr,[],t),!0}catch(t){return!1}},Xr=function(t){if(!N(t))return!1;switch(Br(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return qr||!!Jr(Gr,Ur(t))}catch(t){return!0}};Xr.sham=!0;var Yr=!Vr||u((function(){var t;return Kr(Kr.call)||!Kr(Object)||!Kr((function(){t=!0}))||t}))?Xr:Kr,Zr=Yr,Qr=Et("species"),te=Array,re=function(t){var r;return _r(t)&&(r=t.constructor,(Zr(r)&&(r===te||_r(r.prototype))||st(r)&&null===(r=r[Qr]))&&(r=void 0)),void 0===r?te:r},ee=Et("species"),ne=function(t){return Er(t.length)},oe=function(t){if(t>9007199254740991)throw zr("Maximum allowed index exceeded");return t},ae=function(t,r,e){Lt?dr.f(t,r,Bt(0,e)):t[r]=e},ie=function(t,r){return new(re(t))(0===r?0:r)},ce=function(t){return K>=51||!u((function(){var r=[];return(r.constructor={})[ee]=function(){return{foo:1}},1!==r[t](Boolean).foo}))},ue=Et("isConcatSpreadable"),le=K>=51||!u((function(){var t=[];return t[ue]=!1,t.concat()[0]!==t})),se=function(t){if(!st(t))return!1;var r=t[ue];return void 0!==r?!!r:_r(t)},fe=!le||!ce("concat");Or({target:"Array",proto:!0,arity:1,forced:fe},{concat:function(t){var r,e,n,o,a,i=gt(this),c=ie(i,0),u=0;for(r=-1,n=arguments.length;r<n;r++)if(se(a=-1===r?i:arguments[r]))for(o=ne(a),oe(u+o),e=0;e<o;e++,u++)e in a&&ae(c,u,a[e]);else oe(u+1),ae(c,u++,a);return c.length=u,c}});var pe,he=Object.freeze({__proto__:null}),de=String,ye=Math.max,ve=Math.min,ge=function(t,r){var e=Tr(t);return e<0?ye(e+r,0):ve(e,r)},be=function(t){return function(r,e,n){var o=Ut(r),a=ne(o);if(0===a)return!t&&-1;var i,c=ge(n,a);if(t&&e!=e){for(;a>c;)if((i=o[c++])!=i)return!0}else for(;a>c;c++)if((t||c in o)&&o[c]===e)return t||c||0;return!t&&-1}},me={includes:be(!0),indexOf:be(!1)},we={},xe=me.indexOf,Se=m([].push),Oe=function(t,r){var e,n=Ut(t),o=0,a=[];for(e in n)!_t(we,e)&&_t(n,e)&&Se(a,e);for(;r.length>o;)_t(n,e=r[o++])&&(~xe(a,e)||Se(a,e));return a},_e=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],je=Object.keys||function(t){return Oe(t,_e)},Ce=Lt&&!ir?Object.defineProperties:function(t,r){cr(t);for(var e,n=Ut(r),o=je(r),a=o.length,i=0;a>i;)dr.f(t,e=o[i++],n[e]);return t},Ae={f:Ce},Me=tt("document","documentElement"),Pe=Ot("keys"),Te=Ae,ke=Me,Ee=function(t){return Pe[t]||(Pe[t]=jt(t))},ze="prototype",De="script",Re=Ee("IE_PROTO"),Ie=function(){},Fe=function(t){return"<"+De+">"+t+"</"+De+">"},Le=function(t){t.write(Fe("")),t.close();var r=t.parentWindow.Object;return t=null,r},Ne=function(){try{pe=new ActiveXObject("htmlfile")}catch(t){}var t,r,e;Ne="undefined"!=typeof document?document.domain&&pe?Le(pe):(r=Nt("iframe"),e="java"+De+":",r.style.display="none",ke.appendChild(r),r.src=String(e),(t=r.contentWindow.document).open(),t.write(Fe("document.F=Object")),t.close(),t.F):Le(pe);for(var n=_e.length;n--;)delete Ne[ze][_e[n]];return Ne()};we[Re]=!0;var We,$e,Be,Ue=Object.create||function(t,r){var e;return null!==t?(Ie[ze]=cr(t),e=new Ie,Ie[ze]=null,e[Re]=t):e=Ne(),void 0===r?e:Te.f(e,r)},He=_e.concat("length","prototype"),Ve={f:Object.getOwnPropertyNames||function(t){return Oe(t,He)}},Ge=m([].slice),Je=Ve,qe=Ge,Ke=Je.f,Xe="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],Ye={f:function(t){return Xe&&"Window"===S(t)?function(t){try{return Ke(t)}catch(t){return qe(Xe)}}(t):Ke(Ut(t))}},Ze={f:Object.getOwnPropertySymbols},Qe={f:Et},tn=dr.f,rn=function(t,r,e,n){return n&&n.enumerable?t[r]=e:mr(t,r,e),t},en=Rr?{}.toString:function(){return"[object "+Br(this)+"]"},nn=dr.f,on=Et("toStringTag"),an=$.WeakMap,cn=N(an)&&/native code/.test(String(an)),un="Object already initialized",ln=$.TypeError,sn=$.WeakMap;if(cn||yt.state){var fn=yt.state||(yt.state=new sn);fn.get=fn.get,fn.has=fn.has,fn.set=fn.set,We=function(t,r){if(fn.has(t))throw new ln(un);return r.facade=t,fn.set(t,r),r},$e=function(t){return fn.get(t)||{}},Be=function(t){return fn.has(t)}}else{var pn=Ee("state");we[pn]=!0,We=function(t,r){if(_t(t,pn))throw new ln(un);return r.facade=t,mr(t,pn,r),r},$e=function(t){return _t(t,pn)?t[pn]:{}},Be=function(t){return _t(t,pn)}}var hn={set:We,get:$e,has:Be,enforce:function(t){return Be(t)?$e(t):We(t,{})},getterFor:function(t){return function(r){var e;if(!st(r)||(e=$e(r)).type!==t)throw new ln("Incompatible receiver, "+t+" required");return e}}},dn=m([].push),yn=function(t){var r=1===t,e=2===t,n=3===t,o=4===t,a=6===t,i=7===t,c=5===t||a;return function(u,l,s,f){for(var p,h,d=gt(u),y=F(d),v=ne(y),g=br(l,s),b=0,m=f||ie,w=r?m(u,v):e||i?m(u,0):void 0;v>b;b++)if((c||b in y)&&(h=g(p=y[b],b,d),t))if(r)w[b]=h;else if(h)switch(t){case 3:return!0;case 5:return p;case 6:return b;case 2:dn(w,p)}else switch(t){case 4:return!1;case 7:dn(w,p)}return a?-1:n||o?o:w}},vn={forEach:yn(0),map:yn(1),filter:yn(2),some:yn(3),every:yn(4),find:yn(5),findIndex:yn(6),filterReject:yn(7)},gn=function(t){if("Symbol"===Br(t))throw new TypeError("Cannot convert a Symbol value to a string");return de(t)},bn=Ue,mn=Ye,wn=Ze,xn=function(t,r,e){return dr.f(t,r,e)},Sn=function(t){var r=W.Symbol||(W.Symbol={});_t(r,t)||tn(r,t,{value:Qe.f(t)})},On=function(){var t=tt("Symbol"),r=t&&t.prototype,e=r&&r.valueOf,n=Et("toPrimitive");r&&!r[n]&&rn(r,n,(function(t){return lt(e,this)}),{arity:1})},_n=function(t,r,e,n){var o=e?t:t&&t.prototype;o&&(_t(o,on)||nn(o,on,{configurable:!0,value:r}),n&&!Rr&&mr(o,"toString",en))},jn=hn,Cn=vn,An=Cn.forEach,Mn=Ee("hidden"),Pn="Symbol",Tn="prototype",kn=jn.set,En=jn.getterFor(Pn),zn=Object[Tn],Dn=$.Symbol,Rn=Dn&&Dn[Tn],In=$.RangeError,Fn=$.TypeError,Ln=$.QObject,Nn=vr.f,Wn=dr.f,$n=mn.f,Bn=$t.f,Un=m([].push),Hn=Ot("symbols"),Vn=Ot("op-symbols"),Gn=Ot("wks"),Jn=!Ln||!Ln[Tn]||!Ln[Tn].findChild,qn=function(t,r,e){var n=Nn(zn,r);n&&delete zn[r],Wn(t,r,e),n&&t!==zn&&Wn(zn,r,n)},Kn=Lt&&u((function(){return 7!==bn(Wn({},"a",{get:function(){return Wn(this,"a",{value:7}).a}})).a}))?qn:Wn,Xn=function(t,r){var e=Hn[t]=bn(Rn);return kn(e,{type:Pn,tag:t,description:r}),Lt||(e.description=r),e},Yn=function(t,r,e){t===zn&&Yn(Vn,r,e),cr(t);var n=Ht(r);return cr(e),_t(Hn,n)?(e.enumerable?(_t(t,Mn)&&t[Mn][n]&&(t[Mn][n]=!1),e=bn(e,{enumerable:Bt(0,!1)})):(_t(t,Mn)||Wn(t,Mn,Bt(1,bn(null))),t[Mn][n]=!0),Kn(t,n,e)):Wn(t,n,e)},Zn=function(t,r){cr(t);var e=Ut(r),n=je(e).concat(eo(e));return An(n,(function(r){Lt&&!lt(Qn,e,r)||Yn(t,r,e[r])})),t},Qn=function(t){var r=Ht(t),e=lt(Bn,this,r);return!(this===zn&&_t(Hn,r)&&!_t(Vn,r))&&(!(e||!_t(this,r)||!_t(Hn,r)||_t(this,Mn)&&this[Mn][r])||e)},to=function(t,r){var e=Ut(t),n=Ht(r);if(e!==zn||!_t(Hn,n)||_t(Vn,n)){var o=Nn(e,n);return!o||!_t(Hn,n)||_t(e,Mn)&&e[Mn][n]||(o.enumerable=!0),o}},ro=function(t){var r=$n(Ut(t)),e=[];return An(r,(function(t){_t(Hn,t)||_t(we,t)||Un(e,t)})),e},eo=function(t){var r=t===zn,e=$n(r?Vn:Ut(t)),n=[];return An(e,(function(t){!_t(Hn,t)||r&&!_t(zn,t)||Un(n,Hn[t])})),n};Z||(Dn=function(){if(rt(Rn,this))throw new Fn("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?gn(arguments[0]):void 0,r=jt(t),e=function(t){var n=void 0===this?$:this;n===zn&&lt(e,Vn,t),_t(n,Mn)&&_t(n[Mn],r)&&(n[Mn][r]=!1);var o=Bt(1,t);try{Kn(n,r,o)}catch(t){if(!(t instanceof In))throw t;qn(n,r,o)}};return Lt&&Jn&&Kn(zn,r,{configurable:!0,set:e}),Xn(r,t)},Rn=Dn[Tn],rn(Rn,"toString",(function(){return En(this).tag})),rn(Dn,"withoutSetter",(function(t){return Xn(jt(t),t)})),$t.f=Qn,dr.f=Yn,Te.f=Zn,vr.f=to,Je.f=mn.f=ro,wn.f=eo,Qe.f=function(t){return Xn(Et(t),t)},Lt&&xn(Rn,"description",{configurable:!0,get:function(){return En(this).description}})),Or({global:!0,constructor:!0,wrap:!0,forced:!Z,sham:!Z},{Symbol:Dn}),An(je(Gn),(function(t){Sn(t)})),Or({target:Pn,stat:!0,forced:!Z},{useSetter:function(){Jn=!0},useSimple:function(){Jn=!1}}),Or({target:"Object",stat:!0,forced:!Z,sham:!Lt},{create:function(t,r){return void 0===r?bn(t):Zn(bn(t),r)},defineProperty:Yn,defineProperties:Zn,getOwnPropertyDescriptor:to}),Or({target:"Object",stat:!0,forced:!Z},{getOwnPropertyNames:ro}),On(),_n(Dn,Pn),we[Mn]=!0;var no=Z&&!!Symbol.for&&!!Symbol.keyFor,oo=Ot("string-to-symbol-registry"),ao=Ot("symbol-to-string-registry");Or({target:"Symbol",stat:!0,forced:!no},{for:function(t){var r=gn(t);if(_t(oo,r))return oo[r];var e=tt("Symbol")(r);return oo[r]=e,ao[e]=r,e}});var io=Ot("symbol-to-string-registry");Or({target:"Symbol",stat:!0,forced:!no},{keyFor:function(t){if(!Pt(t))throw new TypeError(it(t)+" is not a symbol");if(_t(io,t))return io[t]}});var co=m([].push),uo=function(t){if(N(t))return t;if(_r(t)){for(var r=t.length,e=[],n=0;n<r;n++){var o=t[n];"string"==typeof o?co(e,o):"number"!=typeof o&&"Number"!==S(o)&&"String"!==S(o)||co(e,gn(o))}var a=e.length,i=!0;return function(t,r){if(i)return i=!1,r;if(_r(this))return r;for(var n=0;n<a;n++)if(e[n]===t)return r}}},lo=String,so=tt("JSON","stringify"),fo=m(/./.exec),po=m("".charAt),ho=m("".charCodeAt),yo=m("".replace),vo=m(1..toString),go=/[\uD800-\uDFFF]/g,bo=/^[\uD800-\uDBFF]$/,mo=/^[\uDC00-\uDFFF]$/,wo=!Z||u((function(){var t=tt("Symbol")("stringify detection");return"[null]"!==so([t])||"{}"!==so({a:t})||"{}"!==so(Object(t))})),xo=u((function(){return'"\\udf06\\ud834"'!==so("\udf06\ud834")||'"\\udead"'!==so("\udead")})),So=function(t,r){var e=qe(arguments),n=uo(r);if(N(n)||void 0!==t&&!Pt(t))return e[1]=function(t,r){if(N(n)&&(r=lt(n,this,lo(t),r)),!Pt(r))return r},yr(so,null,e)},Oo=function(t,r,e){var n=po(e,r-1),o=po(e,r+1);return fo(bo,t)&&!fo(mo,o)||fo(mo,t)&&!fo(bo,n)?"\\u"+vo(ho(t,0),16):t};so&&Or({target:"JSON",stat:!0,arity:3,forced:wo||xo},{stringify:function(t,r,e){var n=qe(arguments),o=yr(wo?So:so,null,n);return xo&&"string"==typeof o?yo(o,go,Oo):o}});var _o=!Z||u((function(){wn.f(1)}));Or({target:"Object",stat:!0,forced:_o},{getOwnPropertySymbols:function(t){var r=wn.f;return r?r(gt(t)):[]}}),Sn("asyncIterator");var jo=Object.freeze({__proto__:null});Sn("hasInstance"),Sn("isConcatSpreadable"),Sn("iterator"),Sn("match"),Sn("matchAll"),Sn("replace"),Sn("search"),Sn("species"),Sn("split"),Sn("toPrimitive"),On(),Sn("toStringTag"),_n(tt("Symbol"),"Symbol"),Sn("unscopables"),_n($.JSON,"JSON",!0);var Co=Object.freeze({__proto__:null}),Ao=Object.freeze({__proto__:null});n(he),n(jo),n(Co),n(Ao);var Mo,Po,To,ko=W.Symbol,Eo=Function.prototype,zo=Lt&&Object.getOwnPropertyDescriptor,Do=_t(Eo,"name"),Ro={EXISTS:Do,PROPER:Do&&"something"===function(){}.name,CONFIGURABLE:Do&&(!Lt||Lt&&zo(Eo,"name").configurable)},Io=!u((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})),Fo=Ee("IE_PROTO"),Lo=Object,No=Lo.prototype,Wo=Io?Lo.getPrototypeOf:function(t){var r=gt(t);if(_t(r,Fo))return r[Fo];var e=r.constructor;return N(e)&&r instanceof e?e.prototype:r instanceof Lo?No:null},$o=Et("iterator"),Bo=!1;[].keys&&("next"in(To=[].keys())?(Po=Wo(Wo(To)))!==Object.prototype&&(Mo=Po):Bo=!0);var Uo=!st(Mo)||u((function(){var t={};return Mo[$o].call(t)!==t}));Mo=Uo?{}:bn(Mo),N(Mo[$o])||rn(Mo,$o,(function(){return this}));var Ho={IteratorPrototype:Mo,BUGGY_SAFARI_ITERATORS:Bo},Vo={},Go=Ho.IteratorPrototype,Jo=function(){return this},qo=function(t){return st(t)||null===t},Ko=String,Xo=TypeError,Yo=function(t,r,e){try{return m(ut(Object.getOwnPropertyDescriptor(t,r)[e]))}catch(t){}},Zo=function(t){if(qo(t))return t;throw new Xo("Can't set "+Ko(t)+" as a prototype")},Qo=(Object.setPrototypeOf||"__proto__"in{}&&function(){var t,r=!1,e={};try{(t=Yo(Object.prototype,"__proto__","set"))(e,[]),r=e instanceof Array}catch(t){}}(),Ro),ta=function(t,r,e,n){var o=r+" Iterator";return t.prototype=bn(Go,{next:Bt(+!n,e)}),_n(t,o,!1,!0),Vo[o]=Jo,t},ra=Qo.PROPER,ea=Ho.BUGGY_SAFARI_ITERATORS,na=Et("iterator"),oa="keys",aa="values",ia="entries",ca=function(){return this},ua=function(){},la=function(t,r,e,n,o,a,i){ta(e,r,n);var c,u,l,s=function(t){if(t===o&&y)return y;if(!ea&&t&&t in h)return h[t];switch(t){case oa:case aa:case ia:return function(){return new e(this,t)}}return function(){return new e(this)}},f=r+" Iterator",p=!1,h=t.prototype,d=h[na]||h["@@iterator"]||o&&h[o],y=!ea&&d||s(o),v="Array"===r&&h.entries||d;if(v&&(c=Wo(v.call(new t)))!==Object.prototype&&c.next&&(_n(c,f,!0,!0),Vo[f]=ca),ra&&o===aa&&d&&d.name!==aa&&(p=!0,y=function(){return lt(d,this)}),o)if(u={values:s(aa),keys:a?y:s(oa),entries:s(ia)},i)for(l in u)(ea||p||!(l in h))&&rn(h,l,u[l]);else Or({target:r,proto:!0,forced:ea||p},u);return i&&h[na]!==y&&rn(h,na,y,{name:o}),Vo[r]=y,u},sa=function(t,r){return{value:t,done:r}},fa=(dr.f,"Array Iterator"),pa=jn.set,ha=jn.getterFor(fa);la(Array,"Array",(function(t,r){pa(this,{type:fa,target:Ut(t),index:0,kind:r})}),(function(){var t=ha(this),r=t.target,e=t.index++;if(!r||e>=r.length)return t.target=void 0,sa(void 0,!0);switch(t.kind){case"keys":return sa(e,!1);case"values":return sa(r[e],!1)}return sa([e,r[e]],!1)}),"values");Vo.Arguments=Vo.Array;ua(),ua(),ua();var da={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0};for(var ya in da)_n($[ya],ya),Vo[ya]=Vo.Array;var va=ko,ga=dr.f,ba=Et("metadata"),ma=Function.prototype;void 0===ma[ba]&&ga(ma,ba,{value:null}),Sn("asyncDispose"),Sn("dispose"),Sn("metadata");var wa=va,xa=tt("Symbol"),Sa=xa.keyFor,Oa=m(xa.prototype.valueOf),_a=xa.isRegisteredSymbol||function(t){try{return void 0!==Sa(Oa(t))}catch(t){return!1}};Or({target:"Symbol",stat:!0},{isRegisteredSymbol:_a});for(var ja=tt("Symbol"),Ca=ja.isWellKnownSymbol,Aa=tt("Object","getOwnPropertyNames"),Ma=m(ja.prototype.valueOf),Pa=Ot("wks"),Ta=0,ka=Aa(ja),Ea=ka.length;Ta<Ea;Ta++)try{var za=ka[Ta];Pt(ja[za])&&Et(za)}catch(t){}var Da=function(t){if(Ca&&Ca(t))return!0;try{for(var r=Ma(t),e=0,n=Aa(Pa),o=n.length;e<o;e++)if(Pa[n[e]]==r)return!0}catch(t){}return!1};Or({target:"Symbol",stat:!0,forced:!0},{isWellKnownSymbol:Da}),Sn("customMatcher"),Sn("observable"),Or({target:"Symbol",stat:!0,name:"isRegisteredSymbol"},{isRegistered:_a}),Or({target:"Symbol",stat:!0,name:"isWellKnownSymbol",forced:!0},{isWellKnown:Da}),Sn("matcher"),Sn("metadataKey"),Sn("patternMatch"),Sn("replaceAll");var Ra=wa,Ia=m("".charAt),Fa=m("".charCodeAt),La=m("".slice),Na=function(t){return function(r,e){var n,o,a=gn(L(r)),i=Tr(e),c=a.length;return i<0||i>=c?t?"":void 0:(n=Fa(a,i))<55296||n>56319||i+1===c||(o=Fa(a,i+1))<56320||o>57343?t?Ia(a,i):n:t?La(a,i,i+2):o-56320+(n-55296<<10)+65536}},Wa={codeAt:Na(!1),charAt:Na(!0)}.charAt,$a="String Iterator",Ba=jn.set,Ua=jn.getterFor($a);la(String,"String",(function(t){Ba(this,{type:$a,string:gn(t),index:0})}),(function(){var t,r=Ua(this),e=r.string,n=r.index;return n>=e.length?sa(void 0,!0):(t=Wa(e,n),r.index+=t.length,sa(t,!1))}));var Ha=Et("iterator"),Va=function(t){if(!R(t))return Tt(t,Ha)||Tt(t,"@@iterator")||Vo[Br(t)]},Ga=Va,Ja=TypeError,qa=Object.getOwnPropertyDescriptor,Ka=Lt&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}()?function(t,r){if(_r(t)&&!qa(t,"length").writable)throw new Ja("Cannot set read only .length");return t.length=r}:function(t,r){return t.length=r},Xa=u((function(){return 4294967297!==[].push.call({length:4294967296},1)}))||!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(t){return t instanceof TypeError}}();Or({target:"Array",proto:!0,arity:1,forced:Xa},{push:function(t){var r=gt(this),e=ne(r),n=arguments.length;oe(e+n);for(var o=0;o<n;o++)r[e]=arguments[o],e++;return Ka(r,e),e}});var Ya=function(t,r){var e=W[t+"Prototype"],n=e&&e[r];if(n)return n;var o=$[t],a=o&&o.prototype;return a&&a[r]},Za=Ya("Array","push"),Qa=Array.prototype,ti=Ra,ri=Ga,ei=function(t){var r=t.push;return t===Qa||rt(Qa,t)&&r===Qa.push?Za:r},ni=e((function(t){t.exports=function(t,r){var e=null==t?null:void 0!==ti&&ri(t)||t["@@iterator"];if(null!=e){var n,o,a,i,c=[],u=!0,l=!1;try{if(a=(e=e.call(t)).next,0===r){if(Object(e)!==e)return;u=!1}else for(;!(u=(n=a.call(e)).done)&&(ei(c).call(c,n.value),c.length!==r);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=e.return&&(i=e.return(),Object(i)!==i))return}finally{if(l)throw o}}return c}},t.exports.__esModule=!0,t.exports.default=t.exports}));r(ni);var oi=ce("slice"),ai=Et("species"),ii=Array,ci=Math.max;Or({target:"Array",proto:!0,forced:!oi},{slice:function(t,r){var e,n,o,a=Ut(this),i=ne(a),c=ge(t,i),u=ge(void 0===r?i:r,i);if(_r(a)&&(e=a.constructor,(Zr(e)&&(e===ii||_r(e.prototype))||st(e)&&null===(e=e[ai]))&&(e=void 0),e===ii||void 0===e))return qe(a,c,u);for(n=new(void 0===e?ii:e)(ci(u-c,0)),o=0;c<u;c++,o++)c in a&&ae(n,o,a[c]);return n.length=o,n}});var ui=Ya("Array","slice"),li=Array.prototype,si=function(t){var r=t.slice;return t===li||rt(li,t)&&r===li.slice?ui:r},fi=si,pi=function(t,r,e){var n,o;cr(t);try{if(!(n=Tt(t,"return"))){if("throw"===r)throw e;return e}n=lt(n,t)}catch(t){o=!0,n=t}if("throw"===r)throw e;if(o)throw n;return cr(n),e},hi=Et("iterator"),di=Array.prototype,yi=TypeError,vi=function(t,r,e,n){try{return n?r(cr(e)[0],e[1]):r(e)}catch(r){pi(t,"throw",r)}},gi=function(t){return void 0!==t&&(Vo.Array===t||di[hi]===t)},bi=function(t,r){var e=arguments.length<2?Va(t):r;if(ut(e))return cr(lt(e,t));throw new yi(it(t)+" is not iterable")},mi=Array,wi=Et("iterator"),xi=!1;try{var Si=0,Oi={next:function(){return{done:!!Si++}},return:function(){xi=!0}};Oi[wi]=function(){return this},Array.from(Oi,(function(){throw 2}))}catch(t){}var _i=function(t){var r=gt(t),e=Zr(this),n=arguments.length,o=n>1?arguments[1]:void 0,a=void 0!==o;a&&(o=br(o,n>2?arguments[2]:void 0));var i,c,u,l,s,f,p=Va(r),h=0;if(!p||this===mi&&gi(p))for(i=ne(r),c=e?new this(i):mi(i);i>h;h++)f=a?o(r[h],h):r[h],ae(c,h,f);else for(c=e?new this:[],s=(l=bi(r,p)).next;!(u=lt(s,l)).done;h++)f=a?vi(l,o,[u.value,h],!0):u.value,ae(c,h,f);return c.length=h,c},ji=function(t,r){try{if(!r&&!xi)return!1}catch(t){return!1}var e=!1;try{var n={};n[wi]=function(){return{next:function(){return{done:e=!0}}}},t(n)}catch(t){}return e},Ci=!ji((function(t){Array.from(t)}));Or({target:"Array",stat:!0,forced:Ci},{from:_i});var Ai=W.Array.from,Mi=e((function(t){t.exports=function(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n},t.exports.__esModule=!0,t.exports.default=t.exports}));r(Mi);var Pi=fi,Ti=Ai,ki=e((function(t){t.exports=function(t,r){if(t){var e;if("string"==typeof t)return Mi(t,r);var n=Pi(e={}.toString.call(t)).call(e,8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Ti(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Mi(t,r):void 0}},t.exports.__esModule=!0,t.exports.default=t.exports}));r(ki);var Ei=e((function(t){t.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},t.exports.__esModule=!0,t.exports.default=t.exports}));r(Ei);var zi=r(e((function(t){t.exports=function(t,r){return Cr(t)||ni(t,r)||ki(t,r)||Ei()},t.exports.__esModule=!0,t.exports.default=t.exports}))),Di=e((function(t){t.exports=function(t){if(jr(t))return Mi(t)},t.exports.__esModule=!0,t.exports.default=t.exports}));r(Di);var Ri=e((function(t){t.exports=function(t){if(void 0!==ti&&null!=ri(t)||null!=t["@@iterator"])return Ti(t)},t.exports.__esModule=!0,t.exports.default=t.exports}));r(Ri);var Ii=e((function(t){t.exports=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},t.exports.__esModule=!0,t.exports.default=t.exports}));r(Ii);var Fi=r(e((function(t){t.exports=function(t){return Di(t)||Ri(t)||ki(t)||Ii()},t.exports.__esModule=!0,t.exports.default=t.exports}))),Li=dr.f;Or({target:"Object",stat:!0,forced:Object.defineProperty!==Li,sham:!Lt},{defineProperty:Li});var Ni=e((function(t){var r=W.Object,e=t.exports=function(t,e,n){return r.defineProperty(t,e,n)};r.defineProperty.sham&&(e.sham=!0)})),Wi=Ni,$i=Wi,Bi=Qe.f("iterator"),Ui=e((function(t){function r(e){return t.exports=r="function"==typeof ti&&"symbol"==typeof Bi?function(t){return typeof t}:function(t){return t&&"function"==typeof ti&&t.constructor===ti&&t!==ti.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports.default=t.exports,r(e)}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports}));r(Ui),n(Object.freeze({__proto__:null}));var Hi=Qe.f("toPrimitive"),Vi=e((function(t){var r=Ui.default;t.exports=function(t,e){if("object"!=r(t)||!t)return t;var n=t[Hi];if(void 0!==n){var o=n.call(t,e||"default");if("object"!=r(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)},t.exports.__esModule=!0,t.exports.default=t.exports}));r(Vi);var Gi=e((function(t){var r=Ui.default;t.exports=function(t){var e=Vi(t,"string");return"symbol"==r(e)?e:e+""},t.exports.__esModule=!0,t.exports.default=t.exports}));r(Gi);var Ji=$i,qi=r(e((function(t){t.exports=function(t,r,e){return(r=Gi(r))in t?Ji(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[r]=e,t},t.exports.__esModule=!0,t.exports.default=t.exports}))),Ki=W.Object.getOwnPropertySymbols,Xi=Ki,Yi=function(t,r){var e=[][t];return!!e&&u((function(){e.call(null,r||function(){return 1},1)}))},Zi=me.indexOf,Qi=rr([].indexOf),tc=!!Qi&&1/Qi([1],1,-0)<0,rc=tc||!Yi("indexOf");Or({target:"Array",proto:!0,forced:rc},{indexOf:function(t){var r=arguments.length>1?arguments[1]:void 0;return tc?Qi(this,t,r)||0:Zi(this,t,r)}});var ec=Ya("Array","indexOf"),nc=Array.prototype,oc=function(t){var r=t.indexOf;return t===nc||rt(nc,t)&&r===nc.indexOf?ec:r},ac=oc,ic=e((function(t){t.exports=function(t,r){if(null==t)return{};var e={};for(var n in t)if({}.hasOwnProperty.call(t,n)){if(ac(r).call(r,n)>=0)continue;e[n]=t[n]}return e},t.exports.__esModule=!0,t.exports.default=t.exports}));r(ic);var cc=Xi,uc=e((function(t){t.exports=function(t,r){if(null==t)return{};var e,n,o=ic(t,r);if(cc){var a=cc(t);for(n=0;n<a.length;n++)e=a[n],ac(r).call(r,e)>=0||{}.propertyIsEnumerable.call(t,e)&&(o[e]=t[e])}return o},t.exports.__esModule=!0,t.exports.default=t.exports})),lc=r(uc),sc=r(e((function(t){t.exports=function(t,r){if(!(t instanceof r))throw new TypeError("Cannot call a class as a function")},t.exports.__esModule=!0,t.exports.default=t.exports}))),fc=r(e((function(t){function r(t,r){for(var e=0;e<r.length;e++){var n=r[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Ji(t,Gi(n.key),n)}}t.exports=function(t,e,n){return e&&r(t.prototype,e),n&&r(t,n),Ji(t,"prototype",{writable:!1}),t},t.exports.__esModule=!0,t.exports.default=t.exports}))),pc=Ya("Array","concat"),hc=Array.prototype,dc=function(t){var r=t.concat;return t===hc||rt(hc,t)&&r===hc.concat?pc:r},yc=Date,vc=m(yc.prototype.getTime);Or({target:"Date",stat:!0},{now:function(){return vc(new yc)}});var gc=W.Date.now,bc=RangeError,mc=m((function(t){var r=gn(L(this)),e="",n=Tr(t);if(n<0||n===1/0)throw new bc("Wrong number of repetitions");for(;n>0;(n>>>=1)&&(r+=r))1&n&&(e+=r);return e})),wc=m("".slice),xc=Math.ceil,Sc=function(t){return function(r,e,n){var o,a,i=gn(L(r)),c=Er(e),u=i.length,l=void 0===n?" ":gn(n);return c<=u||""===l?i:((a=mc(l,xc((o=c-u)/l.length))).length>o&&(a=wc(a,0,o)),t?i+a:a+i)}},Oc={start:Sc(!1),end:Sc(!0)}.start,_c=RangeError,jc=isFinite,Cc=Math.abs,Ac=Date.prototype,Mc=Ac.toISOString,Pc=m(Ac.getTime),Tc=m(Ac.getUTCDate),kc=m(Ac.getUTCFullYear),Ec=m(Ac.getUTCHours),zc=m(Ac.getUTCMilliseconds),Dc=m(Ac.getUTCMinutes),Rc=m(Ac.getUTCMonth),Ic=m(Ac.getUTCSeconds),Fc=u((function(){return"0385-07-25T07:06:39.999Z"!==Mc.call(new Date(-50000000000001))}))||!u((function(){Mc.call(new Date(NaN))}))?function(){if(!jc(Pc(this)))throw new _c("Invalid time value");var t=this,r=kc(t),e=zc(t),n=r<0?"-":r>9999?"+":"";return n+Oc(Cc(r),n?6:4,0)+"-"+Oc(Rc(t)+1,2,0)+"-"+Oc(Tc(t),2,0)+"T"+Oc(Ec(t),2,0)+":"+Oc(Dc(t),2,0)+":"+Oc(Ic(t),2,0)+"."+Oc(e,3,0)+"Z"}:Mc,Lc=u((function(){return null!==new Date(NaN).toJSON()||1!==lt(Date.prototype.toJSON,{toISOString:function(){return 1}})}));Or({target:"Date",proto:!0,forced:Lc},{toJSON:function(t){var r=gt(this),e=Rt(r,"number");return"number"!=typeof e||isFinite(e)?"toISOString"in r||"Date"!==S(r)?r.toISOString():lt(Fc,r):null}}),W.JSON||(W.JSON={stringify:JSON.stringify});var Nc=function(t,r,e){return yr(W.JSON.stringify,null,arguments)},Wc=Nc,$c="function"==typeof Bun&&Bun&&"string"==typeof Bun.version,Bc=TypeError,Uc=$c,Hc=function(t,r){if(t<r)throw new Bc("Not enough arguments");return t},Vc=$.Function,Gc=/MSIE .\./.test(H)||Uc&&function(){var t=$.Bun.version.split(".");return t.length<3||"0"===t[0]&&(t[1]<3||"3"===t[1]&&"0"===t[2])}(),Jc=function(t,r){var e=r?2:1;return Gc?function(n,o){var a=Hc(arguments.length,1)>e,i=N(n)?n:Vc(n),c=a?qe(arguments,e):[],u=a?function(){yr(i,this,c)}:i;return r?t(u,o):t(u)}:t},qc=Jc($.setInterval,!0);Or({global:!0,bind:!0,forced:$.setInterval!==qc},{setInterval:qc});var Kc=Jc($.setTimeout,!0);Or({global:!0,bind:!0,forced:$.setTimeout!==Kc},{setTimeout:Kc});var Xc=W.setTimeout,Yc=Cn.map,Zc=ce("map");Or({target:"Array",proto:!0,forced:!Zc},{map:function(t){return Yc(this,t,arguments.length>1?arguments[1]:void 0)}});var Qc=Ya("Array","map"),tu=Array.prototype,ru=function(t){var r=t.map;return t===tu||rt(tu,t)&&r===tu.map?Qc:r},eu=si,nu=oc,ou="\t\n\v\f\r                　\u2028\u2029\ufeff",au=m("".replace),iu=RegExp("^["+ou+"]+"),cu=RegExp("(^|[^"+ou+"])["+ou+"]+$"),uu=function(t){return function(r){var e=gn(L(r));return 1&t&&(e=au(e,iu,"")),2&t&&(e=au(e,cu,"$1")),e}},lu={start:uu(1),end:uu(2),trim:uu(3)},su=lu.trim,fu=m("".charAt),pu=$.parseFloat,hu=$.Symbol,du=hu&&hu.iterator,yu=1/pu(ou+"-0")!=-1/0||du&&!u((function(){pu(Object(du))}))?function(t){var r=su(gn(t)),e=pu(r);return 0===e&&"-"===fu(r,0)?-0:e}:pu;Or({global:!0,forced:parseFloat!==yu},{parseFloat:yu});var vu=W.parseFloat,gu=Cn.forEach,bu=Yi("forEach")?[].forEach:function(t){return gu(this,t,arguments.length>1?arguments[1]:void 0)};Or({target:"Array",proto:!0,forced:[].forEach!==bu},{forEach:bu});var mu=Ya("Array","forEach");n(Object.freeze({__proto__:null}));var wu=Array.prototype,xu={DOMTokenList:!0,NodeList:!0},Su=function(t){var r=t.forEach;return t===wu||rt(wu,t)&&r===wu.forEach||_t(xu,Br(t))?mu:r},Ou=m([].reverse),_u=[1,2];Or({target:"Array",proto:!0,forced:String(_u)===String(_u.reverse())},{reverse:function(){return _r(this)&&(this.length=this.length),Ou(this)}});var ju=Ya("Array","reverse"),Cu=Array.prototype,Au=function(t){var r=t.reverse;return t===Cu||rt(Cu,t)&&r===Cu.reverse?ju:r},Mu=Qo.PROPER,Pu=lu.trim;Or({target:"String",proto:!0,forced:function(t){return u((function(){return!!ou[t]()||"​᠎"!=="​᠎"[t]()||Mu&&ou[t].name!==t}))}("trim")},{trim:function(){return Pu(this)}});var Tu=Ya("String","trim"),ku=String.prototype,Eu=function(t){var r=t.trim;return"string"==typeof t||t===ku||rt(ku,t)&&r===ku.trim?Tu:r},zu=Et("match"),Du=function(t){var r;return st(t)&&(void 0!==(r=t[zu])?!!r:"RegExp"===S(t))},Ru=TypeError,Iu=Et("match"),Fu=function(t){if(Du(t))throw new Ru("The method doesn't accept regular expressions");return t},Lu=function(t){var r=/./;try{"/./"[t](r)}catch(e){try{return r[Iu]=!1,"/./"[t](r)}catch(t){}}return!1},Nu=(vr.f,rr("".slice)),Wu=Math.min,$u=Lu("endsWith");Or({target:"String",proto:!0,forced:!$u},{endsWith:function(t){var r=gn(L(this));Fu(t);var e=arguments.length>1?arguments[1]:void 0,n=r.length,o=void 0===e?n:Wu(Er(e),n),a=gn(t);return Nu(r,o-a.length,o)===a}});var Bu=Ya("String","endsWith"),Uu=String.prototype,Hu=function(t){var r=t.endsWith;return"string"==typeof t||t===Uu||rt(Uu,t)&&r===Uu.endsWith?Bu:r},Vu=$t.f,Gu=m(Vu),Ju=m([].push),qu=Lt&&u((function(){var t=Object.create(null);return t[2]=2,!Gu(t,2)})),Ku=function(t){return function(r){for(var e,n=Ut(r),o=je(n),a=qu&&null===Wo(n),i=o.length,c=0,u=[];i>c;)e=o[c++],Lt&&!(a?e in n:Gu(n,e))||Ju(u,t?[e,n[e]]:n[e]);return u}},Xu={entries:Ku(!0),values:Ku(!1)}.entries;Or({target:"Object",stat:!0},{entries:function(t){return Xu(t)}});var Yu=W.Object.entries,Zu=me.includes,Qu=u((function(){return!Array(1).includes()}));Or({target:"Array",proto:!0,forced:Qu},{includes:function(t){return Zu(this,t,arguments.length>1?arguments[1]:void 0)}}),ua();var tl=Ya("Array","includes"),rl=m("".indexOf);Or({target:"String",proto:!0,forced:!Lu("includes")},{includes:function(t){return!!~rl(gn(L(this)),gn(Fu(t)),arguments.length>1?arguments[1]:void 0)}});var el=Ya("String","includes"),nl=tl,ol=el,al=Array.prototype,il=String.prototype,cl=function(t){var r=t.includes;return t===al||rt(al,t)&&r===al.includes?nl:"string"==typeof t||t===il||rt(il,t)&&r===il.includes?ol:r},ul=TypeError,ll="Reduce of empty array with no initial value",sl=function(t){return function(r,e,n,o){var a=gt(r),i=F(a),c=ne(a);if(ut(e),0===c&&n<2)throw new ul(ll);var u=t?c-1:0,l=t?-1:1;if(n<2)for(;;){if(u in i){o=i[u],u+=l;break}if(u+=l,t?u<0:c<=u)throw new ul(ll)}for(;t?u>=0:c>u;u+=l)u in i&&(o=e(o,i[u],u,a));return o}},fl={left:sl(!1),right:sl(!0)},pl="process"===S($.process),hl=fl.left,dl=!pl&&K>79&&K<83||!Yi("reduce");Or({target:"Array",proto:!0,forced:dl},{reduce:function(t){var r=arguments.length;return hl(this,t,r,r>1?arguments[1]:void 0)}});var yl=Ya("Array","reduce"),vl=Array.prototype,gl=function(t){var r=t.reduce;return t===vl||rt(vl,t)&&r===vl.reduce?yl:r},bl=Cn.some,ml=Yi("some");Or({target:"Array",proto:!0,forced:!ml},{some:function(t){return bl(this,t,arguments.length>1?arguments[1]:void 0)}});var wl=Ya("Array","some"),xl=Array.prototype,Sl=function(t){var r=t.some;return t===xl||rt(xl,t)&&r===xl.some?wl:r},Ol=u((function(){je(1)}));Or({target:"Object",stat:!0,forced:Ol},{keys:function(t){return je(gt(t))}});var _l=W.Object.keys,jl=Ki,Cl=Cn.filter,Al=ce("filter");Or({target:"Array",proto:!0,forced:!Al},{filter:function(t){return Cl(this,t,arguments.length>1?arguments[1]:void 0)}});var Ml=Ya("Array","filter"),Pl=Array.prototype,Tl=function(t){var r=t.filter;return t===Pl||rt(Pl,t)&&r===Pl.filter?Ml:r},kl=vr.f,El=!Lt||u((function(){kl(1)}));Or({target:"Object",stat:!0,forced:El,sham:!Lt},{getOwnPropertyDescriptor:function(t,r){return kl(Ut(t),r)}});var zl=e((function(t){var r=W.Object,e=t.exports=function(t,e){return r.getOwnPropertyDescriptor(t,e)};r.getOwnPropertyDescriptor.sham&&(e.sham=!0)})),Dl=zl,Rl=m([].concat),Il=tt("Reflect","ownKeys")||function(t){var r=Je.f(cr(t)),e=wn.f;return e?Rl(r,e(t)):r},Fl=Il;Or({target:"Object",stat:!0,sham:!Lt},{getOwnPropertyDescriptors:function(t){for(var r,e,n=Ut(t),o=vr.f,a=Fl(n),i={},c=0;a.length>c;)void 0!==(e=o(n,r=a[c++]))&&ae(i,r,e);return i}});var Ll=W.Object.getOwnPropertyDescriptors,Nl=Te.f;Or({target:"Object",stat:!0,forced:Object.defineProperties!==Nl,sham:!Lt},{defineProperties:Nl});var Wl=e((function(t){var r=W.Object,e=t.exports=function(t,e){return r.defineProperties(t,e)};r.defineProperties.sham&&(e.sham=!0)})),$l=Wl,Bl=Wi;var Ul=function(){this.__data__=[],this.size=0};var Hl=function(t,r){return t===r||t!=t&&r!=r};var Vl=function(t,r){for(var e=t.length;e--;)if(Hl(t[e][0],r))return e;return-1},Gl=Array.prototype.splice;var Jl=function(t){var r=this.__data__,e=Vl(r,t);return!(e<0)&&(e==r.length-1?r.pop():Gl.call(r,e,1),--this.size,!0)};var ql=function(t){var r=this.__data__,e=Vl(r,t);return e<0?void 0:r[e][1]};var Kl=function(t){return Vl(this.__data__,t)>-1};var Xl=function(t,r){var e=this.__data__,n=Vl(e,t);return n<0?(++this.size,e.push([t,r])):e[n][1]=r,this};function Yl(t){var r=-1,e=null==t?0:t.length;for(this.clear();++r<e;){var n=t[r];this.set(n[0],n[1])}}Yl.prototype.clear=Ul,Yl.prototype.delete=Jl,Yl.prototype.get=ql,Yl.prototype.has=Kl,Yl.prototype.set=Xl;var Zl=Yl;var Ql=function(){this.__data__=new Zl,this.size=0};var ts=function(t){var r=this.__data__,e=r.delete(t);return this.size=r.size,e};var rs=function(t){return this.__data__.get(t)};var es=function(t){return this.__data__.has(t)},ns="object"==typeof t&&t&&t.Object===Object&&t,os="object"==typeof self&&self&&self.Object===Object&&self,as=ns||os||Function("return this")(),is=as.Symbol,cs=Object.prototype,us=cs.hasOwnProperty,ls=cs.toString,ss=is?is.toStringTag:void 0;var fs=function(t){var r=us.call(t,ss),e=t[ss];try{t[ss]=void 0;var n=!0}catch(t){}var o=ls.call(t);return n&&(r?t[ss]=e:delete t[ss]),o},ps=Object.prototype.toString;var hs=function(t){return ps.call(t)},ds=is?is.toStringTag:void 0;var ys=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":ds&&ds in Object(t)?fs(t):hs(t)};var vs=function(t){var r=typeof t;return null!=t&&("object"==r||"function"==r)};var gs=function(t){if(!vs(t))return!1;var r=ys(t);return"[object Function]"==r||"[object GeneratorFunction]"==r||"[object AsyncFunction]"==r||"[object Proxy]"==r},bs=as["__core-js_shared__"],ms=function(){var t=/[^.]+$/.exec(bs&&bs.keys&&bs.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();var ws=function(t){return!!ms&&ms in t},xs=Function.prototype.toString;var Ss=function(t){if(null!=t){try{return xs.call(t)}catch(t){}try{return t+""}catch(t){}}return""},Os=/^\[object .+?Constructor\]$/,_s=Function.prototype,js=Object.prototype,Cs=_s.toString,As=js.hasOwnProperty,Ms=RegExp("^"+Cs.call(As).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");var Ps=function(t){return!(!vs(t)||ws(t))&&(gs(t)?Ms:Os).test(Ss(t))};var Ts=function(t,r){return null==t?void 0:t[r]};var ks=function(t,r){var e=Ts(t,r);return Ps(e)?e:void 0},Es=ks(as,"Map"),zs=ks(Object,"create");var Ds=function(){this.__data__=zs?zs(null):{},this.size=0};var Rs=function(t){var r=this.has(t)&&delete this.__data__[t];return this.size-=r?1:0,r},Is=Object.prototype.hasOwnProperty;var Fs=function(t){var r=this.__data__;if(zs){var e=r[t];return"__lodash_hash_undefined__"===e?void 0:e}return Is.call(r,t)?r[t]:void 0},Ls=Object.prototype.hasOwnProperty;var Ns=function(t){var r=this.__data__;return zs?void 0!==r[t]:Ls.call(r,t)};var Ws=function(t,r){var e=this.__data__;return this.size+=this.has(t)?0:1,e[t]=zs&&void 0===r?"__lodash_hash_undefined__":r,this};function $s(t){var r=-1,e=null==t?0:t.length;for(this.clear();++r<e;){var n=t[r];this.set(n[0],n[1])}}$s.prototype.clear=Ds,$s.prototype.delete=Rs,$s.prototype.get=Fs,$s.prototype.has=Ns,$s.prototype.set=Ws;var Bs=$s;var Us=function(){this.size=0,this.__data__={hash:new Bs,map:new(Es||Zl),string:new Bs}};var Hs=function(t){var r=typeof t;return"string"==r||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==t:null===t};var Vs=function(t,r){var e=t.__data__;return Hs(r)?e["string"==typeof r?"string":"hash"]:e.map};var Gs=function(t){var r=Vs(this,t).delete(t);return this.size-=r?1:0,r};var Js=function(t){return Vs(this,t).get(t)};var qs=function(t){return Vs(this,t).has(t)};var Ks=function(t,r){var e=Vs(this,t),n=e.size;return e.set(t,r),this.size+=e.size==n?0:1,this};function Xs(t){var r=-1,e=null==t?0:t.length;for(this.clear();++r<e;){var n=t[r];this.set(n[0],n[1])}}Xs.prototype.clear=Us,Xs.prototype.delete=Gs,Xs.prototype.get=Js,Xs.prototype.has=qs,Xs.prototype.set=Ks;var Ys=Xs;var Zs=function(t,r){var e=this.__data__;if(e instanceof Zl){var n=e.__data__;if(!Es||n.length<199)return n.push([t,r]),this.size=++e.size,this;e=this.__data__=new Ys(n)}return e.set(t,r),this.size=e.size,this};function Qs(t){var r=this.__data__=new Zl(t);this.size=r.size}Qs.prototype.clear=Ql,Qs.prototype.delete=ts,Qs.prototype.get=rs,Qs.prototype.has=es,Qs.prototype.set=Zs;var tf=Qs,rf=function(){try{var t=ks(Object,"defineProperty");return t({},"",{}),t}catch(t){}}();var ef=function(t,r,e){"__proto__"==r&&rf?rf(t,r,{configurable:!0,enumerable:!0,value:e,writable:!0}):t[r]=e};var nf=function(t,r,e){(void 0!==e&&!Hl(t[r],e)||void 0===e&&!(r in t))&&ef(t,r,e)};var of=function(t){return function(r,e,n){for(var o=-1,a=Object(r),i=n(r),c=i.length;c--;){var u=i[t?c:++o];if(!1===e(a[u],u,a))break}return r}}(),af=e((function(t,r){var e=r&&!r.nodeType&&r,n=e&&t&&!t.nodeType&&t,o=n&&n.exports===e?as.Buffer:void 0,a=o?o.allocUnsafe:void 0;t.exports=function(t,r){if(r)return t.slice();var e=t.length,n=a?a(e):new t.constructor(e);return t.copy(n),n}})),cf=as.Uint8Array;var uf=function(t){var r=new t.constructor(t.byteLength);return new cf(r).set(new cf(t)),r};var lf=function(t,r){var e=r?uf(t.buffer):t.buffer;return new t.constructor(e,t.byteOffset,t.length)};var sf=function(t,r){var e=-1,n=t.length;for(r||(r=Array(n));++e<n;)r[e]=t[e];return r},ff=Object.create,pf=function(){function t(){}return function(r){if(!vs(r))return{};if(ff)return ff(r);t.prototype=r;var e=new t;return t.prototype=void 0,e}}();var hf=function(t,r){return function(e){return t(r(e))}}(Object.getPrototypeOf,Object),df=Object.prototype;var yf=function(t){var r=t&&t.constructor;return t===("function"==typeof r&&r.prototype||df)};var vf=function(t){return"function"!=typeof t.constructor||yf(t)?{}:pf(hf(t))};var gf=function(t){return null!=t&&"object"==typeof t};var bf=function(t){return gf(t)&&"[object Arguments]"==ys(t)},mf=Object.prototype,wf=mf.hasOwnProperty,xf=mf.propertyIsEnumerable,Sf=bf(function(){return arguments}())?bf:function(t){return gf(t)&&wf.call(t,"callee")&&!xf.call(t,"callee")},Of=Sf,_f=Array.isArray;var jf=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991};var Cf=function(t){return null!=t&&jf(t.length)&&!gs(t)};var Af=function(t){return gf(t)&&Cf(t)};var Mf=function(){return!1},Pf=e((function(t,r){var e=r&&!r.nodeType&&r,n=e&&t&&!t.nodeType&&t,o=n&&n.exports===e?as.Buffer:void 0,a=(o?o.isBuffer:void 0)||Mf;t.exports=a})),Tf=Function.prototype,kf=Object.prototype,Ef=Tf.toString,zf=kf.hasOwnProperty,Df=Ef.call(Object);var Rf=function(t){if(!gf(t)||"[object Object]"!=ys(t))return!1;var r=hf(t);if(null===r)return!0;var e=zf.call(r,"constructor")&&r.constructor;return"function"==typeof e&&e instanceof e&&Ef.call(e)==Df},If={};If["[object Float32Array]"]=If["[object Float64Array]"]=If["[object Int8Array]"]=If["[object Int16Array]"]=If["[object Int32Array]"]=If["[object Uint8Array]"]=If["[object Uint8ClampedArray]"]=If["[object Uint16Array]"]=If["[object Uint32Array]"]=!0,If["[object Arguments]"]=If["[object Array]"]=If["[object ArrayBuffer]"]=If["[object Boolean]"]=If["[object DataView]"]=If["[object Date]"]=If["[object Error]"]=If["[object Function]"]=If["[object Map]"]=If["[object Number]"]=If["[object Object]"]=If["[object RegExp]"]=If["[object Set]"]=If["[object String]"]=If["[object WeakMap]"]=!1;var Ff=function(t){return gf(t)&&jf(t.length)&&!!If[ys(t)]};var Lf=function(t){return function(r){return t(r)}},Nf=e((function(t,r){var e=r&&!r.nodeType&&r,n=e&&t&&!t.nodeType&&t,o=n&&n.exports===e&&ns.process,a=function(){try{var t=n&&n.require&&n.require("util").types;return t||o&&o.binding&&o.binding("util")}catch(t){}}();t.exports=a})),Wf=Nf&&Nf.isTypedArray,$f=Wf?Lf(Wf):Ff;var Bf=function(t,r){if(("constructor"!==r||"function"!=typeof t[r])&&"__proto__"!=r)return t[r]},Uf=Object.prototype.hasOwnProperty;var Hf=function(t,r,e){var n=t[r];Uf.call(t,r)&&Hl(n,e)&&(void 0!==e||r in t)||ef(t,r,e)};var Vf=function(t,r,e,n){var o=!e;e||(e={});for(var a=-1,i=r.length;++a<i;){var c=r[a],u=n?n(e[c],t[c],c,e,t):void 0;void 0===u&&(u=t[c]),o?ef(e,c,u):Hf(e,c,u)}return e};var Gf=function(t,r){for(var e=-1,n=Array(t);++e<t;)n[e]=r(e);return n},Jf=/^(?:0|[1-9]\d*)$/;var qf=function(t,r){var e=typeof t;return!!(r=null==r?9007199254740991:r)&&("number"==e||"symbol"!=e&&Jf.test(t))&&t>-1&&t%1==0&&t<r},Kf=Object.prototype.hasOwnProperty;var Xf=function(t,r){var e=_f(t),n=!e&&Of(t),o=!e&&!n&&Pf(t),a=!e&&!n&&!o&&$f(t),i=e||n||o||a,c=i?Gf(t.length,String):[],u=c.length;for(var l in t)!r&&!Kf.call(t,l)||i&&("length"==l||o&&("offset"==l||"parent"==l)||a&&("buffer"==l||"byteLength"==l||"byteOffset"==l)||qf(l,u))||c.push(l);return c};var Yf=function(t){var r=[];if(null!=t)for(var e in Object(t))r.push(e);return r},Zf=Object.prototype.hasOwnProperty;var Qf=function(t){if(!vs(t))return Yf(t);var r=yf(t),e=[];for(var n in t)("constructor"!=n||!r&&Zf.call(t,n))&&e.push(n);return e};var tp=function(t){return Cf(t)?Xf(t,!0):Qf(t)};var rp=function(t){return Vf(t,tp(t))};var ep=function(t,r,e,n,o,a,i){var c=Bf(t,e),u=Bf(r,e),l=i.get(u);if(l)nf(t,e,l);else{var s=a?a(c,u,e+"",t,r,i):void 0,f=void 0===s;if(f){var p=_f(u),h=!p&&Pf(u),d=!p&&!h&&$f(u);s=u,p||h||d?_f(c)?s=c:Af(c)?s=sf(c):h?(f=!1,s=af(u,!0)):d?(f=!1,s=lf(u,!0)):s=[]:Rf(u)||Of(u)?(s=c,Of(c)?s=rp(c):vs(c)&&!gs(c)||(s=vf(u))):f=!1}f&&(i.set(u,s),o(s,u,n,a,i),i.delete(u)),nf(t,e,s)}};var np=function t(r,e,n,o,a){r!==e&&of(e,(function(i,c){if(a||(a=new tf),vs(i))ep(r,e,c,n,t,o,a);else{var u=o?o(Bf(r,c),i,c+"",r,e,a):void 0;void 0===u&&(u=i),nf(r,c,u)}}),tp)};var op=function(t){return t};var ap=function(t,r,e){switch(e.length){case 0:return t.call(r);case 1:return t.call(r,e[0]);case 2:return t.call(r,e[0],e[1]);case 3:return t.call(r,e[0],e[1],e[2])}return t.apply(r,e)},ip=Math.max;var cp=function(t,r,e){return r=ip(void 0===r?t.length-1:r,0),function(){for(var n=arguments,o=-1,a=ip(n.length-r,0),i=Array(a);++o<a;)i[o]=n[r+o];o=-1;for(var c=Array(r+1);++o<r;)c[o]=n[o];return c[r]=e(i),ap(t,this,c)}};var up=function(t){return function(){return t}},lp=rf?function(t,r){return rf(t,"toString",{configurable:!0,enumerable:!1,value:up(r),writable:!0})}:op,sp=Date.now;var fp=function(t){var r=0,e=0;return function(){var n=sp(),o=16-(n-e);if(e=n,o>0){if(++r>=800)return arguments[0]}else r=0;return t.apply(void 0,arguments)}},pp=fp(lp);var hp=function(t,r){return pp(cp(t,r,op),t+"")};var dp=function(t,r,e){if(!vs(e))return!1;var n=typeof r;return!!("number"==n?Cf(e)&&qf(r,e.length):"string"==n&&r in e)&&Hl(e[r],t)};var yp=function(t){return hp((function(r,e){var n=-1,o=e.length,a=o>1?e[o-1]:void 0,i=o>2?e[2]:void 0;for(a=t.length>3&&"function"==typeof a?(o--,a):void 0,i&&dp(e[0],e[1],i)&&(a=o<3?void 0:a,o=1),r=Object(r);++n<o;){var c=e[n];c&&t(r,c,n,a)}return r}))}((function(t,r,e,n){np(t,r,e,n)})),vp=new Proxy({},{get:function(t,r,e){return"production"!==BUILD_ENV&&"undefined"!=typeof console&&r in console?console[r]:function(){}}}),gp=["echarts","cherryOptions"];function bp(t,r){var e=_l(t);if(jl){var n=jl(t);r&&(n=Tl(n).call(n,(function(r){return Dl(t,r).enumerable}))),e.push.apply(e,n)}return e}function mp(t){for(var r=1;r<arguments.length;r++){var e,n,o=null!=arguments[r]?arguments[r]:{};r%2?Su(e=bp(Object(o),!0)).call(e,(function(r){qi(t,r,o[r])})):Ll?$l(t,Ll(o)):Su(n=bp(Object(o))).call(n,(function(r){Bl(t,r,Dl(o,r))}))}return t}var wp={renderer:"svg",width:500,height:300},xp=function(){function t(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};sc(this,t);var e=r.echarts,n=r.cherryOptions,o=lc(r,gp);if(!e&&!window.echarts)throw new Error("table-echarts-plugin[init]: Package echarts not found.");this.options=mp(mp({},wp),o||{}),this.echartsRef=e||window.echarts,this.dom=null,this.cherryOptions=n}return fc(t,[{key:"getInstance",value:function(){if(!this.dom){this.dom=document.createElement("div"),this.dom.style.width="".concat(this.options.width,"px"),this.dom.style.height="".concat(this.options.height,"px"),this.dom.style.minHeight="300px",this.dom.style.display="block",this.dom.style.position="relative";var t=this.echartsRef.init(this.dom,null,this.options);window.addEventListener("resize",(function(){t.resize()}))}return this.echartsRef.getInstanceByDom(this.dom)}},{key:"render",value:function(t,r,e){var n,o,a,i,c,u,l=this;vp.log("Rendering chart:",t,r,e);var s={};switch(t){case"bar":s=this.renderBarChart(e,r);break;case"line":s=this.renderLineChart(e,r);break;case"radar":s=this.renderRadarChart(e,r);break;case"map":s=this.renderMapChart(e,r);break;case"heatmap":s=this.renderHeatmapChart(e,r);break;case"pie":s=this.renderPieChart(e,r);break;default:return""}vp.log("Chart options:",s);var f=dc(n="chart-".concat(gc(),"-")).call(n,Math.random().toString(36).substr(2,9)),p=Wc(e),h=Wc(r),d=dc(o=dc(a=dc(i=dc(c=dc(u='\n      <div class="cherry-echarts-wrapper" \n           style="width: '.concat(this.options.width,"px; height: ")).call(u,this.options.height,'px; min-height: 300px; display: block; position: relative; border: 1px solid #ddd;" \n           id="')).call(c,f,'"\n           data-chart-type="')).call(i,t,'"\n           data-table-data="')).call(a,p.replace(/"/g,"&quot;"),'"\n           data-chart-options="')).call(o,h.replace(/"/g,"&quot;"),'">\n        <div class="chart-loading" style="text-align: center; line-height: 300px; color: #666;">正在加载图表...</div>\n      </div>\n    '),y=function r(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,n=document.getElementById(f);if(n&&l.echartsRef)try{var o=l.echartsRef.init(n);vp.log("Chart initialized successfully:",f),o.setOption(s),"heatmap"!==t&&"pie"!==t||l.addClickHighlightEffect(o,t)}catch(t){vp.error("Failed to render chart:",t),vp.error("Chart options:",s),vp.error("Container:",n),n&&(n.innerHTML='<div style="text-align: center; line-height: 300px; color: red;">\n              图表渲染失败<br/>\n              <span style="font-size: 12px; color: #666;">错误: '.concat(t.message,"</span>\n            </div>"))}else if(e<10){var a;vp.log(dc(a="Retrying chart initialization for ".concat(f,", attempt: ")).call(a,e+1)),Xc((function(){return r(e+1)}),100)}else{vp.error("Failed to find chart container after 10 retries:",f,!!l.echartsRef);var i=document.getElementById(f);i&&(i.innerHTML='<div style="text-align: center; line-height: 300px; color: red;">\n            图表容器未找到<br/>\n            <span style="font-size: 12px; color: #666;">容器ID: '.concat(f,"</span>\n          </div>"))}};return Xc((function(){return y()}),50),d}},{key:"renderBarChart",value:function(t,r){return this.$renderChartCommon(t,r,"bar")}},{key:"renderLineChart",value:function(t,r){return this.$renderChartCommon(t,r,"line")}},{key:"renderRadarChart",value:function(t,r){return this.$renderRadarChartCommon(t,r)}},{key:"renderHeatmapChart",value:function(t,r){return this.$renderHeatmapChartCommon(t,r)}},{key:"renderPieChart",value:function(t,r){return this.$renderPieChartCommon(t,r)}},{key:"$renderRadarChartCommon",value:function(t,r){var e,n,o,a,i;vp.log("Rendering radar chart:",t);var c=ru(e=eu(n=t.header).call(n,1)).call(e,(function(r){var e,n=Math.max.apply(Math,Fi(ru(e=t.rows).call(e,(function(e){var n,o=nu(n=t.header).call(n,r);return vu(e[o].replace(/,/g,""))||0}))));return{name:r,max:Math.ceil(1.2*n)}})),u=ru(o=t.rows).call(o,(function(t,r){var e;return{name:t[0],value:ru(e=eu(t).call(t,1)).call(e,(function(t){return vu(t.replace(/,/g,""))||0})),areaStyle:{opacity:.1+.05*r},lineStyle:{width:2},itemStyle:{borderWidth:2}}}));return vp.log("Radar indicator:",c),vp.log("Radar seriesData:",u),{backgroundColor:"#fff",color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc"],tooltip:{trigger:"item",backgroundColor:"rgba(0,0,0,0.8)",borderColor:"#999",borderWidth:1,textStyle:{color:"#fff",fontSize:12},formatter:function(t){var r,e='<div style="margin-bottom:4px;font-weight:bold;">'.concat(t.name,"</div>");return Su(r=t.value).call(r,(function(r,n){e+='<div style="margin:2px 0;">',e+='<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:'.concat(t.color,';"></span>'),e+='<span style="font-weight:bold;">'.concat(c[n].name,"</span>"),e+='<span style="float:right;margin-left:20px;font-weight:bold;">'.concat(r,"</span>"),e+="</div>"})),e},extraCssText:"box-shadow: 0 2px 8px rgba(0,0,0,0.15); border-radius: 4px;"},legend:{data:ru(a=t.rows).call(a,(function(t){return t[0]})),orient:"horizontal",left:"center",top:"bottom",textStyle:{fontSize:12},itemWidth:12,itemHeight:12,selectedMode:"multiple",selector:[{type:"all",title:"全选"},{type:"inverse",title:"反选"}]},toolbox:{show:!0,orient:"vertical",left:"right",top:"center",feature:{dataView:{show:!0,readOnly:!1,title:"数据视图",lang:["数据视图","关闭","刷新"]},restore:{show:!0,title:"重置"},saveAsImage:{show:!0,title:"保存为图片",type:"png",backgroundColor:"#fff"}},iconStyle:{borderColor:"#999"},emphasis:{iconStyle:{borderColor:"#666"}}},radar:{name:{textStyle:{color:"#333",fontSize:12,fontWeight:"bold"},formatter:function(t){return t.length>6?"".concat(t.substr(0,6),"..."):t}},indicator:c,radius:"60%",center:["50%","50%"],splitNumber:5,shape:"polygon",splitArea:{areaStyle:{color:Au(i=["rgba(114, 172, 209, 0.2)","rgba(114, 172, 209, 0.4)","rgba(114, 172, 209, 0.6)","rgba(114, 172, 209, 0.8)","rgba(114, 172, 209, 1)"]).call(i)}},axisLine:{lineStyle:{color:"rgba(211, 253, 250, 0.8)"}},splitLine:{lineStyle:{color:"rgba(211, 253, 250, 0.8)"}}},series:[{name:"雷达图数据",type:"radar",data:u,emphasis:{lineStyle:{width:4},areaStyle:{opacity:.3}},animation:!0,animationDuration:1e3,animationEasing:"elasticOut"}],graphic:{elements:[{type:"text",left:"center",top:"5%",style:{text:"雷达图分析",fontSize:16,fontWeight:"bold",fill:"#333"}}]}}}},{key:"$renderHeatmapChartCommon",value:function(t,r){var e,n,o;vp.log("Rendering heatmap chart:",t);var a=eu(e=t.header).call(e,1),i=ru(n=t.rows).call(n,(function(t){return t[0]})),c=[];Su(o=t.rows).call(o,(function(t,r){var e;Su(e=eu(t).call(t,1)).call(e,(function(t,e){var n=vu(t.replace(/,/g,""))||0;c.push([e,r,n])}))}));var u=ru(c).call(c,(function(t){return t[2]})),l=Math.min.apply(Math,Fi(u)),s=Math.max.apply(Math,Fi(u));return{backgroundColor:"#fff",tooltip:{trigger:"item",backgroundColor:"rgba(0,0,0,0.8)",borderColor:"#999",borderWidth:1,textStyle:{color:"#fff",fontSize:12},formatter:function(t){var r,e;return dc(r=dc(e="".concat(i[t.data[1]],"<br/>")).call(e,a[t.data[0]],": <strong>")).call(r,t.data[2],"</strong>")},extraCssText:"box-shadow: 0 2px 8px rgba(0,0,0,0.15); border-radius: 4px;"},grid:{height:"50%",top:"10%",left:"10%",right:"10%"},xAxis:{type:"category",data:a,splitArea:{show:!0},axisLabel:{fontSize:12}},yAxis:{type:"category",data:i,splitArea:{show:!0},axisLabel:{fontSize:12}},visualMap:{min:l,max:s,calculable:!0,orient:"horizontal",left:"center",bottom:"15%",inRange:{color:["#313695","#4575b4","#74add1","#abd9e9","#e0f3f8","#ffffcc","#fee090","#fdae61","#f46d43","#d73027","#a50026"]},textStyle:{fontSize:12}},series:[{name:"热力图数据",type:"heatmap",data:c,label:{show:!0,fontSize:10},emphasis:{itemStyle:{shadowBlur:10,shadowColor:"rgba(0, 0, 0, 0.5)",borderWidth:2,borderColor:"#ff6b6b"}},select:{itemStyle:{borderWidth:2,borderColor:"#ff6b6b",opacity:1}},selectedMode:"single",animation:!0,animationDuration:1e3,animationEasing:"cubicOut"}],toolbox:{show:!0,orient:"vertical",left:"right",top:"bottom",feature:{dataView:{show:!0,readOnly:!1,title:"数据视图",lang:["数据视图","关闭","刷新"]},restore:{show:!0,title:"重置"},saveAsImage:{show:!0,title:"保存为图片",type:"png",backgroundColor:"#fff"}},iconStyle:{borderColor:"#999"},emphasis:{iconStyle:{borderColor:"#666"}}}}}},{key:"$renderPieChartCommon",value:function(t,r){var e;return vp.log("Rendering pie chart:",t),{backgroundColor:"#fff",tooltip:{trigger:"item",backgroundColor:"rgba(0,0,0,0.8)",borderColor:"#999",borderWidth:1,textStyle:{color:"#fff",fontSize:12},formatter:"{a} <br/>{b}: {c} ({d}%)",extraCssText:"box-shadow: 0 2px 8px rgba(0,0,0,0.15); border-radius: 4px;"},legend:{orient:"vertical",left:"left",top:"middle",textStyle:{fontSize:12}},series:[{name:"数据分布",type:"pie",radius:["40%","70%"],center:["50%","50%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"18",fontWeight:"bold"},itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)",borderWidth:3,borderColor:"#ff6b6b"}},select:{itemStyle:{borderWidth:3,borderColor:"#ff6b6b",opacity:1}},selectedMode:"single",labelLine:{show:!1},data:ru(e=t.rows).call(e,(function(t){return{name:t[0],value:vu(t[1].replace(/,/g,""))||0}})),animation:!0,animationDuration:1e3,animationEasing:"cubicOut"}],toolbox:{show:!0,orient:"vertical",left:"right",top:"center",feature:{dataView:{show:!0,readOnly:!1,title:"数据视图",lang:["数据视图","关闭","刷新"]},restore:{show:!0,title:"重置"},saveAsImage:{show:!0,title:"保存为图片",type:"png",backgroundColor:"#fff"}},iconStyle:{borderColor:"#999"},emphasis:{iconStyle:{borderColor:"#666"}}}}}},{key:"renderMapChart",value:function(t,r){return vp.log("开始渲染地图图表，选项:",r),r&&r.mapDataSource?(vp.log("检测到自定义地图数据源:",r.mapDataSource),window.echarts&&window.echarts.getMap("china")&&vp.log("清除现有地图数据以使用自定义地图数据源"),this.$loadCustomMapData(r.mapDataSource,!0)):(vp.log("使用默认地图数据源"),this.$loadChinaMapData()),this.$renderMapChartCommon(t,r)}},{key:"$loadChinaMapData",value:function(){if(void 0!==window.echarts)if(window.echarts.getMap("china"))vp.log("中国地图数据已存在");else{vp.log("正在加载中国地图数据...");var t=["https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json","./assets/data/china.json"];this.cherryOptions&&this.cherryOptions.toolbars&&this.cherryOptions.toolbars.config&&this.cherryOptions.toolbars.config.mapTable&&this.cherryOptions.toolbars.config.mapTable.sourceUrl&&(t=this.cherryOptions.toolbars.config.mapTable.sourceUrl,vp.log("使用配置的地图数据源:",t)),this.$tryLoadMapDataFromPaths(t,0)}else vp.error("ECharts 库未加载")}},{key:"$tryLoadMapDataFromPaths",value:function(t,r){var e=this;if(r>=t.length)vp.error("所有地图数据源都加载失败");else{var n=t[r];vp.log("尝试加载地图数据: ".concat(n)),this.$fetchMapData(n).catch((function(o){vp.warn("地图数据加载失败 (".concat(n,"):"),o.message),e.$tryLoadMapDataFromPaths(t,r+1)}))}}},{key:"$fetchMapData",value:function(t){var r=this;return fetch(t).then((function(r){var e;if(!r.ok)throw new Error(dc(e="HTTP error! status: ".concat(r.status," for ")).call(e,t));return r.json()})).then((function(e){return window.echarts.registerMap("china",e),vp.log("中国地图数据加载成功！来源: ".concat(t)),r.$refreshMapCharts(),e}))}},{key:"$loadCustomMapData",value:function(t){var r,e=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];t&&""!==Eu(t).call(t)?(vp.log(dc(r="正在加载用户自定义地图数据: ".concat(t)).call(r,n?" (强制重新加载)":"")),this.$fetchMapData(t).then((function(){vp.log("用户自定义地图数据加载成功，正在刷新所有地图图表"),e.$refreshMapCharts()})).catch((function(r){vp.warn("用户自定义地图数据加载失败 (".concat(t,"):"),r.message),vp.warn("自定义地图数据加载失败，回退到默认地图数据"),e.$loadChinaMapData()}))):vp.warn("自定义地图数据URL为空，使用默认加载方法")}},{key:"$refreshMapCharts",value:function(){var t=this,r=document.querySelectorAll('[id^="chart-"][data-chart-type="map"]');vp.log("Found map containers to refresh:",r.length),Su(r).call(r,(function(r){var e=r.id;vp.log("Refreshing map chart:",e);var n=r.getAttribute("data-table-data"),o=r.getAttribute("data-chart-options");if(n&&t.echartsRef)try{var a=JSON.parse(n),i=o?JSON.parse(o):{},c=t.$renderMapChartCommon(a,i),u=t.echartsRef.getInstanceByDom(r);if(u)u.setOption(c),vp.log("Map chart refreshed successfully:",e);else t.echartsRef.init(r).setOption(c),vp.log("Map chart recreated:",e)}catch(t){vp.error("Failed to refresh map chart:",e,t)}}))}},{key:"$renderMapChartCommon",value:function(t,r){var e;if(vp.log("Rendering map chart:",t),void 0===window.echarts)return vp.error("ECharts 库未加载"),{title:{text:"地图渲染失败: ECharts 库未加载",left:"center",textStyle:{color:"#ff0000"}}};if(!window.echarts.getMap("china"))return vp.warn("中国地图数据未加载，正在尝试加载..."),this.$loadChinaMapData(),{title:{text:"正在加载地图数据...",left:"center",top:"middle",textStyle:{color:"#666",fontSize:16}},graphic:{elements:[{type:"text",left:"center",top:"60%",style:{text:"如果长时间未显示，请检查网络连接",font:"12px sans-serif",fill:"#999"}}]}};var n={"北京":"北京市","天津":"天津市","上海":"上海市","重庆":"重庆市","河北":"河北省","山西":"山西省","辽宁":"辽宁省","吉林":"吉林省","黑龙江":"黑龙江省","江苏":"江苏省","浙江":"浙江省","安徽":"安徽省","福建":"福建省","江西":"江西省","山东":"山东省","河南":"河南省","湖北":"湖北省","湖南":"湖南省","广东":"广东省","海南":"海南省","四川":"四川省","贵州":"贵州省","云南":"云南省","陕西":"陕西省","甘肃":"甘肃省","青海":"青海省","台湾":"台湾省","内蒙古":"内蒙古自治区","广西":"广西壮族自治区","西藏":"西藏自治区","宁夏":"宁夏回族自治区","新疆":"新疆维吾尔自治区","香港":"香港特别行政区","澳门":"澳门特别行政区"},o=ru(e=t.rows).call(e,(function(t){var r,e=t[0],o=function(t){var r=Eu(t).call(t);if(n[r])return n[r];if(Hu(r).call(r,"市")||Hu(r).call(r,"省")||Hu(r).call(r,"自治区")||Hu(r).call(r,"特别行政区"))return r;for(var e=0,o=Yu(n);e<o.length;e++){var a=zi(o[e],2),i=a[0],c=a[1];if(cl(c).call(c,r)||cl(r).call(r,i))return c}return vp.warn("Province name not matched: ".concat(t)),r}(e),a=vu(t[1].replace(/,/g,""))||0;return vp.log(dc(r='Name mapping: "'.concat(e,'" -> "')).call(r,o,'"')),{name:o,value:a}}));return vp.log("Map data:",o),{backgroundColor:"#fff",title:{text:"地图数据分析",left:"center",top:"5%",textStyle:{fontSize:16,fontWeight:"bold",color:"#333"}},tooltip:{trigger:"item",backgroundColor:"rgba(0,0,0,0.8)",borderColor:"#999",borderWidth:1,textStyle:{color:"#fff",fontSize:12},formatter:function(t){var r;return dc(r="".concat(t.name,": ")).call(r,t.value||0)},extraCssText:"box-shadow: 0 2px 8px rgba(0,0,0,0.15); border-radius: 4px;"},visualMap:{min:Math.min.apply(Math,Fi(ru(o).call(o,(function(t){return t.value})))),max:Math.max.apply(Math,Fi(ru(o).call(o,(function(t){return t.value})))),left:"left",top:"bottom",text:["高","低"],calculable:!0,inRange:{color:["#e0ffff","#006edd"]},textStyle:{fontSize:12}},series:[{name:"地图数据",type:"map",map:"china",roam:!0,label:{show:!0,fontSize:10},emphasis:{label:{show:!0,fontSize:12,fontWeight:"bold"},itemStyle:{areaColor:"#ffefd5"}},data:o,itemStyle:{areaColor:"#f5f5f5",borderColor:"#999",borderWidth:.5}}],toolbox:{show:!0,orient:"vertical",left:"right",top:"center",feature:{dataView:{show:!0,readOnly:!1,title:"数据视图",lang:["数据视图","关闭","刷新"]},restore:{show:!0,title:"重置"},saveAsImage:{show:!0,title:"保存为图片",type:"png",backgroundColor:"#fff"}},iconStyle:{borderColor:"#999"},emphasis:{iconStyle:{borderColor:"#666"}}}}}},{key:"$renderChartCommon",value:function(t,r,e){var n,o,a,i;vp.log("Common chart rendering:",e,t);var c={bar:{type:"bar",barWidth:"60%",animation:!0,animationDuration:1e3,animationEasing:"elasticOut",animationDelay:function(t){return 10*t},name:"",data:[],emphasis:{focus:"series",itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}},label:{show:!1,position:"top",formatter:"{c}"}},line:{type:"line",animation:!0,animationDuration:1e3,animationEasing:"elasticOut",animationDelay:function(t){return 10*t},name:"",data:[],symbol:"circle",symbolSize:8,lineStyle:{width:3,cap:"round",join:"round"},itemStyle:{borderWidth:2,borderColor:"#fff"},emphasis:{focus:"series",lineStyle:{width:5},itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)",borderWidth:3}},smooth:.3,markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]}}};if(!c[e])return{};var u=gl(n=t.rows).call(n,(function(t,r){var n;return vp.log("Processing row:",r),t.legend.data.push(r[0]),t.series.push(mp(mp({},c[e]),{},{name:r[0],data:ru(n=eu(r).call(r,1)).call(n,(function(t){var r=vu(t.replace(/,/g,""));return vp.log("Parsed data:",t,"->",r),r}))})),t}),{legend:{data:[],type:"scroll",orient:"horizontal",left:"center",top:"top",textStyle:{fontSize:12},itemWidth:12,itemHeight:12,selectedMode:"multiple",selector:[{type:"all",title:"全选"},{type:"inverse",title:"反选"}]},series:[]}),l=mp(mp({},u),{},{backgroundColor:"#fff",color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc"],tooltip:{trigger:"axis",backgroundColor:"rgba(0,0,0,0.8)",borderColor:"#999",borderWidth:1,textStyle:{color:"#fff",fontSize:12},axisPointer:{type:"line"===e?"cross":"shadow",label:{backgroundColor:"#6a7985"},crossStyle:{color:"#999"}},formatter:function(t){var r='<div style="margin-bottom:4px;font-weight:bold;">'.concat(t[0].axisValueLabel,"</div>");return Su(t).call(t,(function(t,e){r+='<div style="margin:2px 0;">',r+='<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:'.concat(t.color,';"></span>'),r+='<span style="font-weight:bold;">'.concat(t.seriesName,"</span>"),r+='<span style="float:right;margin-left:20px;font-weight:bold;">'.concat(t.value,"</span>"),r+="</div>"})),r},extraCssText:"box-shadow: 0 2px 8px rgba(0,0,0,0.15); border-radius: 4px;"},toolbox:{show:!0,orient:"vertical",left:"right",top:"center",feature:{mark:{show:!0,title:"辅助线开关"},dataView:{show:!0,readOnly:!1,title:"数据视图",lang:["数据视图","关闭","刷新"]},magicType:{show:!0,type:["line","bar"],title:{line:"切换为折线图",bar:"切换为柱状图"}},restore:{show:!0,title:"重置"},saveAsImage:{show:!0,title:"保存为图片",type:"png",backgroundColor:"#fff"}},iconStyle:{borderColor:"#999"},emphasis:{iconStyle:{borderColor:"#666"}}},xAxis:{data:eu(o=t.header).call(o,1),type:"category",axisLine:{lineStyle:{color:"#333"}},axisLabel:{color:"#333",rotate:Sl(a=eu(i=t.header).call(i,1)).call(a,(function(t){return t.length>4}))?45:0,interval:0,fontSize:11},axisTick:{alignWithLabel:!0}},yAxis:{type:"value",axisLabel:{color:"#333",fontSize:11,formatter:function(t){return t>=1e6?"".concat((t/1e6).toFixed(1),"M"):t>=1e3?"".concat((t/1e3).toFixed(1),"K"):t}},axisLine:{lineStyle:{color:"#333"}},splitLine:{lineStyle:{color:"#eee",type:"dashed"}},nameTextStyle:{color:"#333"}},grid:{containLabel:!0,left:"3%",right:"8%",bottom:"8%",top:"15%"},dataZoom:[{type:"slider",show:t.header.length>8,xAxisIndex:[0],start:0,end:100,bottom:"2%",height:20,handleIcon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23.1h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z",handleSize:"80%",handleStyle:{color:"#fff",shadowBlur:3,shadowColor:"rgba(0, 0, 0, 0.6)",shadowOffsetX:2,shadowOffsetY:2}},{type:"inside",xAxisIndex:[0],start:0,end:100}],brush:{toolbox:["rect","polygon","lineX","lineY","keep","clear"],xAxisIndex:0}});return vp.log("Final chart options:",l),l}},{key:"addClickHighlightEffect",value:function(t,r){var e=this,n=null;t.on("click",(function(o){if(vp.log("Chart clicked:",o),n===o.dataIndex)return n=null,void e.clearHighlight(t,r);n=o.dataIndex}))}},{key:"clearHighlight",value:function(t,r){t.dispatchAction({type:"downplay",seriesIndex:0});var e=t.getOption().series[0].data;Su(e).call(e,(function(t){t.itemStyle&&(delete t.itemStyle.opacity,delete t.itemStyle.borderWidth,delete t.itemStyle.borderColor)})),t.setOption({series:[{data:e}]})}},{key:"onDestroy",value:function(){this.dom&&this.echartsRef.dispose(this.dom)}}],[{key:"install",value:function(r){if("undefined"==typeof window)return vp.warn("echarts-table-engine only works in browser."),void yp(r,{engine:{syntax:{table:{enableChart:!1}}}});yp(r,{engine:{syntax:{table:{enableChart:!0,chartRenderEngine:t,externals:["echarts"]}}}})}}])}();return xp}));
