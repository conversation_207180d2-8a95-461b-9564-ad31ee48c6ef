!function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define(["../../lib/codemirror"],e):e(CodeMirror)}(function(rt){"use strict";rt.defineMode("javascript",function(e,l){var t,r,n,a,f=e.indentUnit,d=l.statementIndent,i=l.jsonld,o=l.json||i,c=!1!==l.trackScope,u=l.typescript,p=l.wordCharacters||/[\w$\xa1-\uffff]/,s=(t=m("keyword a"),r=m("keyword b"),n=m("keyword c"),a=m("keyword d"),e=m("operator"),{if:m("if"),while:t,with:t,else:r,do:r,try:r,finally:r,return:a,break:a,continue:a,new:m("new"),delete:n,void:n,throw:n,debugger:m("debugger"),var:m("var"),const:m("var"),let:m("var"),function:m("function"),catch:m("catch"),for:m("for"),switch:m("switch"),case:m("case"),default:m("default"),in:e,typeof:e,instanceof:e,true:e={type:"atom",style:"atom"},false:e,null:e,undefined:e,NaN:e,Infinity:e,this:m("this"),class:m("class"),super:m("atom"),yield:n,export:m("export"),import:m("import"),extends:n,await:n});function m(e){return{type:e,style:"keyword"}}var k,v,y=/[+\-*&%=<>!?|~^@]/,w=/^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)"/;function b(e,t,r){return k=e,v=r,t}function x(e,t){var a,r=e.next();if('"'==r||"'"==r)return t.tokenize=(a=r,function(e,t){var r,n=!1;if(i&&"@"==e.peek()&&e.match(w))return t.tokenize=x,b("jsonld-keyword","meta");for(;null!=(r=e.next())&&(r!=a||n);)n=!n&&"\\"==r;return n||(t.tokenize=x),b("string","string")}),t.tokenize(e,t);if("."==r&&e.match(/^\d[\d_]*(?:[eE][+\-]?[\d_]+)?/))return b("number","number");if("."==r&&e.match(".."))return b("spread","meta");if(/[\[\]{}\(\),;\:\.]/.test(r))return b(r);if("="==r&&e.eat(">"))return b("=>","operator");if("0"==r&&e.match(/^(?:x[\dA-Fa-f_]+|o[0-7_]+|b[01_]+)n?/))return b("number","number");if(/\d/.test(r))return e.match(/^[\d_]*(?:n|(?:\.[\d_]*)?(?:[eE][+\-]?[\d_]+)?)?/),b("number","number");if("/"==r)return e.eat("*")?(t.tokenize=h)(e,t):e.eat("/")?(e.skipToEnd(),b("comment","comment")):tt(e,t,1)?(function(e){for(var t,r=!1,n=!1;null!=(t=e.next());){if(!r){if("/"==t&&!n)return;"["==t?n=!0:n&&"]"==t&&(n=!1)}r=!r&&"\\"==t}}(e),e.match(/^\b(([gimyus])(?![gimyus]*\2))+\b/),b("regexp","string-2")):(e.eat("="),b("operator","operator",e.current()));if("`"==r)return(t.tokenize=g)(e,t);if("#"==r&&"!"==e.peek())return e.skipToEnd(),b("meta","meta");if("#"==r&&e.eatWhile(p))return b("variable","property");if("<"==r&&e.match("!--")||"-"==r&&e.match("->")&&!/\S/.test(e.string.slice(0,e.start)))return e.skipToEnd(),b("comment","comment");if(y.test(r))return">"==r&&t.lexical&&">"==t.lexical.type||(e.eat("=")?"!"!=r&&"="!=r||e.eat("="):/[<>*+\-|&?]/.test(r)&&(e.eat(r),">"==r&&e.eat(r))),"?"==r&&e.eat(".")?b("."):b("operator","operator",e.current());if(p.test(r)){e.eatWhile(p);r=e.current();if("."!=t.lastType){if(s.propertyIsEnumerable(r)){t=s[r];return b(t.type,t.style,r)}if("async"==r&&e.match(/^(\s|\/\*([^*]|\*(?!\/))*?\*\/)*[\[\(\w]/,!1))return b("async","keyword",r)}return b("variable","variable",r)}}function h(e,t){for(var r,n=!1;r=e.next();){if("/"==r&&n){t.tokenize=x;break}n="*"==r}return b("comment","comment")}function g(e,t){for(var r,n=!1;null!=(r=e.next());){if(!n&&("`"==r||"$"==r&&e.eat("{"))){t.tokenize=x;break}n=!n&&"\\"==r}return b("quasi","string-2",e.current())}function j(e,t){t.fatArrowAt&&(t.fatArrowAt=null);var r,n=e.string.indexOf("=>",e.start);if(!(n<0)){!u||(r=/:\s*(?:\w+(?:<[^>]*>|\[\])?|\{[^}]*\})\s*$/.exec(e.string.slice(e.start,n)))&&(n=r.index);for(var a=0,i=!1,o=n-1;0<=o;--o){var c=e.string.charAt(o),s="([{}])".indexOf(c);if(0<=s&&s<3){if(!a){++o;break}if(0==--a){"("==c&&(i=!0);break}}else if(3<=s&&s<6)++a;else if(p.test(c))i=!0;else if(/["'\/`]/.test(c))for(;;--o){if(0==o)return;if(e.string.charAt(o-1)==c&&"\\"!=e.string.charAt(o-2)){o--;break}}else if(i&&!a){++o;break}}i&&!a&&(t.fatArrowAt=o)}}var M={atom:!0,number:!0,variable:!0,string:!0,regexp:!0,this:!0,import:!0,"jsonld-keyword":!0};function A(e,t,r,n,a,i){this.indented=e,this.column=t,this.type=r,this.prev=a,this.info=i,null!=n&&(this.align=n)}function V(e,t,r,n,a){var i=e.cc;for(E.state=e,E.stream=a,E.marked=null,E.cc=i,E.style=t,e.lexical.hasOwnProperty("align")||(e.lexical.align=!0);;)if((i.length?i.pop():o?D:F)(r,n)){for(;i.length&&i[i.length-1].lex;)i.pop()();return E.marked?E.marked:"variable"==r&&function(e,t){if(c){for(var r=e.localVars;r;r=r.next)if(r.name==t)return 1;for(var n=e.context;n;n=n.prev)for(r=n.vars;r;r=r.next)if(r.name==t)return 1}}(e,n)?"variable-2":t}}var E={state:null,column:null,marked:null,cc:null};function z(){for(var e=arguments.length-1;0<=e;e--)E.cc.push(arguments[e])}function I(){return z.apply(null,arguments),!0}function T(e,t){for(var r=t;r;r=r.next)if(r.name==e)return 1}function $(e){var t=E.state;if(E.marked="def",c){if(t.context)if("var"==t.lexical.info&&t.context&&t.context.block){var r=function e(t,r){{if(r){if(r.block){var n=e(t,r.prev);return n?n==r.prev?r:new C(n,r.vars,!0):null}return T(t,r.vars)?r:new C(r.prev,new S(t,r.vars),!1)}return null}}(e,t.context);if(null!=r)return void(t.context=r)}else if(!T(e,t.localVars))return void(t.localVars=new S(e,t.localVars));l.globalVars&&!T(e,t.globalVars)&&(t.globalVars=new S(e,t.globalVars))}}function q(e){return"public"==e||"private"==e||"protected"==e||"abstract"==e||"readonly"==e}function C(e,t,r){this.prev=e,this.vars=t,this.block=r}function S(e,t){this.name=e,this.next=t}var _=new S("this",new S("arguments",null));function O(){E.state.context=new C(E.state.context,E.state.localVars,!1),E.state.localVars=_}function P(){E.state.context=new C(E.state.context,E.state.localVars,!0),E.state.localVars=null}function N(){E.state.localVars=E.state.context.vars,E.state.context=E.state.context.prev}function U(n,a){function e(){var e=E.state,t=e.indented;if("stat"==e.lexical.type)t=e.lexical.indented;else for(var r=e.lexical;r&&")"==r.type&&r.align;r=r.prev)t=r.indented;e.lexical=new A(t,E.stream.column(),n,null,e.lexical,a)}return e.lex=!0,e}function W(){var e=E.state;e.lexical.prev&&(")"==e.lexical.type&&(e.indented=e.lexical.indented),e.lexical=e.lexical.prev)}function B(r){return function e(t){return t==r?I():";"==r||"}"==t||")"==t||"]"==t?z():I(e)}}function F(e,t){return"var"==e?I(U("vardef",t),Ve,B(";"),W):"keyword a"==e?I(U("form"),J,F,W):"keyword b"==e?I(U("form"),F,W):"keyword d"==e?E.stream.match(/^\s*$/,!1)?I():I(U("stat"),L,B(";"),W):"debugger"==e?I(B(";")):"{"==e?I(U("}"),P,le,W,N):";"==e?I():"if"==e?("else"==E.state.lexical.info&&E.state.cc[E.state.cc.length-1]==W&&E.state.cc.pop()(),I(U("form"),J,F,W,qe)):"function"==e?I(Oe):"for"==e?I(U("form"),P,Ce,F,N,W):"class"==e||u&&"interface"==t?(E.marked="keyword",I(U("form","class"==e?e:t),Be,W)):"variable"==e?u&&"declare"==t?(E.marked="keyword",I(F)):u&&("module"==t||"enum"==t||"type"==t)&&E.stream.match(/^\s*\w/,!1)?(E.marked="keyword","enum"==t?I(Ze):"type"==t?I(Ne,B("operator"),ke,B(";")):I(U("form"),Ee,B("{"),U("}"),le,W,W)):u&&"namespace"==t?(E.marked="keyword",I(U("form"),D,F,W)):u&&"abstract"==t?(E.marked="keyword",I(F)):I(U("stat"),ne):"switch"==e?I(U("form"),J,B("{"),U("}","switch"),P,le,W,W,N):"case"==e?I(D,B(":")):"default"==e?I(B(":")):"catch"==e?I(U("form"),O,H,F,W,N):"export"==e?I(U("stat"),Ge,W):"import"==e?I(U("stat"),Ke,W):"async"==e?I(F):"@"==t?I(D,F):z(U("stat"),D,B(";"),W)}function H(e){if("("==e)return I(Ue,B(")"))}function D(e,t){return K(e,t,!1)}function G(e,t){return K(e,t,!0)}function J(e){return"("!=e?z():I(U(")"),L,B(")"),W)}function K(e,t,r){if(E.state.fatArrowAt==E.stream.start){var n=r?ee:Z;if("("==e)return I(O,U(")"),se(Ue,")"),W,B("=>"),n,N);if("variable"==e)return z(O,Ee,B("=>"),n,N)}var a,n=r?R:Q;return M.hasOwnProperty(e)?I(n):"function"==e?I(Oe,n):"class"==e||u&&"interface"==t?(E.marked="keyword",I(U("form"),We,W)):"keyword c"==e||"async"==e?I(r?G:D):"("==e?I(U(")"),L,B(")"),W,n):"operator"==e||"spread"==e?I(r?G:D):"["==e?I(U("]"),Ye,W,n):"{"==e?ue(ie,"}",null,n):"quasi"==e?z(X,n):"new"==e?I((a=r,function(e){return"."==e?I(a?re:te):"variable"==e&&u?I(je,a?R:Q):z(a?G:D)})):I()}function L(e){return e.match(/[;\}\)\],]/)?z():z(D)}function Q(e,t){return","==e?I(L):R(e,t,!1)}function R(e,t,r){var n=0==r?Q:R,a=0==r?D:G;return"=>"==e?I(O,r?ee:Z,N):"operator"==e?/\+\+|--/.test(t)||u&&"!"==t?I(n):u&&"<"==t&&E.stream.match(/^([^<>]|<[^<>]*>)*>\s*\(/,!1)?I(U(">"),se(ke,">"),W,n):"?"==t?I(D,B(":"),a):I(a):"quasi"==e?z(X,n):";"!=e?"("==e?ue(G,")","call",n):"."==e?I(ae,n):"["==e?I(U("]"),L,B("]"),W,n):u&&"as"==t?(E.marked="keyword",I(ke,n)):"regexp"==e?(E.state.lastType=E.marked="operator",E.stream.backUp(E.stream.pos-E.stream.start-1),I(a)):void 0:void 0}function X(e,t){return"quasi"!=e?z():"${"!=t.slice(t.length-2)?I(X):I(L,Y)}function Y(e){if("}"==e)return E.marked="string-2",E.state.tokenize=g,I(X)}function Z(e){return j(E.stream,E.state),z("{"==e?F:D)}function ee(e){return j(E.stream,E.state),z("{"==e?F:G)}function te(e,t){if("target"==t)return E.marked="keyword",I(Q)}function re(e,t){if("target"==t)return E.marked="keyword",I(R)}function ne(e){return":"==e?I(W,F):z(Q,B(";"),W)}function ae(e){if("variable"==e)return E.marked="property",I()}function ie(e,t){return"async"==e?(E.marked="property",I(ie)):"variable"!=e&&"keyword"!=E.style?"number"==e||"string"==e?(E.marked=i?"property":E.style+" property",I(ce)):"jsonld-keyword"==e?I(ce):u&&q(t)?(E.marked="keyword",I(ie)):"["==e?I(D,fe,B("]"),ce):"spread"==e?I(G,ce):"*"==t?(E.marked="keyword",I(ie)):":"==e?z(ce):void 0:(E.marked="property","get"==t||"set"==t?I(oe):(u&&E.state.fatArrowAt==E.stream.start&&(r=E.stream.match(/^\s*:\s*/,!1))&&(E.state.fatArrowAt=E.stream.pos+r[0].length),I(ce)));var r}function oe(e){return"variable"!=e?z(ce):(E.marked="property",I(Oe))}function ce(e){return":"==e?I(G):"("==e?z(Oe):void 0}function se(n,a,i){function o(e,t){if(i?-1<i.indexOf(e):","==e){var r=E.state.lexical;return"call"==r.info&&(r.pos=(r.pos||0)+1),I(function(e,t){return e==a||t==a?z():z(n)},o)}return e==a||t==a?I():i&&-1<i.indexOf(";")?z(n):I(B(a))}return function(e,t){return e==a||t==a?I():z(n,o)}}function ue(e,t,r){for(var n=3;n<arguments.length;n++)E.cc.push(arguments[n]);return I(U(t,r),se(e,t),W)}function le(e){return"}"==e?I():z(F,le)}function fe(e,t){if(u)return":"==e?I(ke):"?"==t?I(fe):void 0}function de(e,t){if(u&&(":"==e||"in"==t))return I(ke)}function pe(e){if(u&&":"==e)return E.stream.match(/^\s*\w+\s+is\b/,!1)?I(D,me,ke):I(ke)}function me(e,t){if("is"==t)return E.marked="keyword",I()}function ke(e,t){return"keyof"==t||"typeof"==t||"infer"==t||"readonly"==t?(E.marked="keyword",I("typeof"==t?G:ke)):"variable"==e||"void"==t?(E.marked="type",I(ge)):"|"==t||"&"==t?I(ke):"string"==e||"number"==e||"atom"==e?I(ge):"["==e?I(U("]"),se(ke,"]",","),W,ge):"{"==e?I(U("}"),ye,W,ge):"("==e?I(se(he,")"),ve,ge):"<"==e?I(se(ke,">"),ke):"quasi"==e?z(be,ge):void 0}function ve(e){if("=>"==e)return I(ke)}function ye(e){return e.match(/[\}\)\]]/)?I():","==e||";"==e?I(ye):z(we,ye)}function we(e,t){return"variable"==e||"keyword"==E.style?(E.marked="property",I(we)):"?"==t||"number"==e||"string"==e?I(we):":"==e?I(ke):"["==e?I(B("variable"),de,B("]"),we):"("==e?z(Pe,we):e.match(/[;\}\)\],]/)?void 0:I()}function be(e,t){return"quasi"!=e?z():"${"!=t.slice(t.length-2)?I(be):I(ke,xe)}function xe(e){if("}"==e)return E.marked="string-2",E.state.tokenize=g,I(be)}function he(e,t){return"variable"==e&&E.stream.match(/^\s*[?:]/,!1)||"?"==t?I(he):":"==e?I(ke):"spread"==e?I(he):z(ke)}function ge(e,t){return"<"==t?I(U(">"),se(ke,">"),W,ge):"|"==t||"."==e||"&"==t?I(ke):"["==e?I(ke,B("]"),ge):"extends"==t||"implements"==t?(E.marked="keyword",I(ke)):"?"==t?I(ke,B(":"),ke):void 0}function je(e,t){if("<"==t)return I(U(">"),se(ke,">"),W,ge)}function Me(){return z(ke,Ae)}function Ae(e,t){if("="==t)return I(ke)}function Ve(e,t){return"enum"==t?(E.marked="keyword",I(Ze)):z(Ee,fe,Te,$e)}function Ee(e,t){return u&&q(t)?(E.marked="keyword",I(Ee)):"variable"==e?($(t),I()):"spread"==e?I(Ee):"["==e?ue(Ie,"]"):"{"==e?ue(ze,"}"):void 0}function ze(e,t){return"variable"!=e||E.stream.match(/^\s*:/,!1)?("variable"==e&&(E.marked="property"),"spread"==e?I(Ee):"}"==e?z():"["==e?I(D,B("]"),B(":"),ze):I(B(":"),Ee,Te)):($(t),I(Te))}function Ie(){return z(Ee,Te)}function Te(e,t){if("="==t)return I(G)}function $e(e){if(","==e)return I(Ve)}function qe(e,t){if("keyword b"==e&&"else"==t)return I(U("form","else"),F,W)}function Ce(e,t){return"await"==t?I(Ce):"("==e?I(U(")"),Se,W):void 0}function Se(e){return"var"==e?I(Ve,_e):("variable"==e?I:z)(_e)}function _e(e,t){return")"==e?I():";"==e?I(_e):"in"==t||"of"==t?(E.marked="keyword",I(D,_e)):z(D,_e)}function Oe(e,t){return"*"==t?(E.marked="keyword",I(Oe)):"variable"==e?($(t),I(Oe)):"("==e?I(O,U(")"),se(Ue,")"),W,pe,F,N):u&&"<"==t?I(U(">"),se(Me,">"),W,Oe):void 0}function Pe(e,t){return"*"==t?(E.marked="keyword",I(Pe)):"variable"==e?($(t),I(Pe)):"("==e?I(O,U(")"),se(Ue,")"),W,pe,N):u&&"<"==t?I(U(">"),se(Me,">"),W,Pe):void 0}function Ne(e,t){return"keyword"==e||"variable"==e?(E.marked="type",I(Ne)):"<"==t?I(U(">"),se(Me,">"),W):void 0}function Ue(e,t){return"@"==t&&I(D,Ue),"spread"==e?I(Ue):u&&q(t)?(E.marked="keyword",I(Ue)):u&&"this"==e?I(fe,Te):z(Ee,fe,Te)}function We(e,t){return("variable"==e?Be:Fe)(e,t)}function Be(e,t){if("variable"==e)return $(t),I(Fe)}function Fe(e,t){return"<"==t?I(U(">"),se(Me,">"),W,Fe):"extends"==t||"implements"==t||u&&","==e?("implements"==t&&(E.marked="keyword"),I(u?ke:D,Fe)):"{"==e?I(U("}"),He,W):void 0}function He(e,t){return"async"==e||"variable"==e&&("/aicode/static"==t||"get"==t||"set"==t||u&&q(t))&&E.stream.match(/^\s+[\w$\xa1-\uffff]/,!1)?(E.marked="keyword",I(He)):"variable"==e||"keyword"==E.style?(E.marked="property",I(De,He)):"number"==e||"string"==e?I(De,He):"["==e?I(D,fe,B("]"),De,He):"*"==t?(E.marked="keyword",I(He)):u&&"("==e?z(Pe,He):";"==e||","==e?I(He):"}"==e?I():"@"==t?I(D,He):void 0}function De(e,t){if("!"==t)return I(De);if("?"==t)return I(De);if(":"==e)return I(ke,Te);if("="==t)return I(G);t=E.state.lexical.prev;return z(t&&"interface"==t.info?Pe:Oe)}function Ge(e,t){return"*"==t?(E.marked="keyword",I(Xe,B(";"))):"default"==t?(E.marked="keyword",I(D,B(";"))):"{"==e?I(se(Je,"}"),Xe,B(";")):z(F)}function Je(e,t){return"as"==t?(E.marked="keyword",I(B("variable"))):"variable"==e?z(G,Je):void 0}function Ke(e){return"string"==e?I():"("==e?z(D):"."==e?z(Q):z(Le,Qe,Xe)}function Le(e,t){return"{"==e?ue(Le,"}"):("variable"==e&&$(t),"*"==t&&(E.marked="keyword"),I(Re))}function Qe(e){if(","==e)return I(Le,Qe)}function Re(e,t){if("as"==t)return E.marked="keyword",I(Le)}function Xe(e,t){if("from"==t)return E.marked="keyword",I(D)}function Ye(e){return"]"==e?I():z(se(G,"]"))}function Ze(){return z(U("form"),Ee,B("{"),U("}"),se(et,"}"),W,W)}function et(){return z(Ee,Te)}function tt(e,t,r){return t.tokenize==x&&/^(?:operator|sof|keyword [bcd]|case|new|export|default|spread|[\[{}\(,;:]|=>)$/.test(t.lastType)||"quasi"==t.lastType&&/\{\s*$/.test(e.string.slice(0,e.pos-(r||0)))}return O.lex=P.lex=!0,W.lex=N.lex=!0,{startState:function(e){e={tokenize:x,lastType:"sof",cc:[],lexical:new A((e||0)-f,0,"block",!1),localVars:l.localVars,context:l.localVars&&new C(null,null,!1),indented:e||0};return l.globalVars&&"object"==typeof l.globalVars&&(e.globalVars=l.globalVars),e},token:function(e,t){if(e.sol()&&(t.lexical.hasOwnProperty("align")||(t.lexical.align=!1),t.indented=e.indentation(),j(e,t)),t.tokenize!=h&&e.eatSpace())return null;var r=t.tokenize(e,t);return"comment"==k?r:(t.lastType="operator"!=k||"++"!=v&&"--"!=v?k:"incdec",V(t,r,k,v,e))},indent:function(e,t){if(e.tokenize==h||e.tokenize==g)return rt.Pass;if(e.tokenize!=x)return 0;var r,n=t&&t.charAt(0),a=e.lexical;if(!/^\s*else\b/.test(t))for(var i=e.cc.length-1;0<=i;--i){var o=e.cc[i];if(o==W)a=a.prev;else if(o!=qe&&o!=N)break}for(;("stat"==a.type||"form"==a.type)&&("}"==n||(r=e.cc[e.cc.length-1])&&(r==Q||r==R)&&!/^[,\.=+\-*:?[\(]/.test(t));)a=a.prev;var c,s=(a=d&&")"==a.type&&"stat"==a.prev.type?a.prev:a).type,u=n==s;return"vardef"==s?a.indented+("operator"==e.lastType||","==e.lastType?a.info.length+1:0):"form"==s&&"{"==n?a.indented:"form"==s?a.indented+f:"stat"==s?a.indented+(c=t,"operator"==(s=e).lastType||","==s.lastType||y.test(c.charAt(0))||/[,.]/.test(c.charAt(0))?d||f:0):"switch"!=a.info||u||0==l.doubleIndentSwitch?a.align?a.column+(u?0:1):a.indented+(u?0:f):a.indented+(/^(?:case|default)\b/.test(t)?f:2*f)},electricInput:/^\s*(?:case .*?:|default:|\{|\})$/,blockCommentStart:o?null:"/*",blockCommentEnd:o?null:"*/",blockCommentContinue:o?null:" * ",lineComment:o?null:"//",fold:"brace",closeBrackets:"()[]{}''\"\"``",helperType:o?"json":"javascript",jsonldMode:i,jsonMode:o,expressionAllowed:tt,skipExpression:function(e){V(e,"atom","atom","true",new rt.StringStream("",2,null))}}}),rt.registerHelper("wordChars","javascript",/[\w$]/),rt.defineMIME("text/javascript","javascript"),rt.defineMIME("text/ecmascript","javascript"),rt.defineMIME("application/javascript","javascript"),rt.defineMIME("application/x-javascript","javascript"),rt.defineMIME("application/ecmascript","javascript"),rt.defineMIME("application/json",{name:"javascript",json:!0}),rt.defineMIME("application/x-json",{name:"javascript",json:!0}),rt.defineMIME("application/manifest+json",{name:"javascript",json:!0}),rt.defineMIME("application/ld+json",{name:"javascript",jsonld:!0}),rt.defineMIME("text/typescript",{name:"javascript",typescript:!0}),rt.defineMIME("application/typescript",{name:"javascript",typescript:!0})});