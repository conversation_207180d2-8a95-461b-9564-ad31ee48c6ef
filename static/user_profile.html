<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户信息 - 全栈助手</title>

    <!-- Bootstrap CSS -->
    <link href="/aicode/static/external/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="/aicode/static/external/all.min.css" rel="stylesheet">

    <style>
        body {
            background-color: #f8f9fa;
        }

        .profile-container {
            max-width: 600px;
            margin: 50px auto;
        }

        .profile-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .profile-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .profile-header h3 {
            margin: 0;
            font-weight: 600;
        }

        .profile-header .user-icon {
            font-size: 60px;
            margin-bottom: 15px;
        }

        .profile-body {
            padding: 30px;
        }

        .form-label {
            font-weight: 500;
            color: #333;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(102, 126, 234, 0.4);
        }

        .back-link {
            color: white;
            text-decoration: none;
        }

        .back-link:hover {
            color: #f0f0f0;
            text-decoration: underline;
        }
    </style>
</head>

<body>
    <div class="profile-container">
        <div class="profile-card">
            <div class="profile-header">
                <a href="/" class="back-link">
                    <i class="fas fa-arrow-left"></i> 返回首页
                </a>
                <div class="user-icon mt-3">
                    <i class="fas fa-user-circle"></i>
                </div>
                <h3>用户信息</h3>
                <p class="mb-0">管理您的账户设置</p>
            </div>

            <div class="profile-body">
                <div id="alert-container"></div>

                <!-- 用户名显示 -->
                <div class="mb-4">
                    <label class="form-label">
                        <i class="fas fa-user"></i> 用户名
                    </label>
                    <input type="text" class="form-control" id="username" value="admin" disabled>
                    <small class="form-text text-muted">用户名不可修改</small>
                </div>

                <hr class="my-4">

                <h5 class="mb-3"><i class="fas fa-key"></i> 修改密码</h5>

                <form id="changePasswordForm">
                    <div class="mb-3">
                        <label for="oldPassword" class="form-label">
                            <i class="fas fa-lock"></i> 原密码 <span class="text-danger">*</span>
                        </label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="oldPassword" required
                                placeholder="请输入原密码">
                            <button class="btn btn-outline-secondary" type="button"
                                onclick="togglePasswordVisibility('oldPassword', this)">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="newPassword" class="form-label">
                            <i class="fas fa-lock"></i> 新密码 <span class="text-danger">*</span>
                        </label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="newPassword" required
                                placeholder="请输入新密码（至少6位）">
                            <button class="btn btn-outline-secondary" type="button"
                                onclick="togglePasswordVisibility('newPassword', this)">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="confirmPassword" class="form-label">
                            <i class="fas fa-lock"></i> 确认新密码 <span class="text-danger">*</span>
                        </label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="confirmPassword" required
                                placeholder="请再次输入新密码">
                            <button class="btn btn-outline-secondary" type="button"
                                onclick="togglePasswordVisibility('confirmPassword', this)">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                            <i class="fas fa-save"></i> 修改密码
                        </button>
                        <a href="/" class="btn btn-secondary btn-lg">
                            <i class="fas fa-times"></i> 取消
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="/aicode/static/external/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="/aicode/static/external/jquery-3.6.0.min.js"></script>

    <script>
        // 显示提示消息
        function showAlert(message, type = 'danger') {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${type === 'danger' ? 'exclamation-circle' : 'check-circle'}"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('#alert-container').html(alertHtml);

            // 3秒后自动消失
            setTimeout(() => {
                $('.alert').fadeOut();
            }, 3000);
        }

        // 切换密码可见性
        function togglePasswordVisibility(inputId, button) {
            const input = document.getElementById(inputId);
            const icon = button.querySelector('i');

            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // 处理密码修改表单提交
        $('#changePasswordForm').on('submit', function (e) {
            e.preventDefault();

            const oldPassword = $('#oldPassword').val();
            const newPassword = $('#newPassword').val();
            const confirmPassword = $('#confirmPassword').val();

            // 验证
            if (!oldPassword || !newPassword || !confirmPassword) {
                showAlert('请填写所有必填项', 'warning');
                return;
            }

            if (newPassword.length < 6) {
                showAlert('新密码长度至少6位', 'warning');
                return;
            }

            if (newPassword !== confirmPassword) {
                showAlert('两次输入的新密码不一致', 'warning');
                return;
            }

            const submitBtn = $('#submitBtn');
            const originalText = submitBtn.html();
            submitBtn.html('<i class="fas fa-spinner fa-spin"></i> 修改中...').prop('disabled', true);

            $.ajax({
                url: '/aicode/api/auth/change_password',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    old_password: oldPassword,
                    new_password: newPassword
                }),
                success: function (response) {
                    if (response.success) {
                        showAlert(response.message || '密码修改成功，请重新登录', 'success');
                        setTimeout(() => {
                            window.location.href = '/aicode/login.html';
                        }, 2000);
                    } else {
                        showAlert(response.message || '修改失败', 'danger');
                        submitBtn.html(originalText).prop('disabled', false);
                    }
                },
                error: function (xhr) {
                    const response = xhr.responseJSON || {};
                    showAlert(response.message || '修改失败，请检查原密码是否正确', 'danger');
                    submitBtn.html(originalText).prop('disabled', false);
                }
            });
        });

        // 获取当前用户信息
        $.ajax({
            url: '/aicode/api/auth/current_user',
            method: 'GET',
            success: function (response) {
                if (response.logged_in && response.username) {
                    $('#username').val(response.username);
                }
            }
        });
    </script>
</body>

</html>
