<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表板 - 全栈助手</title>

    <!-- Bootstrap CSS -->
    <link href="/aicode/static/external/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="/aicode/static/external/all.min.css" rel="stylesheet">
    <!-- Modern Theme -->
    <link href="/aicode/static/css/modern-theme.css" rel="stylesheet">
    <style>
        .layout-container {
            display: flex;
        }

        .sidebar {
            width: 200px;
            /* 假设侧边栏宽度为200px */
            background-color: #34495e;
            padding: 20px;
            box-sizing: border-box;
        }

        .main-content {
            flex: 1;
            padding-left: 10px;
            /* 减少左侧空白，可以根据需要调整数值 */
            box-sizing: border-box;
        }

        /* 侧边栏折叠时减少左边距 */
        .sidebar.collapsed+.main-content {
            padding-left: 0.5rem !important;
        }

        /* 滚动标题样式 */
        .index-title-container {
            display: flex;
            align-items: center;
            height: 40px;
            overflow: hidden;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 0 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            min-width: 250px;
            max-width: 400px;
            position: relative;
        }

        .index-title-scroll {
            display: flex;
            align-items: center;
            width: 100%;
            height: 100%;
            position: relative;
        }

        #indexTitleText {
            color: white;
            font-weight: 500;
            font-size: 14px;
            white-space: nowrap;
            
            display: inline-block;
            padding-right: 100%;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        @keyframes scrollText {
            0% {
                transform: translateX(0);
            }
            100% {
                transform: translateX(-100%);
            }
        }

        /* 响应式设计 */
        @media (max-width: 991.98px) {
            .index-title-container {
                min-width: 200px;
                max-width: 300px;
            }

            #indexTitleText {
                font-size: 12px;
            }
        }

        @media (max-width: 767.98px) {
            .index-title-container {
                min-width: 150px;
                max-width: 200px;
                height: 35px;
            }

            #indexTitleText {
                font-size: 11px;
                animation-duration: 3s;
            }
        }

        @media (max-width: 767.98px) {
            .main-content {
                padding-left: 0.5rem !important;
                padding-right: 0.5rem !important;
            }
        }
    </style>
</head>

<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary" id="navbar">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.html">
                <i class="fas fa-tasks"></i> 全栈助手
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- 当前项目选择器移动到左侧 -->
                <div class="navbar-nav">
                    <select class="form-select form-select-sm" id="currentProjectSelector"
                        onchange="switchCurrentProject()" style="min-width: 200px;">
                        <option value="">请选择项目...</option>
                    </select>
                </div>

                <!-- 系统标题滚动显示区域 -->
                <div class="navbar-nav mx-auto flex-grow-1 justify-content-center">
                    <div class="index-title-container">
                        <div class="index-title-scroll" id="indexTitleScroll">
                            <span id="indexTitleText">AI任务管理系统</span>
                        </div>
                    </div>
                </div>

                <!-- 活动菜单显示区域 -->
                <div class="navbar-nav">
                    <span class="navbar-text me-3" id="activeMenuDisplay"></span>
                </div>

                <!-- 用户操作区域 -->
                <div class="navbar-nav">
                    <a class="nav-link" href="#" onclick="showUserProfile()" title="用户信息">
                        <i class="fas fa-user-circle fa-lg"></i>
                    </a>
                    <a class="nav-link" href="#" onclick="logout()" title="退出登录">
                        <i class="fas fa-sign-out-alt fa-lg"></i>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="layout-container">
            <!-- 侧边栏 -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <!-- 收起/展开按钮 -->
                <button id="sidebarToggle" class="sidebar-toggle" onclick="toggleSidebar()" title="收起侧边栏">
                    <i class="fas fa-chevron-left"></i>
                </button>

                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showProjectManagement()">
                                <i class="fas fa-folder-open"></i> <span class="menu-text">项目管理</span>
                            </a>
                        </li>
                        <!-- <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showConfigManagement()">
                                <i class="fas fa-cog"></i> <span class="menu-text">配置管理</span>
                            </a>
                        </li> -->
                    </ul>

                    <!-- 当前项目相关菜单 -->
                    <div id="currentProjectMenu" style="display: none;">
                        <h6
                            class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                            <span class="menu-text">当前项目</span>
                        </h6>
                        <ul class="nav flex-column mb-2">
                            <li class="nav-item">
                                <a class="nav-link" href="#" onclick="showTaskManager()">
                                    <i class="fas fa-tasks"></i> <span class="menu-text">任务管理</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" onclick="showRulesManager()">
                                    <i class="fas fa-shield-alt"></i> <span class="menu-text">规则管理</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" onclick="showRequirementManager()">
                                    <i class="fas fa-file-alt"></i> <span class="menu-text">需求管理</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" onclick="showDesignManager()">
                                    <i class="fas fa-palette"></i> <span class="menu-text">设计管理</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" onclick="showKnowledgeManager()">
                                    <i class="fas fa-brain"></i> <span class="menu-text">知识库管理</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" onclick="showFileManager()">
                                    <i class="fas fa-folder-tree"></i> <span class="menu-text">文件管理</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" onclick="showTerminal()">
                                    <i class="fas fa-terminal"></i> <span class="menu-text">终端管理</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" onclick="showAiChat()">
                                    <i class="fas fa-robot"></i> <span class="menu-text">AiChat</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main id="mainContent" class="col-md-9 ms-sm-auto col-lg-10 main-content">
                <!-- 默认显示仪表板 -->
                <div id="dashboardContent">
                    <div class="pt-3">
                        <div
                            class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                            <h1 class="h2">仪表板</h1>
                            <div class="btn-toolbar mb-2 mb-md-0">
                                <div class="btn-group me-2">
                                    <button type="button" class="btn btn-sm btn-outline-secondary"
                                        onclick="refreshDashboard()">
                                        <i class="fas fa-sync-alt"></i> 刷新
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 统计卡片 -->
                        <div class="row mb-4">
                            <div class="col-xl-3 col-md-6 mb-4">
                                <div class="card border-left-primary shadow h-100 py-2">
                                    <div class="card-body">
                                        <div class="row no-gutters align-items-center">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                    项目总数
                                                </div>
                                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-projects">
                                                    0
                                                </div>
                                            </div>
                                            <div class="col-auto">
                                                <i class="fas fa-folder fa-2x text-gray-300"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-xl-3 col-md-6 mb-4">
                                <div class="card border-left-success shadow h-100 py-2">
                                    <div class="card-body">
                                        <div class="row no-gutters align-items-center">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                    活跃项目
                                                </div>
                                                <div class="h5 mb-0 font-weight-bold text-gray-800"
                                                    id="active-projects">
                                                    0
                                                </div>
                                            </div>
                                            <div class="col-auto">
                                                <i class="fas fa-play fa-2x text-gray-300"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-xl-3 col-md-6 mb-4">
                                <div class="card border-left-info shadow h-100 py-2">
                                    <div class="card-body">
                                        <div class="row no-gutters align-items-center">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                    总任务数
                                                </div>
                                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-tasks">
                                                    0
                                                </div>
                                            </div>
                                            <div class="col-auto">
                                                <i class="fas fa-list fa-2x text-gray-300"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-xl-3 col-md-6 mb-4">
                                <div class="card border-left-warning shadow h-100 py-2">
                                    <div class="card-body">
                                        <div class="row no-gutters align-items-center">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                    运行中任务
                                                </div>
                                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="running-tasks">
                                                    0
                                                </div>
                                            </div>
                                            <div class="col-auto">
                                                <i class="fas fa-cog fa-spin fa-2x text-gray-300"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 最近项目 -->
                        <div class="row">
                            <div class="col-lg-8">
                                <div class="card shadow mb-4">
                                    <div
                                        class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                                        <h6 class="m-0 font-weight-bold text-primary">最近项目</h6>
                                        <a href="#" class="btn btn-sm btn-primary" onclick="showProjectManagement()">查看全部</a>
                                    </div>
                                    <div class="card-body" id="recent-projects-container">
                                        <div class="text-center py-4">
                                            <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                                            <div>加载中...</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-4">
                                <div class="card shadow">
                                    <div class="card-header py-3">
                                        <h6 class="m-0 font-weight-bold text-primary">系统状态</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between">
                                                <span>系统运行状态</span>
                                                <span class="badge bg-success">正常</span>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between">
                                                <span>Claude连接</span>
                                                <span class="badge bg-success">已连接</span>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between">
                                                <span>数据存储</span>
                                                <span class="badge bg-success">正常</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 仪表板内容结束 -->


                </div>
            </main>
        </div>
    </div>

    <!-- 创建项目模态框 -->
    <div class="modal fade" id="createProjectModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建新项目</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createProjectForm">
                        <div class="mb-3">
                            <label for="createProjectName" class="form-label">项目名称 <span
                                    class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="createProjectName" required
                                placeholder="请输入项目名称">
                        </div>
                        <div class="mb-3">
                            <label for="createProjectWorkDir" class="form-label">工作目录 <span
                                    class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="createProjectWorkDir" required
                                placeholder="例如: /tmp/projects/my_project">
                            <div class="form-text">
                                <i class="fas fa-info-circle"></i>
                                项目文件将存储在此目录中
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="createProjectProvider" class="form-label">LLM Provider</label>
                            <select class="form-control" id="createProjectProvider">
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="createProjectTaskType" class="form-label">任务类型</label>
                            <select class="form-control" id="createProjectTaskType">
                                <option value="产品迭代">产品迭代</option>
                                <option value="BUG修复">BUG修复</option>
                                <option value="PMO">PMO</option>
                                <option value="代码分析">代码分析</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="ProjectManager.submitCreate()">创建项目</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="/aicode/static/external/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="/aicode/static/external/jquery-3.6.0.min.js"></script>
    <!-- 自定义JS -->
    <script src="/aicode/static/js/common.js"></script>
    <script src="/aicode/static/js/projects.js"></script>
    <script src="/aicode/static/js/index.js"></script>

    <script>
        // 页面初始化
        $(document).ready(function () {
            loadDashboardData();
            restoreSidebarState();
            // 恢复上次选择的项目
            restoreLastProject();
        });
        
        // 双击导航栏隐藏/显示功能
        let navbarVisible = true;
        document.getElementById('navbar').addEventListener('dblclick', function() {
            const navbar = document.getElementById('navbar');
            if (navbarVisible) {
                navbar.style.display = 'none';
                navbarVisible = false;
            } else {
                navbar.style.display = 'block';
                navbarVisible = true;
            }
        });
    </script>
</body>

</html>