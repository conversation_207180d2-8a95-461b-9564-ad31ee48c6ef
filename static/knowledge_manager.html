<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识库管理 - 全栈助手</title>

    <!-- Bootstrap CSS -->
    <link href="/aicode/static/external/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="/aicode/static/external/all.min.css" rel="stylesheet">
    <!-- Prism.js CSS -->
    <link href="/aicode/static/external/cherry-markdown.css" rel="stylesheet">
    <!-- Modern Theme -->
    <link href="/aicode/static/css/modern-theme.css" rel="stylesheet">
    <link href="/aicode/static/js/common.js" rel="stylesheet">

    <style>
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        #progressMessage {
            display: none;
        }

        .btn-tool {
            background: transparent;
            border: none;
            color: var(--text-secondary);
            transition: var(--transition);
        }

        .btn-tool:hover {
            color: var(--text-primary);
        }

        .search-section {
            background: var(--bg-secondary);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border: 1.5px solid var(--border-color);
            box-shadow: var(--shadow);
        }

        .search-options {
            display: flex;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }

        .search-option {
            cursor: pointer;
            padding: 0.75rem 1.5rem;
            border-radius: var(--border-radius-sm);
            background-color: var(--gray-100);
            transition: var(--transition);
            font-weight: 500;
            box-shadow: var(--shadow-sm);
        }

        .search-option:hover {
            background-color: var(--gray-200);
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .search-option.active {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            color: white;
            box-shadow: var(--shadow);
        }

        .stats-card {
            margin-bottom: 1.5rem;
        }

        .stats-card .card {
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            transition: var(--transition);
            height: 100%;
        }

        .stats-card .card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .stats-card .card-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .stats-card .card-text {
            font-size: 0.85rem;
            opacity: 0.9;
        }

        .content-card {
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            margin-bottom: 1.5rem;
            background-color: var(--bg-primary);
        }

        .content-card .card-header {
            background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
            border-bottom: 2px solid var(--border-color);
            padding: 1rem 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .content-card .card-title {
            font-weight: 600;
            margin-bottom: 0;
        }

        .table-responsive {
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
        }

        .table th {
            background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
            border-bottom: 2px solid var(--border-color);
            color: var(--text-primary);
            font-weight: 600;
            padding: 1rem;
            text-transform: uppercase;
            font-size: 0.75rem;
            letter-spacing: 0.05em;
        }

        .table td {
            padding: 1rem;
            vertical-align: middle;
            border-bottom: 1px solid var(--border-color);
        }

        .table tbody tr {
            transition: var(--transition);
        }

        .table tbody tr:hover {
            background-color: var(--gray-50);
        }

        .page-header {
            margin-bottom: 1.5rem;
        }

        .page-header h1 {
            font-weight: 700;
            color: var(--text-primary);
            letter-spacing: -0.025em;
        }

        .action-buttons .btn {
            margin-left: 0.5rem;
            box-shadow: var(--shadow-sm);
        }

        .action-buttons .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        /* 优化统计卡片区域 */
        .stats-summary {
            background: var(--bg-secondary);
            border-radius: var(--border-radius);
            padding: 0.75rem;
            margin-bottom: 1.5rem;
            border: 1.5px solid var(--border-color);
            box-shadow: var(--shadow);
        }

        .stats-item {
            text-align: center;
            padding: 0.5rem;
        }

        .stats-value {
            font-size: 1.5rem;
            font-weight: 700;
            display: block;
        }

        .stats-label {
            font-size: 0.85rem;
            color: var(--text-secondary);
            margin-top: 0.25rem;
        }

        .stats-primary .stats-value {
            color: var(--primary-color);
        }

        .stats-success .stats-value {
            color: var(--success-color);
        }

        .stats-info .stats-value {
            color: var(--info-color);
        }

        .stats-warning .stats-value {
            color: var(--warning-color);
        }

        /* 搜索区域一行显示样式 */
        .search-controls {
            display: flex;
            gap: 0.75rem;
            align-items: center;
        }

        .search-type-select {
            min-width: 150px;
            flex-shrink: 0;
        }

        .search-input-group {
            flex: 1;
        }

        /* 代码高亮样式 */
        pre[class*="language-"] {
            border-radius: var(--border-radius-sm);
            margin: 0.5rem 0;
        }
        
        code[class*="language-"] {
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            font-size: 0.85rem;
            line-height: 1.4;
        }
        
        pre[class*="language-"] {
            padding: 1rem;
            overflow: auto;
            background-color: var(--gray-50);
            border: 1px solid var(--border-color);
        }
        
        .search-result-code {
            background-color: var(--gray-50);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-sm);
            padding: 0.75rem;
            margin-bottom: 0.5rem;
        }
        
        /* 暗色主题适配 */
        [data-theme="dark"] pre[class*="language-"],
        [data-theme="dark"] .search-result-code {
            background-color: var(--gray-800);
            border-color: var(--gray-700);
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 10px;
            }
            
            .stats-item {
                padding: 0.5rem;
            }
            
            .stats-value {
                font-size: 1.25rem;
            }
            
            .action-buttons .btn {
                margin: 0.25rem 0.25rem;
            }
            
            .search-controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-type-select {
                min-width: auto;
            }
        }
    </style>
</head>

<body>
    <div class="main-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                <h1 class="h3 mb-3 mb-md-0">
                    <i class="fas fa-database me-2"></i>知识库管理
                </h1>
                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="showUploadModal()" id="uploadBtn" disabled>
                        <i class="fas fa-upload me-1"></i>上传文件
                    </button>
                    <button class="btn btn-info" onclick="rebuildCodeKnowledgeBase()" id="rebuildCodeBtn" disabled>
                        <i class="fas fa-sync me-1"></i>重建代码库
                    </button>
                    <button class="btn btn-danger" onclick="clearKnowledge()" id="clearBtn" disabled>
                        <i class="fas fa-trash me-1"></i>清空知识库
                    </button>
                    <button class="btn btn-success" onclick="loadKnowledgeBase()" id="refreshBtn">
                        <i class="fas fa-sync-alt me-1"></i>刷 新
                    </button>
                </div>
            </div>
        </div>

        <!-- 知识库内容 -->
        <div id="knowledgeSection" style="display: none;">
            <!-- 统计信息 -->
            <div class="stats-summary">
                <div class="row">
                    <div class="col-6 col-md-3 mb-2 mb-md-0">
                        <div class="stats-item stats-primary">
                            <span class="stats-value" id="totalDocuments">0</span>
                            <span class="stats-label">文档数量</span>
                        </div>
                    </div>
                    <div class="col-6 col-md-3 mb-2 mb-md-0">
                        <div class="stats-item stats-success">
                            <span class="stats-value" id="totalChunks">0</span>
                            <span class="stats-label">文档知识块</span>
                        </div>
                    </div>
                    <div class="col-6 col-md-3 mb-2 mb-md-0">
                        <div class="stats-item stats-info">
                            <span class="stats-value" id="totalCodeFiles">0</span>
                            <span class="stats-label">代码文件</span>
                        </div>
                    </div>
                    <div class="col-6 col-md-3 mb-2 mb-md-0">
                        <div class="stats-item stats-warning">
                            <span class="stats-value" id="totalCodeChunks">0</span>
                            <span class="stats-label">代码知识块</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 搜索区域 -->
            <div class="search-section">
                <div class="search-controls">
                    <div class="search-type-select">
                        <select class="form-select" id="searchType" onchange="changeSearchType()">
                            <option value="documents" selected>文档知识库</option>
                            <option value="code">代码知识库</option>
                        </select>
                    </div>
                    <div class="search-input-group input-group">
                        <input type="text" class="form-control" id="searchQuery" placeholder="输入搜索关键词...">
                        <button class="btn btn-outline-primary" onclick="searchKnowledge()">
                            <i class="fas fa-search me-1"></i>搜索
                        </button>
                    </div>
                </div>
            </div>

            <!-- 搜索结果 -->
            <div class="content-card mb-4" id="searchResults" style="display: none;">
                <div class="card-header">
                    <h6 class="card-title mb-0">搜索结果</h6>
                    <div class="card-tools">
                        <button class="btn btn-tool btn-sm" onclick="toggleSearchResults()">
                            <i class="fas fa-chevron-up" id="toggleIcon"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body" id="searchResultsBody">
                    <div id="searchResultsContent">
                        <!-- 搜索结果将在这里显示 -->
                    </div>
                </div>
            </div>

            <!-- 知识库文档列表 -->
            <div class="content-card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>知识库文档列表
                    </h6>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>文档名称</th>
                                    <th>文件类型</th>
                                    <th>知识块数量</th>
                                    <th>描述</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="docs-table-body">
                                <!-- 文档列表将通过JavaScript动态填充 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 上传文件模态框 -->
    <div class="modal fade" id="uploadModal" tabindex="-1" aria-labelledby="uploadModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="uploadModalLabel">上传知识库文件</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <form id="uploadForm" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="knowledgeFile" class="form-label">选择文件 <span
                                    class="text-danger">*</span></label>
                            <input type="file" class="form-control" id="knowledgeFile" accept=".pdf,.docx,.doc,.md,.txt,.json"
                                required>
                            <div class="form-text">支持格式: PDF, Word, Markdown, 文本文件, JSON<br>
                        </div>
                        <div class="mb-3">
                            <label for="knowledgeName" class="form-label">文件名称</label>
                            <input type="text" class="form-control" id="knowledgeName" placeholder="留空则使用文件名">
                        </div>
                        <div class="mb-3">
                            <label for="knowledgeType" class="form-label">文件类型</label>
                            <select class="form-select" id="knowledgeType">
                                <option value="knowledge">知识文档</option>
                                <option value="config">配置文件</option>
                                <option value="script">脚本文件</option>
                                <option value="manual">用户手册</option>
                                <option value="test">测试用例</option>
                                <option value="json_data">JSON数据</option>
                            </select>
                        </div>
                        <div class="mb-3" id="jsonProcessingOptions" style="display: none;">
                            <!-- <div class="mb-3 mt-3" id="jsonChunkLevelOptions">
                                <label for="jsonChunkLevel" class="form-label">分块起始层级</label>
                                <select class="form-select" id="jsonChunkLevel">
                                    <option value="1" selected>第1层级（根节点）</option>
                                    <option value="2">第2层级</option>
                                    <option value="3">第3层级</option>
                                    <option value="4">第4层级</option>
                                    <option value="auto">自动选择（推荐）</option>
                                </select>
                                <div class="form-text text-muted">
                                    选择从哪个层级开始分块。如果指定层级不存在，则使用上一层级。
                                </div>
                            </div> -->

                            <div class="mb-3">
                                <label for="jsonChunkThreshold" class="form-label">分块阈值</label>
                                <select class="form-select" id="jsonChunkThreshold">
                                    <option value="500">500 字节</option>
                                    <option value="1000" selected>1000 字节（推荐）</option>
                                    <option value="4000">4000 字节</option>
                                    <option value="custom">自定义</option>
                                </select>
                                <div class="form-text text-muted">
                                    超过此阈值的内容将使用滑动窗口分块。
                                </div>
                            </div>

                            <div class="mb-3" id="jsonCustomThresholdOptions" style="display: none;">
                                <label for="jsonCustomThreshold" class="form-label">自定义阈值（字节）</label>
                                <input type="number" class="form-control" id="jsonCustomThreshold" min="500" max="4000" value="1000">
                                <div class="form-text text-muted">
                                    输入500-4000之间的数值。
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="uploadButton"
                        onclick="uploadKnowledgeFile()">上传</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="/aicode/static/external/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="/aicode/static/external/jquery-3.6.0.min.js"></script>
    <!-- Prism.js 代码高亮 -->
    <script src="/aicode/static/external/cherry-markdown.js"></script>
    <!-- 自定义JS -->
    <script src="/aicode/static/js/common.js"></script>
    <script src="/aicode/static/js/knowledge.js"></script>
    
    <script>
        // 新增函数：处理搜索类型切换
        function changeSearchType() {
            const searchType = document.getElementById('searchType').value;
            // 这里可以添加额外的逻辑来处理搜索类型切换
            // 例如，可以触发一次自动搜索或清除当前搜索结果
        }
    </script>
</body>

</html>