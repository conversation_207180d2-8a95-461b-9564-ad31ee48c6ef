<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 全栈助手</title>

    <!-- Bootstrap CSS -->
    <link href="/aicode/static/external/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="/aicode/static/external/all.min.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }

        .login-container {
            width: 100%;
            max-width: 400px;
            padding: 15px;
        }

        .login-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .login-header h3 {
            margin: 0;
            font-weight: 600;
        }

        .login-header p {
            margin: 10px 0 0;
            opacity: 0.9;
            font-size: 14px;
        }

        .login-body {
            padding: 40px 30px;
        }

        .form-label {
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
        }

        .form-control {
            border-radius: 8px;
            padding: 12px 15px;
            border: 1px solid #ddd;
            transition: all 0.3s;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .input-group-text {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 8px 0 0 8px;
            padding: 12px 15px;
        }

        .input-group .form-control {
            border-radius: 0 8px 8px 0;
        }

        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 12px;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .btn-login:active {
            transform: translateY(0);
        }

        .alert {
            border-radius: 8px;
            border: none;
        }

        .login-footer {
            text-align: center;
            padding: 20px;
            color: white;
            font-size: 14px;
        }

        .login-footer a {
            color: white;
            text-decoration: underline;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-container {
            animation: fadeIn 0.5s ease-out;
        }
    </style>
</head>

<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <h3><i class="fas fa-robot"></i> 全栈助手</h3>
                <p>AI驱动的项目管理系统</p>
            </div>
            <div class="login-body">
                <div id="alert-container"></div>

                <form id="loginForm">
                    <div class="mb-3">
                        <label for="username" class="form-label">
                            <i class="fas fa-user"></i> 用户名
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-user"></i>
                            </span>
                            <input type="text" class="form-control" id="username" name="username"
                                placeholder="请输入用户名" required autofocus>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock"></i> 密码
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="password" class="form-control" id="password" name="password"
                                placeholder="请输入密码" required>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-login" id="loginBtn">
                        <i class="fas fa-sign-in-alt"></i> 登录
                    </button>
                </form>
            </div>
        </div>

        <div class="login-footer">
            <p>&copy; 2025 全栈助手. All rights reserved.</p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="/aicode/static/external/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="/aicode/static/external/jquery-3.6.0.min.js"></script>

    <script>
        // 显示提示消息
        function showAlert(message, type = 'danger') {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${type === 'danger' ? 'exclamation-circle' : 'check-circle'}"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('#alert-container').html(alertHtml);

            // 3秒后自动消失
            setTimeout(() => {
                $('.alert').fadeOut();
            }, 3000);
        }

        // 处理登录表单提交
        $('#loginForm').on('submit', function (e) {
            e.preventDefault();

            const username = $('#username').val().trim();
            const password = $('#password').val();

            if (!username || !password) {
                showAlert('请输入用户名和密码', 'warning');
                return;
            }

            const loginBtn = $('#loginBtn');
            const originalText = loginBtn.html();
            loginBtn.html('<i class="fas fa-spinner fa-spin"></i> 登录中...').prop('disabled', true);

            $.ajax({
                url: '/aicode/api/auth/login',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    username: username,
                    password: password
                }),
                success: function (response) {
                    if (response.success) {
                        showAlert('登录成功，正在跳转...', 'success');
                        setTimeout(() => {
                            window.location.href = '/aicode/';
                        }, 1000);
                    } else {
                        showAlert(response.message || '登录失败', 'danger');
                        loginBtn.html(originalText).prop('disabled', false);
                    }
                },
                error: function (xhr) {
                    const response = xhr.responseJSON || {};
                    showAlert(response.message || '登录失败，请检查用户名和密码', 'danger');
                    loginBtn.html(originalText).prop('disabled', false);
                }
            });
        });

        // 回车键登录
        $(document).on('keypress', function (e) {
            if (e.which === 13) {
                $('#loginForm').submit();
            }
        });
    </script>
</body>

</html>
