#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基础功能测试脚本
测试AI智能测试平台的核心功能
"""

import sys
import os
import json
from datetime import datetime
# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))
from task_manager import TaskManager

def test_gen_tasks(task_manager:TaskManager, prd, num_tasks=None):
    print("\n" + "=" * 50)
    print("生成任务")
    print("=" * 50)
    
    try:
        def update_progress(progress: int, title:str, message: str=''):
            print(f"进度: {progress}% {title}:{message}")                    
        
        knowledge_base = None
        result = task_manager.gen_tasks(prd, num_tasks, knowledge_base, update_progress)
        if result["success"]:
            print(f"✓ 测试用例生成结果: {result}")
        else:
            print(f"✗ 测试用例失败: {result}")

    except Exception as e:
        print(f"✗ 测试用例失败: {e}")
        import traceback
        traceback.print_exc()
        return False
def test_run_tasks(task_manager:TaskManager):
    print("\n" + "=" * 50)
    print("运行任务")
    print("=" * 50)
    
    try:
        def update_progress(progress: int, title:str, message: str=''):
            print(f"进度: {progress}% {title}:{message}")
        result = task_manager.auto_run_tasks(False, update_progress)
        if result["success"]:
            print(f"✓ 测试用例生成结果: {result}")
        else:
            print(f"✗ 测试用例失败: {result}")
    
    except Exception as e:
        print(f"✗ 测试用例失败: {e}")
        import traceback
        traceback.print_exc()
        return False
def test_claude_context():
    from claude.claude_agent import ClaudeAgent
    try:
        def log_and_callback(progress: int, title: str, content: str = ""):
            print(f"{progress}% - {title}: {content}")
        agent = ClaudeAgent("", "local")
        agent.run_agent_sync("/opt/data/projects/demo", "分析项目", "1c27f9a0-839b-40ec-a97d-8b49dbbb012a","",log_and_callback)
        
    except Exception as e:
        print(f"✗ 测试用例失败: {e}")
        import traceback
        traceback.print_exc()
        return False
def main():
    """主测试函数"""
    test_results = []
    demo_prd = "使用html生成一个俄罗斯方块游戏"
    csdk_prd = """
        """
        
    #task_manager = TaskManager("demo", "/mnt/d/agent/auto-claude-tasks/demo")
    #task_manager = TaskManager("/mnt/d/aicode/csdk")
    #test_gen_tasks(task_manager,demo_prd,3)
    # test_run_tasks(task_manager)
    test_claude_context()
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
