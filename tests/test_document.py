#!/usr/bin/env python3
"""
代码知识库测试用例
测试代码知识库的构建、搜索功能
"""

import os
import sys
import json
import tempfile
import shutil
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from project_manager import Project, ProjectManager
from knowledge_manager import KnowledgeManager
from log_manager import TaskLogManager


class TestKnowledgeCode:
    """文档AI测试类"""

    def __init__(self):
        self.test_data_dir = "/mnt/d/agent/auto-claude-tasks/data"
        self.project_manager = None
        self.project = None
        self.knowledge_manager = None
        self.log_manager = None
        self.setup()

    def setup(self):
        """设置测试环境"""
        print("🔧 设置测试环境...")

        try:
            self.project_manager = ProjectManager(self.test_data_dir)
            #  "name": "csdk自动化测试接口重构", "work_dir": "/mnt/d/aicode/csdk",
            return True
        except Exception as e:
            print(f"❌ 测试环境初始化失败: {e}")
            return False

    def teardown(self):
        """清理测试环境"""
        print("🧹 清理测试环境...")

    def test_document_optimize(self):
        """测试文档片段优化功能"""
        print("\n🏗️ 测试1: 文档片段优化")
        try:
            project = self.project_manager.get_project("1760409013513")
            # 检查测试目录是否存在
            if not project:
                print(f"❌ 测试项目不存在")
                return False
            knowledge_manager = self.project_manager.get_knowledge_manager(
                project.project_id
            )
            document_manager = self.project_manager.get_document_manager(
                project.project_id
            )
            ai_actions = ["润色", "总结", "扩写"]
            for ai_action in ai_actions:
                data = {
                    "doc_type": "requirement",
                    "selection": "（1）外部验签运算externalEccVerifySign接口，使用公钥验签，新开发软算法接口，支持软算法",
                    "ai_action": ai_action,
                    "enable_kb": True,
                }
                knowledge_manager = None
                if data.get("enable_kb"):
                    knowledge_manager = self.knowledge_manager

                # 记录执行时间
                start_time = datetime.now()
                result = document_manager.optimize_document(data, knowledge_manager)
                print(f"*******{ai_action}耗时: {datetime.now() - start_time}")

                if result.get("success"):
                    print(
                        f"✅ 文档片段{ai_action}成功：{result.get('message', '无消息')}"
                    )
                else:
                    print(
                        f"❌ 文档片段{ai_action}失败: {result.get('error', '未知错误')}"
                    )

        except Exception as e:
            print(f"❌ 文档片段测试失败: {e}")

    def test_require_template(self):
        print("\n🏗️ 测试2: 需求文档模板功能")
        try:

            project = self.project_manager.get_project("fd23027e-aaf1-4d2b-ac63-ce93c0d5a354")
            # 检查测试目录是否存在
            if not project:
                print(f"❌ 测试项目不存在")
                return False
            knowledge_manager = self.project_manager.get_knowledge_manager(
                project.project_id
            )
            document_manager = self.project_manager.get_document_manager(
                project.project_id
            )

            # 记录执行时间
            start_time = datetime.now()
            requirement = project.load_requirement_from_file()
            requirement = "# 需求\n使用纯HTML技术实现俄罗斯方块游戏"
            result = document_manager.optimize_requirement(requirement, knowledge_manager)
            print(f"*******耗时: {datetime.now() - start_time}")

            if result.get("success"):
                print(f"✅ 需求规格生成成功：{result.get('message', '无消息')}")
                project.requirement = result.get("message")
                project.save_requirement_to_file()
            else:
                print(f"❌ 需求规格生成失败: {result.get('error', '未知错误')}")

        except Exception as e:
            print(f"❌ 需求规格测试失败: {e}")


def main():
    """主函数"""
    tester = TestKnowledgeCode()
    tester.test_require_template()


if __name__ == "__main__":
    exit(main())
