#!/usr/bin/env python3
"""
代码知识库测试用例
测试代码知识库的构建、搜索功能
"""

import os
import sys
import json
import tempfile
import shutil
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from project_manager import Project
from knowledge_manager import KnowledgeManager
from log_manager import TaskLogManager


class TestKnowledgeCode:
    """代码知识库测试类"""
    
    def __init__(self):
        self.test_project_dir = "/mnt/d/aicode/csdk/"
        self.temp_dir = None
        self.project = None
        self.knowledge_manager = None
        self.log_manager = None
    
    def setup(self):
        """设置测试环境"""
        print("🔧 设置测试环境...")
        
        # 检查测试目录是否存在
        if not os.path.exists(self.test_project_dir):
            print(f"❌ 测试目录不存在: {self.test_project_dir}")
            print("请确保测试目录存在，或修改test_project_dir变量")
            return False
        
        # 创建临时工作目录
        self.temp_dir = tempfile.mkdtemp(prefix="test_knowledge_code_")
        print(f"📁 创建临时工作目录: {self.temp_dir}")
        
        # 创建测试项目
        self.project = Project(
            project_id="test_code_kb",
            name="代码知识库测试项目",
            work_dir=self.test_project_dir,  # 使用指定的测试目录
            description="用于测试代码知识库功能的项目",
            provider="local",
            exclude_patterns="tests/,thirdparty/,debug/",
            include_patterns="autotest/src/func.c"
        )
        
        # 创建日志管理器
        self.log_manager = TaskLogManager(self.project.work_dir)
        
        # 创建知识库管理器
        try:
            self.knowledge_manager = KnowledgeManager(self.project, self.log_manager)
            print("✅ 知识库管理器初始化成功")
            return True
        except Exception as e:
            print(f"❌ 知识库管理器初始化失败: {e}")
            return False
    
    def teardown(self):
        """清理测试环境"""
        print("🧹 清理测试环境...")
        
        # 清理临时目录
        if self.temp_dir and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
            print(f"🗑️ 删除临时目录: {self.temp_dir}")
    
    def test_code_scanning(self):
        """测试代码文件扫描功能"""
        print("\n📋 测试1: 代码文件扫描")
        
        if not self.knowledge_manager or not self.knowledge_manager.knowledge_code:
            print("❌ 代码知识库管理器未初始化")
            return False
        
        try:
            # 扫描代码文件
            code_files = self.knowledge_manager.knowledge_code._scan_code_files()
            print(f"📁 发现 {len(code_files)} 个代码文件")
            
            # 显示前10个文件
            for i, file_path in enumerate(code_files[:30]):
                rel_path = os.path.relpath(file_path, self.test_project_dir)
                print(f"  {i+1}. {rel_path}")
            
            if len(code_files) > 30:
                print(f"  ... 还有 {len(code_files) - 30} 个文件")
            
            return len(code_files) > 0
        except Exception as e:
            print(f"❌ 代码扫描失败: {e}")
            return False
    
    def test_code_parsing(self):
        """测试代码解析功能"""
        print("\n🔍 测试2: 代码解析")
        
        if not self.knowledge_manager or not self.knowledge_manager.knowledge_code:
            print("❌ 代码知识库管理器未初始化")
            return False
        
        try:
            # 获取一些代码文件进行测试
            code_files = self.knowledge_manager.knowledge_code._scan_code_files()
            if not code_files:
                print("❌ 没有找到代码文件")
                return False
            
            # 测试解析前几个文件
            test_files = code_files[:3]  # 只测试前3个文件
            total_chunks = 0
            
            for file_path in test_files:
                rel_path = os.path.relpath(file_path, self.test_project_dir)
                print(f"📄 解析文件: {rel_path}")
                
                try:
                    chunks = self.knowledge_manager.knowledge_code._process_file(file_path)
                    print(f"  ✅ 生成 {len(chunks)} 个代码块")
                    total_chunks += len(chunks)
                    
                    # 显示前几个块的信息
                    for i, chunk in enumerate(chunks[:3]):
                        print(f"    块{i+1}: {chunk.chunk_type} - {chunk.function_name or 'N/A'} ({chunk.start_line}-{chunk.end_line}行)")
                    
                    if len(chunks) > 3:
                        print(f"    ... 还有 {len(chunks) - 3} 个块")
                        
                except Exception as e:
                    print(f"  ❌ 解析失败: {e}")
            
            print(f"📊 总计生成 {total_chunks} 个代码块")
            return total_chunks > 0
            
        except Exception as e:
            print(f"❌ 代码解析测试失败: {e}")
            return False
    
    def test_knowledge_base_build(self):
        """测试知识库构建功能"""
        print("\n🏗️ 测试3: 知识库构建")
        
        if not self.knowledge_manager:
            print("❌ 知识库管理器未初始化")
            return False
        
        try:
            # 构建代码知识库
            print("开始构建代码知识库...")
            result = self.knowledge_manager.build_code_knowledge_base()
            
            if result.get("success"):
                print("✅ 代码知识库构建成功")
                print(f"📊 处理文件数: {result.get('processed_files', 0)}")
                print(f"📊 生成代码块数: {result.get('total_chunks', 0)}")
                print(f"📊 总文件数: {result.get('total_files', 0)}")
                return True
            else:
                print(f"❌ 代码知识库构建失败: {result.get('error', '未知错误')}")
                return False
                
        except Exception as e:
            print(f"❌ 知识库构建测试失败: {e}")
            return False
    
    def test_knowledge_base_search(self):
        """测试知识库搜索功能"""
        print("\n🔍 测试4: 知识库搜索")
        
        if not self.knowledge_manager:
            print("❌ 知识库管理器未初始化")
            return False
        
        try:
            # 测试搜索查询
            test_queries = [
                "函数定义",
                "class",
                "main",
                "初始化",
                "配置"
            ]
            
            for query in test_queries:
                print(f"🔍 搜索: '{query}'")
                results = self.knowledge_manager.search_code(query, limit=5)
                
                if results:
                    print(f"  ✅ 找到 {len(results)} 个结果")
                    for i, result in enumerate(results[:3]):
                        title = result.get("title", "N/A")
                        score = result.get("score", 0)
                        metadata = result.get("metadata", {})
                        file_path = metadata.get("file_path", "N/A")
                        function_name = metadata.get("function_name", "N/A")
                        print(f"    {i+1}. {title} (相似度: {score:.3f})")
                        print(f"       文件: {file_path}, 函数: {function_name}")
                else:
                    print(f"  ❌ 没有找到结果")
                    return False
                print()
            
            return True
            
        except Exception as e:
            print(f"❌ 知识库搜索测试失败: {e}")
            return False
    
    def test_knowledge_base_rebuild(self):
        """测试知识库重建功能"""
        print("\n🔄 测试5: 知识库重建")
        
        if not self.knowledge_manager:
            print("❌ 知识库管理器未初始化")
            return False
        
        try:
            # 重建代码知识库
            print("开始重建代码知识库...")
            result = self.knowledge_manager.rebuild_code_knowledge_base()
            
            if result.get("success"):
                print("✅ 代码知识库重建成功")
                print(f"📊 处理文件数: {result.get('processed_files', 0)}")
                print(f"📊 生成代码块数: {result.get('total_chunks', 0)}")
                return True
            else:
                print(f"❌ 代码知识库重建失败: {result.get('error', '未知错误')}")
                return False
                
        except Exception as e:
            print(f"❌ 知识库重建测试失败: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始代码知识库功能测试")
        print(f"📁 测试目录: {self.test_project_dir}")
        print("=" * 60)
        
        # 设置测试环境
        if not self.setup():
            print("❌ 测试环境设置失败")
            return False
        
        try:
            # 运行测试
            tests = [
                ("代码文件扫描", self.test_code_scanning),
                #("代码解析", self.test_code_parsing),
                ("知识库构建", self.test_knowledge_base_build),
                ("知识库搜索", self.test_knowledge_base_search),
                #("知识库重建", self.test_knowledge_base_rebuild),
            ]
            
            passed = 0
            total = len(tests)
            
            for test_name, test_func in tests:
                try:
                    if test_func():
                        passed += 1
                        print(f"✅ {test_name} - 通过")
                    else:
                        print(f"❌ {test_name} - 失败")
                except Exception as e:
                    print(f"❌ {test_name} - 异常: {e}")
            
            print("\n" + "=" * 60)
            print(f"📊 测试结果: {passed}/{total} 通过")
            
            if passed == total:
                print("🎉 所有测试通过！")
                return True
            else:
                print("⚠️ 部分测试失败")
                return False
                
        finally:
            # 清理测试环境
            self.teardown()


def main():
    """主函数"""
    tester = TestKnowledgeCode()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ 代码知识库功能测试完成，所有功能正常")
        return 0
    else:
        print("\n❌ 代码知识库功能测试失败，请检查错误信息")
        return 1


if __name__ == "__main__":
    exit(main())
