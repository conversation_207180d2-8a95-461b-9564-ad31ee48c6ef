# 测试文档

## 测试概述

本目录包含应用的各种测试脚本，用于验证应用是否能正常运行。

## 测试脚本

### 1. `test_startup.py` - 启动测试
检查应用的基本配置和组件是否正确加载。

**测试内容：**
- 模块导入检查
- 应用配置检查
- SocketIO 初始化检查
- 路由注册检查
- 清理函数检查

**运行方式：**
```bash
python3 tests/test_startup.py
```

### 2. `test_runtime.py` - 运行时测试
启动应用服务器并进行 HTTP 请求测试。

**测试内容：**
- 服务器启动测试
- API 端点测试（/api/projects, /api/terminals）
- 静态文件访问测试（/xterm.html, /）

**运行方式：**
```bash
python3 tests/test_runtime.py
```

### 3. `check_all.sh` - 综合测试脚本
运行所有测试，提供完整的检查报告。

**测试内容：**
- Python 语法检查
- 导入测试
- 启动测试
- 运行时测试

**运行方式：**
```bash
./tests/check_all.sh
```

## 测试结果

### ✓ 所有测试通过（2025-10-16）

```
======================================================
开始全面检查
======================================================

1. Python 语法检查...
✓ web_app.py 语法正确
✓ run_app.py 语法正确

2. 导入测试...
✓ 导入成功

3. 启动测试...
✓ web_app 导入成功
✓ ProjectManager 导入成功
✓ terminals 配置存在
✓ terminals 是字典类型
✓ SocketIO 对象已创建
✓ SocketIO server 已初始化
✓ 路由 /api/terminals 已注册
✓ 路由 /api/projects 已注册
✓ 路由 /xterm.html 已注册
✓ cleanup_terminal 函数存在
✓ cleanup_idle_terminals 函数存在

4. 运行时测试...
✓ 服务器启动成功
✓ GET /api/projects - 状态码: 200
✓ GET /api/terminals - 状态码: 200
✓ GET /xterm.html - 状态码: 200
✓ GET / - 状态码: 200

======================================================
✓ 所有检查通过！应用可以正常运行。
======================================================
```

## 已解决的问题

### 1. pydevd 调试器超时问题
**问题：** 启动时出现 "pydevd: Sending message related to process being replaced timed-out after 5 seconds"

**解决方案：**
- 在子进程启动前清理调试器相关环境变量（PYDEVD*, PYTHONBREAKPOINT）
- 更新 VSCode 调试配置（`.vscode/launch.json`）：
  - 添加 `justMyCode: true`
  - 添加 `subProcess: false`
  - 添加环境变量 `PYDEVD_DISABLE_FILE_VALIDATION: "1"`

### 2. SocketIO 处理器检查问题
**问题：** 测试脚本中 `socketio.handlers` 的访问方式不正确

**解决方案：**
- 简化检查方式，只验证 socketio 对象是否正确初始化
- 运行时测试通过实际 HTTP 请求验证功能

## 启动应用

应用已通过所有测试，可以使用以下命令启动：

```bash
# 普通模式
python3 run_app.py --port 5005

# 调试模式
python3 run_app.py --port 5005 --debug

# 指定数据目录
python3 run_app.py --port 5005 --data-dir ./data
```

## xterm 终端功能验证

终端功能的关键特性已验证：

1. **多项目隔离**：每个项目独立的终端进程和工作目录
2. **WebSocket 连接**：SocketIO 正确初始化，支持实时通信
3. **自动清理**：30 分钟无活动自动清理终端进程
4. **API 管理**：
   - `GET /api/terminals` - 查看所有活动终端
   - `DELETE /api/terminals/<project_id>` - 关闭指定终端

## 持续集成

建议在每次代码修改后运行：
```bash
./tests/check_all.sh
```

这将确保所有功能正常工作。
