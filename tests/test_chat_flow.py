#!/usr/bin/env python3
"""
测试任务聊天流程
验证前端 -> API -> TaskManager -> ClaudeAgent -> progress_callback -> Socket.IO 的完整链路
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

import time
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_progress_callback():
    """测试进度回调机制"""
    print("\n" + "="*60)
    print("测试1: 验证 progress_callback 机制")
    print("="*60)

    callback_calls = []

    def test_callback(progress: int, title: str, content: str = ""):
        callback_calls.append({
            'progress': progress,
            'title': title,
            'content': content
        })
        print(f"✓ 回调被调用: progress={progress}, title={title}, content_len={len(content)}")

    # 模拟调用
    test_callback(10, "测试开始", "这是测试内容")
    test_callback(50, "处理中", "正在处理数据")
    test_callback(100, "完成", "处理完成")

    if len(callback_calls) == 3:
        print("✓ 测试1通过: progress_callback 正常工作")
        return True
    else:
        print("✗ 测试1失败: progress_callback 调用次数不正确")
        return False

def test_create_log_callback():
    """测试 create_log_callback 函数"""
    print("\n" + "="*60)
    print("测试2: 验证 create_log_callback 函数")
    print("="*60)

    try:
        from utils import create_log_callback

        received_calls = []

        def mock_progress_callback(progress: int, title: str, content: str = ""):
            received_calls.append((progress, title, content))

        # 创建日志回调
        log_callback = create_log_callback("test_project", 1, mock_progress_callback)

        # 调用日志回调
        log_callback(10, "测试标题", "测试内容")
        log_callback(50, "处理中", "正在处理")

        if len(received_calls) == 2:
            print(f"✓ 测试2通过: create_log_callback 正常工作，收到 {len(received_calls)} 次回调")
            return True
        else:
            print(f"✗ 测试2失败: 期望收到2次回调，实际收到 {len(received_calls)} 次")
            return False

    except Exception as e:
        print(f"✗ 测试2失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_socket_io_emit():
    """测试 Socket.IO emit 机制"""
    print("\n" + "="*60)
    print("测试3: 验证 Socket.IO emit 机制")
    print("="*60)

    try:
        from web_app import socketio, app

        emit_calls = []

        # 保存原始的 emit 方法
        original_emit = socketio.emit

        # Mock emit 方法
        def mock_emit(event, data, **kwargs):
            emit_calls.append({
                'event': event,
                'data': data,
                'kwargs': kwargs
            })
            print(f"✓ socketio.emit 被调用: event={event}, room={kwargs.get('room', 'broadcast')}")

        socketio.emit = mock_emit

        # 模拟发送事件
        socketio.emit('task_chat_progress', {
            'project_id': 'test',
            'task_id': '1',
            'progress': 50,
            'title': '测试'
        }, room='project_test')

        # 恢复原始方法
        socketio.emit = original_emit

        if len(emit_calls) == 1:
            print("✓ 测试3通过: Socket.IO emit 正常工作")
            return True
        else:
            print("✗ 测试3失败: emit 调用次数不正确")
            return False

    except Exception as e:
        print(f"✗ 测试3失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def print_integration_guide():
    """打印集成测试指南"""
    print("\n" + "="*60)
    print("集成测试指南")
    print("="*60)
    print("""
要测试完整的聊天流程，请按以下步骤操作：

1. 启动应用：
   python3 run_app.py

2. 打开浏览器访问任务聊天页面：
   http://localhost:5005/aicode/static/task_chat.html?project_id=<项目ID>&task_id=<任务ID>

3. 打开浏览器开发者工具（F12），查看：
   - Console 标签：查看前端日志
   - Network 标签：查看 API 请求

4. 在聊天输入框输入消息并发送

5. 观察日志输出：

   【浏览器控制台应显示】：
   - 发送聊天请求到: /aicode/api/projects/.../chat
   - Socket.IO 已连接: true
   - 聊天请求成功，收到响应
   - Socket.IO 已连接，等待 Socket.IO 事件
   - 收到进度更新事件: {...}
   - 收到聊天完成事件: {...}

   【后端终端应显示】：
   - 收到聊天请求: project_id=..., task_id=...
   - 聊天请求数据: {'message': '...'}
   - 处理聊天消息: ...
   - 开始调用 task_manager.run_chat...
   - [回调] 调用 progress_callback: progress=..., title=...
   - [Socket.IO] 准备发送进度更新到房间 project_...
   - [Socket.IO] 进度更新已发送
   - task_manager.run_chat 完成，结果: success=True
   - [Socket.IO] 发送聊天完成事件到房间 project_...

6. 如果没有看到预期的日志，检查：
   - Socket.IO 是否正确连接（浏览器控制台）
   - API 请求是否成功（Network 标签）
   - 后端是否收到请求（终端日志）
   - 回调函数是否被调用（终端日志）
    """)

def main():
    print("\n" + "="*60)
    print("任务聊天流程测试")
    print("="*60)

    results = []

    # 运行测试
    results.append(("progress_callback 机制", test_progress_callback()))
    results.append(("create_log_callback 函数", test_create_log_callback()))
    results.append(("Socket.IO emit 机制", test_socket_io_emit()))

    # 打印测试结果
    print("\n" + "="*60)
    print("测试结果汇总")
    print("="*60)

    passed = sum(1 for _, result in results if result)
    total = len(results)

    for name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{status}: {name}")

    print(f"\n总计: {passed}/{total} 测试通过")

    if passed == total:
        print("\n✓ 所有单元测试通过！")
    else:
        print(f"\n✗ 有 {total - passed} 个测试失败")

    # 打印集成测试指南
    print_integration_guide()

    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
