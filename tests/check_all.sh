#!/bin/bash
# 综合测试脚本

echo "======================================================"
echo "开始全面检查"
echo "======================================================"

# 1. Python 语法检查
echo -e "\n1. Python 语法检查..."
python3 -m py_compile src/web_app.py && echo "✓ web_app.py 语法正确" || { echo "✗ web_app.py 语法错误"; exit 1; }
python3 -m py_compile run_app.py && echo "✓ run_app.py 语法正确" || { echo "✗ run_app.py 语法错误"; exit 1; }

# 2. 导入测试
echo -e "\n2. 导入测试..."
python3 -c "import sys; sys.path.insert(0, 'src'); from web_app import app, socketio" && echo "✓ 导入成功" || { echo "✗ 导入失败"; exit 1; }

# 3. 启动测试
echo -e "\n3. 启动测试..."
python3 tests/test_startup.py || { echo "✗ 启动测试失败"; exit 1; }

# 4. 运行时测试
echo -e "\n4. 运行时测试..."
python3 tests/test_runtime.py || { echo "✗ 运行时测试失败"; exit 1; }

echo -e "\n======================================================"
echo "✓ 所有检查通过！应用可以正常运行。"
echo "======================================================"
echo -e "\n启动命令："
echo "  python3 run_app.py --port 5005"
echo -e "\n或使用调试模式："
echo "  python3 run_app.py --port 5005 --debug"
