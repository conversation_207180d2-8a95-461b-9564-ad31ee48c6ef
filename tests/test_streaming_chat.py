#!/usr/bin/env python3
"""
测试流式聊天功能
"""

import sys
import os

# 添加项目路径到 sys.path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_imports():
    """测试导入"""
    print("测试导入模块...")
    try:
        from src.web_app import app
        print("✓ 成功导入 web_app")
        return True
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_routes():
    """测试路由存在"""
    print("\n测试路由配置...")
    try:
        from src.web_app import app

        # 检查聊天 API 路由
        chat_route = None
        for rule in app.url_map.iter_rules():
            if 'chat' in rule.rule and rule.rule.endswith('/chat'):
                chat_route = rule
                break

        if chat_route:
            print(f"✓ 找到聊天路由: {chat_route.rule}")
            print(f"  方法: {', '.join(chat_route.methods)}")
            return True
        else:
            print("✗ 未找到聊天路由")
            return False
    except Exception as e:
        print(f"✗ 测试路由失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_response_type():
    """测试响应类型"""
    print("\n测试 Response 类型...")
    try:
        from flask import Response
        print("✓ Flask Response 类型可用")
        return True
    except Exception as e:
        print(f"✗ Response 类型导入失败: {e}")
        return False

def main():
    print("=" * 60)
    print("流式聊天功能测试")
    print("=" * 60)

    results = []

    # 运行测试
    results.append(("导入测试", test_imports()))
    results.append(("路由测试", test_routes()))
    results.append(("响应类型测试", test_response_type()))

    # 汇总结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)

    passed = sum(1 for _, result in results if result)
    total = len(results)

    for name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{name}: {status}")

    print(f"\n总计: {passed}/{total} 通过")

    if passed == total:
        print("\n✓ 所有测试通过!")
        return 0
    else:
        print(f"\n✗ {total - passed} 个测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
