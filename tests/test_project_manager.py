#!/usr/bin/env python3
"""
项目管理器测试脚本
"""

import os
import sys
import tempfile
import shutil

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from project_manager import ProjectManager

def test_project_management():
    """测试项目管理功能"""
    print("=== 测试项目管理功能 ===")
    
    # 创建临时数据目录
    with tempfile.TemporaryDirectory() as temp_dir:
        data_dir = os.path.join(temp_dir, "data")
        work_dir = os.path.join(temp_dir, "projects", "test_project")
        
        # 创建项目管理器
        pm = ProjectManager(data_dir)
        
        # 测试创建项目
        print("1. 创建项目...")
        project_id = pm.create_project(
            name="测试项目",
            work_dir=work_dir,
            description="这是一个测试项目"
        )
        print(f"   项目ID: {project_id}")
        
        # 测试获取项目
        print("2. 获取项目...")
        project = pm.get_project(project_id)
        print(f"   项目名称: {project.name}")
        print(f"   工作目录: {project.work_dir}")
        print(f"   描述: {project.description}")
        
        # 测试列出项目
        print("3. 列出所有项目...")
        projects = pm.list_projects()
        print(f"   项目数量: {len(projects)}")
        
        # 测试更新项目
        print("4. 更新项目...")
        pm.update_project(project_id, name="更新后的测试项目", description="更新后的描述")
        updated_project = pm.get_project(project_id)
        print(f"   更新后名称: {updated_project.name}")
        print(f"   更新后描述: {updated_project.description}")
        
        return pm, project_id

def test_requirement_management(pm, project_id):
    """测试需求管理功能"""
    print("\n=== 测试需求管理功能 ===")
    
    # 测试创建需求
    print("1. 创建需求...")
    req_id = pm.create_requirement(
        project_id=project_id,
        title="实现用户登录功能",
        description="用户可以通过用户名和密码登录系统",
        priority="high"
    )
    print(f"   需求ID: {req_id}")
    
    # 创建第二个需求
    req_id2 = pm.create_requirement(
        project_id=project_id,
        title="实现用户注册功能",
        description="新用户可以注册账号",
        priority="medium"
    )
    print(f"   需求ID2: {req_id2}")
    
    # 测试获取需求
    print("2. 获取需求...")
    requirement = pm.get_requirement(req_id)
    print(f"   需求标题: {requirement.title}")
    print(f"   需求描述: {requirement.description}")
    print(f"   优先级: {requirement.priority}")
    print(f"   状态: {requirement.status}")
    
    # 测试列出需求
    print("3. 列出项目需求...")
    requirements = pm.list_requirements(project_id)
    print(f"   需求数量: {len(requirements)}")
    for req in requirements:
        print(f"   - {req.title} ({req.priority})")
    
    # 测试更新需求
    print("4. 更新需求...")
    pm.update_requirement(req_id, status="in_progress")
    updated_req = pm.get_requirement(req_id)
    print(f"   更新后状态: {updated_req.status}")
    
    return req_id, req_id2

def test_task_integration(pm, req_id):
    """测试任务集成功能"""
    print("\n=== 测试任务集成功能 ===")
    
    # 测试获取TaskManager
    print("1. 获取TaskManager...")
    task_manager = pm.get_task_manager(req_id)
    if task_manager:
        print(f"   TaskManager工作目录: {task_manager.work_dir}")
        print(f"   任务文件路径: {task_manager.task_file_name}")
    else:
        print("   获取TaskManager失败")
        return
    
    # 注意：这里不实际调用Claude，只是测试接口
    print("2. 模拟任务生成...")
    print("   (跳过实际的Claude调用)")
    
    # 测试项目摘要
    print("3. 获取项目摘要...")
    requirement = pm.get_requirement(req_id)
    project_id = requirement.project_id
    summary = pm.get_project_summary(project_id)
    print(f"   项目名称: {summary['project']['name']}")
    print(f"   需求数量: {summary['requirements_count']}")
    print(f"   需求状态分布: {summary['requirements_by_status']}")
    print(f"   需求优先级分布: {summary['requirements_by_priority']}")

def test_deletion(pm, project_id, req_id, req_id2):
    """测试删除功能"""
    print("\n=== 测试删除功能 ===")
    
    # 测试删除需求
    print("1. 删除需求...")
    success = pm.delete_requirement(req_id2)
    print(f"   删除需求结果: {success}")
    
    remaining_reqs = pm.list_requirements(project_id)
    print(f"   剩余需求数量: {len(remaining_reqs)}")
    
    # 测试删除项目
    print("2. 删除项目...")
    success = pm.delete_project(project_id, delete_files=True)
    print(f"   删除项目结果: {success}")
    
    remaining_projects = pm.list_projects()
    print(f"   剩余项目数量: {len(remaining_projects)}")

def main():
    """主测试函数"""
    print("开始测试项目管理器...")
    
    try:
        # 测试项目管理
        pm, project_id = test_project_management()
        
        # 测试需求管理
        req_id, req_id2 = test_requirement_management(pm, project_id)
        
        # 测试任务集成
        test_task_integration(pm, req_id)
        
        # 测试删除功能
        test_deletion(pm, project_id, req_id, req_id2)
        
        print("\n✅ 所有测试通过！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
