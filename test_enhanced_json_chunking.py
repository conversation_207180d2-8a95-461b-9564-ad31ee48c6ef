#!/usr/bin/env python3
"""
测试增强的JSON文档切块功能
支持自定义起始层级和阈值参数
"""

import sys
import os
import json
import logging

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from json_chunker import JsonChunker, JSON_CHUNK_SIZE_THRESHOLD, JSON_SLIDING_OVERLAP

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_enhanced_json_chunking():
    """测试增强的JSON文档切块功能"""
    print("🧪 开始测试增强的JSON文档切块功能...")
    print("=" * 80)

    # 创建JSON切块器
    chunker = JsonChunker()

    # 测试配置
    test_configs = [
        {
            "file": "test_small_json.json",
            "scenarios": [
                {"level": 1, "threshold": 500, "description": "第1层级，500字节阈值"},
                {"level": 2, "threshold": 1000, "description": "第2层级，1000字节阈值"},
                {"level": "auto", "threshold": 800, "description": "自动层级，800字节阈值"},
            ]
        },
        {
            "file": "test_large_json.json",
            "scenarios": [
                {"level": 1, "threshold": 500, "description": "第1层级，500字节阈值"},
                {"level": 1, "threshold": 1000, "description": "第1层级，1000字节阈值"},
                {"level": 2, "threshold": 800, "description": "第2层级，800字节阈值"},
                {"level": "auto", "threshold": 600, "description": "自动层级，600字节阈值"},
            ]
        },
        {
            "file": "test_multilevel_json.json",
            "scenarios": [
                {"level": 1, "threshold": 1000, "description": "第1层级，1000字节阈值"},
                {"level": 2, "threshold": 1000, "description": "第2层级，1000字节阈值"},
                {"level": 3, "threshold": 1000, "description": "第3层级，1000字节阈值"},
                {"level": 4, "threshold": 1000, "description": "第4层级，1000字节阈值"},
                {"level": "auto", "threshold": 1000, "description": "自动层级，1000字节阈值"},
            ]
        }
    ]

    for test_config in test_configs:
        test_file = test_config["file"]

        if not os.path.exists(test_file):
            print(f"⚠️  测试文件不存在: {test_file}")
            continue

        print(f"\n📁 测试文件: {test_file}")
        print("-" * 60)

        try:
            # 读取JSON文件
            with open(test_file, 'r', encoding='utf-8') as f:
                json_content = f.read()

            file_size = len(json_content.encode('utf-8'))
            print(f"📊 文件大小: {file_size} 字节")

            # 获取JSON摘要
            summary = chunker.get_json_summary(json_content)
            print(f"📋 JSON摘要:")
            print(f"   - 结构类型: {summary.get('structure_type', 'Unknown')}")
            print(f"   - 最大深度: {summary.get('max_depth', 0)}")
            print(f"   - 根节点数量: {summary.get('total_elements', 0)}")
            if summary.get('root_keys'):
                root_keys_preview = summary['root_keys'][:3]
                print(f"   - 主要根节点: {', '.join(root_keys_preview)}{'...' if len(summary['root_keys']) > 3 else ''}")

            # 测试不同配置
            for scenario in test_config["scenarios"]:
                level = scenario["level"]
                threshold = scenario["threshold"]
                description = scenario["description"]

                print(f"\n🔧 测试配置: {description}")
                print(f"   起始层级: {level}, 阈值: {threshold}字节")

                # 进行切块处理
                chunks_data = chunker.chunk_json_document(
                    json_content, test_file, start_level=level, threshold=threshold
                )

                print(f"✅ 切块完成，共 {len(chunks_data)} 个块")

                # 统计分析
                level_counts = {}
                method_counts = {}
                size_stats = []
                total_size = 0

                for i, chunk in enumerate(chunks_data):
                    chunk_level = chunk['metadata'].get('level', 1)
                    method = chunk['metadata'].get('chunk_method', 'structure')
                    size = chunk['metadata'].get('size_bytes', 0)

                    level_counts[chunk_level] = level_counts.get(chunk_level, 0) + 1
                    method_counts[method] = method_counts.get(method, 0) + 1
                    size_stats.append(size)
                    total_size += size

                # 显示统计信息
                print(f"📈 分块统计:")
                print(f"   - 总块数: {len(chunks_data)}")
                print(f"   - 总大小: {total_size} 字节")
                print(f"   - 平均大小: {total_size/len(chunks_data):.1f} 字节")

                print(f"   - 按层级分布:")
                for lvl, count in sorted(level_counts.items()):
                    print(f"     * 层级 {lvl}: {count} 块")

                print(f"   - 按方法分布:")
                for method, count in method_counts.items():
                    print(f"     * {method}: {count} 块")

                # 验证块大小是否合理
                oversized_chunks = [s for s in size_stats if s > threshold]
                if oversized_chunks:
                    print(f"⚠️  警告: 有 {len(oversized_chunks)} 个块超过阈值")
                    print(f"   最大块: {max(oversized_chunks)} 字节")
                else:
                    print("✅ 所有块都在阈值范围内")

                # 显示前几个块的详细信息
                print(f"📄 前3个块详情:")
                for i, chunk in enumerate(chunks_data[:3]):
                    meta = chunk['metadata']
                    content_preview = chunk['content'][:100]
                    print(f"   块 {i+1}: 层级{meta.get('level', 1)}, {meta.get('size_bytes', 0)}字节")
                    if meta.get('key'):
                        print(f"           键: {meta['key']}")
                    elif meta.get('index') is not None:
                        print(f"           索引: {meta['index']}")
                    print(f"           预览: {content_preview}{'...' if len(content_preview) > 100 else ''}")

        except Exception as e:
            print(f"❌ 测试失败: {e}")
            logger.error(f"测试文件 {test_file} 失败", exc_info=True)

    print("\n" + "=" * 80)
    print("🎉 增强JSON文档切块功能测试完成!")

def test_auto_level_selection():
    """测试自动层级选择功能"""
    print("\n🤖 测试自动层级选择功能...")
    print("-" * 50)

    chunker = JsonChunker()

    # 测试不同复杂度的JSON
    test_cases = [
        {"file": "test_small_json.json", "threshold": 800},
        {"file": "test_large_json.json", "threshold": 1000},
        {"file": "test_multilevel_json.json", "threshold": 1000},
    ]

    for test_case in test_cases:
        test_file = test_case["file"]
        threshold = test_case["threshold"]

        if not os.path.exists(test_file):
            continue

        print(f"\n📁 文件: {test_file} (阈值: {threshold}字节)")

        try:
            with open(test_file, 'r', encoding='utf-8') as f:
                json_content = f.read()

            json_data = json.loads(json_content)

            # 测试自动层级选择
            selected_level = chunker._auto_select_start_level(json_data, threshold)
            print(f"🎯 自动选择层级: {selected_level}")

            # 分析层级统计
            level_stats = chunker._analyze_json_levels(json_data, chunker._calculate_max_depth(json_data))

            print("📊 各层级统计:")
            for level in range(1, 5):
                if level in level_stats:
                    stats = level_stats[level]
                    print(f"   层级 {level}: {stats['chunk_count']} 块, 平均 {stats['avg_size']:.1f} 字节")

            # 使用选择的层级进行分块
            chunks_data = chunker.chunk_json_document(
                json_content, test_file, start_level=selected_level, threshold=threshold
            )

            print(f"✅ 使用层级 {selected_level} 分块结果: {len(chunks_data)} 个块")

        except Exception as e:
            print(f"❌ 测试失败: {e}")

def test_sliding_window_with_new_threshold():
    """测试新的滑动窗口阈值"""
    print("\n🔄 测试滑动窗口功能（新阈值）...")
    print("-" * 50)

    chunker = JsonChunker()

    # 创建不同大小的测试文本
    test_texts = [
        {"text": "短文本测试。" * 50, "description": "小文本 (~200字节)"},
        {"text": "中等长度文本测试，用于测试分块功能。" * 100, "description": "中等文本 (~800字节)"},
        {"text": "这是一个用于测试滑动窗口功能的长文本。" * 200, "description": "长文本 (~3000字节)"},
    ]

    for test_case in test_texts:
        text = test_case["text"]
        description = test_case["description"]
        threshold = 1000  # 使用新的阈值

        print(f"\n📝 {description}")
        print(f"   文本长度: {len(text.encode('utf-8'))} 字节")

        # 使用滑动窗口分块
        sliding_chunks = chunker._chunk_by_sliding_window(text, "test", threshold=threshold)

        print(f"   滑动窗口切块结果: {len(sliding_chunks)} 个块")

        for i, chunk in enumerate(sliding_chunks[:3]):  # 只显示前3个
            meta = chunk['metadata']
            print(f"   块 {i+1}: {meta['size_bytes']}字节, 窗口[{meta.get('window_start')}-{meta.get('window_end')}]")
            print(f"           预览: {chunk['content'][:50]}{'...' if len(chunk['content']) > 50 else ''}")

if __name__ == "__main__":
    test_enhanced_json_chunking()
    test_auto_level_selection()
    test_sliding_window_with_new_threshold()