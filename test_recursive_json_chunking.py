#!/usr/bin/env python3
"""
测试递归分层JSON文档切块功能
验证新的分层逻辑：第1层级100字节阈值，其他层级1000字节阈值
"""

import sys
import os
import json
import logging

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from json_chunker import JsonChunker, JSON_FIRST_LEVEL_THRESHOLD, JSON_CHUNK_SIZE_THRESHOLD

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_recursive_json_chunking():
    """测试递归分层JSON文档切块功能"""
    print("🧪 开始测试递归分层JSON文档切块功能...")
    print("=" * 80)
    print(f"📋 分块配置:")
    print(f"   - 第1层级阈值: {JSON_FIRST_LEVEL_THRESHOLD} 字节")
    print(f"   - 其他层级阈值: {JSON_CHUNK_SIZE_THRESHOLD} 字节")
    print(f"   - 滑动窗口大小: {JSON_CHUNK_SIZE_THRESHOLD} 字节")
    print(f"   - 滑动窗口重叠: 100 字节")
    print("=" * 80)

    # 创建JSON切块器
    chunker = JsonChunker()

    # 测试文件列表
    test_files = [
        "test_small_json.json",
        "test_large_json.json",
        "test_multilevel_json.json"
    ]

    for test_file in test_files:
        if not os.path.exists(test_file):
            print(f"⚠️  测试文件不存在: {test_file}")
            continue

        print(f"\n📁 测试文件: {test_file}")
        print("-" * 60)

        try:
            # 读取JSON文件
            with open(test_file, 'r', encoding='utf-8') as f:
                json_content = f.read()

            file_size = len(json_content.encode('utf-8'))
            print(f"📊 文件信息:")
            print(f"   - 文件大小: {file_size} 字节")

            # 获取JSON摘要
            summary = chunker.get_json_summary(json_content)
            print(f"   - 结构类型: {summary.get('structure_type', 'Unknown')}")
            print(f"   - 最大深度: {summary.get('max_depth', 0)}")
            print(f"   - 根节点数量: {summary.get('total_elements', 0)}")
            if summary.get('root_keys'):
                root_keys_preview = summary['root_keys'][:3]
                print(f"   - 主要根节点: {', '.join(root_keys_preview)}{'...' if len(summary['root_keys']) > 3 else ''}")

            # 进行递归分层切块处理
            print(f"\n🔧 执行递归分层切块...")
            chunks_data = chunker.chunk_json_document(json_content, test_file)

            print(f"✅ 切块完成，共生成 {len(chunks_data)} 个块")

            # 统计分析
            level_counts = {}
            method_counts = {}
            size_stats = []
            total_size = 0
            sliding_window_count = 0

            for i, chunk in enumerate(chunks_data):
                chunk_level = chunk['metadata'].get('level', 1)
                method = chunk['metadata'].get('chunk_method', 'structure')
                size = chunk['metadata'].get('size_bytes', 0)

                level_counts[chunk_level] = level_counts.get(chunk_level, 0) + 1
                method_counts[method] = method_counts.get(method, 0) + 1
                size_stats.append(size)
                total_size += size

                if method == 'sliding_window':
                    sliding_window_count += 1

            # 显示统计信息
            print(f"\n📈 分块统计:")
            print(f"   - 总块数: {len(chunks_data)}")
            print(f"   - 总大小: {total_size} 字节")
            print(f"   - 平均大小: {total_size/len(chunks_data):.1f} 字节")
            print(f"   - 滑动窗口块数: {sliding_window_count}")

            print(f"\n   - 按层级分布:")
            for level in sorted(level_counts.keys()):
                count = level_counts[level]
                threshold = JSON_FIRST_LEVEL_THRESHOLD if level == 1 else JSON_CHUNK_SIZE_THRESHOLD
                print(f"     * 层级 {level}: {count} 块 (阈值: {threshold}字节)")

            print(f"\n   - 按方法分布:")
            for method, count in method_counts.items():
                print(f"     * {method}: {count} 块")

            # 验证分块逻辑
            print(f"\n🔍 分块逻辑验证:")

            # 检查第1层级的块是否都满足100字节阈值
            level1_chunks = [c for c in chunks_data if c['metadata'].get('level') == 1]
            if level1_chunks:
                oversized_level1 = [c for c in level1_chunks if c['metadata'].get('size_bytes', 0) > JSON_FIRST_LEVEL_THRESHOLD]
                if oversized_level1:
                    print(f"   ⚠️  第1层级有 {len(oversized_level1)} 个块超过100字节阈值")
                    for chunk in oversized_level1[:3]:  # 只显示前3个
                        size = chunk['metadata'].get('size_bytes', 0)
                        key = chunk['metadata'].get('key', 'N/A')
                        print(f"      - 键: {key}, 大小: {size}字节")
                else:
                    print(f"   ✅ 第1层级所有块都在{JSON_FIRST_LEVEL_THRESHOLD}字节阈值内")

            # 检查其他层级的块是否都满足1000字节阈值
            other_level_chunks = [c for c in chunks_data if c['metadata'].get('level') > 1]
            if other_level_chunks:
                oversized_other = [c for c in other_level_chunks if c['metadata'].get('size_bytes', 0) > JSON_CHUNK_SIZE_THRESHOLD]
                if oversized_other:
                    print(f"   ⚠️  其他层级有 {len(oversized_other)} 个块超过{JSON_CHUNK_SIZE_THRESHOLD}字节阈值")
                else:
                    print(f"   ✅ 其他层级所有块都在{JSON_CHUNK_SIZE_THRESHOLD}字节阈值内")

            # 显示前几个块的详细信息
            print(f"\n📄 前5个块详情:")
            for i, chunk in enumerate(chunks_data[:5]):
                meta = chunk['metadata']
                content_preview = chunk['content'][:100]
                method = meta.get('chunk_method', 'structure')

                print(f"   块 {i+1}: 层级{meta.get('level', 1)}, {meta.get('size_bytes', 0)}字节, 方法:{method}")

                # 显示层级路径
                parent_keys = meta.get('parent_keys', [])
                if parent_keys:
                    path_str = " → ".join(parent_keys)
                    print(f"           路径: {path_str}")

                # 显示键或索引
                if meta.get('key'):
                    print(f"           键: {meta['key']}")
                elif meta.get('index') is not None:
                    print(f"           索引: {meta['index']}")

                print(f"           预览: {content_preview}{'...' if len(content_preview) > 100 else ''}")
                print()

        except Exception as e:
            print(f"❌ 测试失败: {e}")
            logger.error(f"测试文件 {test_file} 失败", exc_info=True)

    print("\n" + "=" * 80)
    print("🎉 递归分层JSON文档切块功能测试完成!")

def test_threshold_behavior():
    """测试阈值行为"""
    print("\n🎯 测试阈值行为...")
    print("-" * 50)

    chunker = JsonChunker()

    # 创建不同大小的测试JSON对象
    test_cases = [
        {
            "name": "小对象（应满足第1层级阈值）",
            "data": {"small": "这是一个小对象，应该满足100字节阈值"}
        },
        {
            "name": "中等对象（应进入第2层级）",
            "data": {
                "medium": "这是一个中等大小的对象，内容会超过100字节阈值但不会超过1000字节。" * 10
            }
        },
        {
            "name": "大对象（应进入深层或滑动窗口）",
            "data": {
                "large": {
                    "content": "这是一个大对象，会递归分层。" * 50,
                    "nested": {
                        "deep": "继续嵌套的内容，应该会继续向下分层。" * 30
                    }
                }
            }
        }
    ]

    for i, test_case in enumerate(test_cases):
        print(f"\n测试用例 {i+1}: {test_case['name']}")

        try:
            json_content = json.dumps(test_case['data'], ensure_ascii=False, indent=2)
            chunks_data = chunker.chunk_json_document(json_content, f"test_case_{i+1}")

            print(f"   原始大小: {len(json_content.encode('utf-8'))} 字节")
            print(f"   生成块数: {len(chunks_data)}")

            # 分析块的层级分布
            levels = sorted(set(chunk['metadata'].get('level', 1) for chunk in chunks_data))
            print(f"   涉及层级: {levels}")

            for level in levels:
                level_chunks = [c for c in chunks_data if c['metadata'].get('level') == level]
                avg_size = sum(c['metadata'].get('size_bytes', 0) for c in level_chunks) / len(level_chunks)
                print(f"     层级 {level}: {len(level_chunks)} 块, 平均大小: {avg_size:.1f} 字节")

        except Exception as e:
            print(f"   ❌ 测试失败: {e}")

def test_sliding_window_behavior():
    """测试滑动窗口行为"""
    print("\n🔄 测试滑动窗口行为...")
    print("-" * 50)

    chunker = JsonChunker()

    # 创建一个包含大文本的JSON对象
    large_text = "这是一个很长的文本内容，用于测试滑动窗口分块功能。" * 100
    test_data = {
        "large_text": large_text,
        "description": "测试滑动窗口分块"
    }

    json_content = json.dumps(test_data, ensure_ascii=False, indent=2)
    chunks_data = chunker.chunk_json_document(json_content, "sliding_window_test")

    print(f"原始JSON大小: {len(json_content.encode('utf-8'))} 字节")
    print(f"生成块数: {len(chunks_data)}")

    # 查找滑动窗口块
    sliding_chunks = [c for c in chunks_data if c['metadata'].get('chunk_method') == 'sliding_window']

    if sliding_chunks:
        print(f"滑动窗口块数: {len(sliding_chunks)}")
        print("前3个滑动窗口块:")
        for i, chunk in enumerate(sliding_chunks[:3]):
            meta = chunk['metadata']
            print(f"   块 {i+1}: 层级{meta.get('level', 1)}, {meta.get('size_bytes', 0)}字节")
            print(f"           窗口: [{meta.get('window_start', 0)}-{meta.get('window_end', 0)}]")
            content_preview = chunk['content'][:80]
            print(f"           预览: {content_preview}{'...' if len(content_preview) > 80 else ''}")
    else:
        print("没有使用滑动窗口分块")

if __name__ == "__main__":
    test_recursive_json_chunking()
    test_threshold_behavior()
    test_sliding_window_behavior()