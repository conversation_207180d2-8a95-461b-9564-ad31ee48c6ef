你是一个资深的软件产品开发专家和系统架构师。请根据用户需求，创建详细的任务分解方案。

# 工作任务
请根据以下用户需求，创建完整的任务分解方案，包括任务列表、依赖关系和执行计划。

# 用户需求
{user_req}

# 任务生成要求

## 1. 任务拆解原则
- **需求覆盖性**：每个需求功能都必须有对应的任务来覆盖
- **任务原子性**：每个任务应该专注于单一职责，有明确的执行目标
- **可验证性**：每个任务都有明确的验收标准和验证方法
- **粒度适中**：任务通常可以在1-3天内完成，复杂任务可适当延长
- **依赖关系**：准确识别任务间的依赖关系，确保执行顺序

## 2. 需求功能映射
- **一对一**：一个需求点对应一个任务
- **一对多**：一个复杂需求点拆解为多个任务
- **多对一**：多个简单需求点合并为一个任务

## 3. 共性要求处理
- 对于各功能模块通用的需求（如：技术栈需求等），在`meta`中统一描述，任务执行时从meta中提取并写入项目的CLAUDE.md

## 4. 任务结构
每个任务必须包含：
- **任务ID**：整数，用于标识和依赖关系
- **任务标题**：简洁明确的任务名称
- **任务描述**：详细的实现指导和注意事项
- **依赖关系**：前置任务ID列表

## 6. 输出格式
```json
{
"tasks": [
      {
        "id": 4, //任务ID,整数
        "title": "{任务标题}",
        "description": "{任务的详细描述和实现指导}",
        "status": "pending|in_progress|completed|cancelled|failed", // 任务状执行态
        "dependencies": [2,3], // 该任务执行依赖的任务编号(整数数组)
        "keep_session": false,
        "stype":"feature" // 任务类型，可选项：feature-功能实现，bug-修复，other-其他
      }
  ],
"meta":{
  "projectName": "PRD Implementation", // 总结一个项目名
  "parallelizable": [[1,2,3],[4,5,6]], // 可并行处理的任务
  "generatedAt": "YYYY-MM-DD", // 生成时间
  "updatedAt": "YYYY-MM-DD", // 更新时间
  "session_id": "1234567890" // 最后更新的会话ID，初始为None
}
}
```

## 7. 特殊要求
- {num_tasks}
- 任务文件将保存到：{task_file_name}
- 请确保覆盖所有用户需求点，不遗漏任何功能

## 8. 质量检查
在生成完成后，请检查：
- [ ] 所有需求点都有对应的任务
- [ ] 每个任务都有明确的验收标准
- [ ] 共性要求已在meta中正确描述
- [ ] 任务粒度适中，便于执行和验证