# 需求规格说明书

## 1. 简介

### 1.1. 术语和缩写

### 1.2. 参考资料

包括引用的项目内资料、客户方资料等。

## 2. 产品描述

包括产品介绍、产品范围等。

## 3. 用户业务场景分析

分析用户的真实业务场景，描述用户场景对于产品的需求要点。

## 4. 与现有产品差异

如果产品是升级产品或从现有产品进行改造，请注明前一产品的规格，并列出现有产品差异。在备注栏可填写额外的说明信息。

| 现有产品功能点 | 新需求差异描述 | 备注 |
|---|---|---|
| | | |
| | | |
| | | |
| | | |

## 5. 约束与限制

说明在实现时所必须满足的条件和所受的限制，以及相应的原因。如：必须使用或者避免的特定技术、工具、编程语言和数据库、企业策略、政府法规或工业标准。

## 6. 需求详细描述

本文档是从产品整体的角度描述需求，可能会涉及到硬件和软件结合的情况。在产品层次未涉及的硬件详细需求规格，可专门输出《硬件需求规格说明书》。

### 6.1. 产品运行环境

详细说明各功能模块运行所需的环境（比如运行平台、操作系统、硬件配置、网络环境）。

| 需求编号 | 名称 | 需求描述 | 优先级 |
|---|---|---|---|
| PR-E-0001 | | | |
| PR-E-0002 | | | |
| | | | |
| | | | |

**注：**

需求编号采用 PR-E-**** 的形式，PR 代表产品，E 代表运行环境，编号从 0001开始依次累加。填写表格时需要按照需求的层次自行增加子章节。

### 6.2. 功能清单

描述整个产品所包含的功能模块以及所属关系。

| 序号 | 一级功能说明 | 二级功能说明 | 三级功能说明 | 其它说明 |
|---|---|---|---|---|
| 1 | 功能1 | 功能1.1 | 功能 1.1.1 | |
| 2 | 功能1 | 功能1.1 | 功能1.1.2 | |
| 3 | 功能1 | 功能1.1 | | |
| 4 | 功能1 | 功能1.2 | … | |
| 5 | 功能2 | | | |
| 6 | … | | | |

### 6.3. 功能需求

产品所实现的功能，即从用户角度要求必须实现的功能点。包括用户陈述的或隐含的需求，可划分为"高、中、低"三级。

**说明：**

1. 需求编号采用 PR-F-**** 的形式，PR 代表产品，F 代表功能需求，编号从0001 开始依次累加。
2. 填写时需要按照需求的编号自行增加子章节。

#### 1. 功能1.1 或1.1.1（此处可以二级或三级功能为子目录，根据产品情况自行决定）

**说明：** 先用文字整体描述该模块/功能所支持的功能，或与其它功能之间的关系。再将该功能分解成几个需求进行详细描述。

**PR-F-0001：[需求名称]**

| 项目 | 内容 |
|---|---|
| **需求编号** | PR-F-0001 |
| **需求名称** |  |
| **优先级** |  |
| **需求描述** | 详细描述需求的具体内容 |
| **业务流程** | 需求的业务流程说明，要包括正常和异常的业务流程，可采用文字或者流程图等不同方式描述 |
| **输入输出约束** |  |
| **验收标准** | 验收标准是从验收的视角出发对需求的定量描述。每条需求都应有明确的指标可以作为验收标准。在验收时主要从两方面考虑需求规格描述是否合格：一是考虑它能否作为测试的数据输入，二是考虑需求的度量方式是否满足产品的目标。 |
| **其它说明** | 以上条目中若有不适用的，写明"不涉及"即可 |


#### 2. 功能1.2

该功能是 XXXXX

**PR-F-0002：[需求名称]**

| 项目 | 内容 |
|---|---|
| **需求编号** | PR-F-0002 |
| **需求名称** |  |
| **优先级** |  |
| **需求描述** |  |
| **业务流程** |  |
| **输入输出约束** |  |
| **验收标准** |  |
| **其它说明** |  |

### 6.4. 接口需求

可包括硬件接口、软件接口、通信接口等。

- **硬件接口：** 要指出每一个接口的逻辑特点、支撑什么样的设备、如何支撑这些设备、有何约定。
- **软件接口：** 在这里应指定需使用的其他软件产品（例如：数据管理系统、操作系统、或者数学软件包），以及同其他应用系统之间的接口。对于每一个接口，这部分应说明与产品相关的接口软件的目的，并根据信息的内容和格式定义接口，这里不必详细描述任何已有完整文件的接口，只要引用定义该接口的文件即可。
- **通信接口：** 这里指定各个通信接口，例如：局部网络的协议等。

**注：**

1. 需求编号采用 PR-I-**** 的形式，PR 代表产品，I 代表接口需求，编号从0001 开始依次累加。
2. 填写表格时需要按照需求的层次自行增加子章节。

#### 1. XXX

**PR-I-0001：[需求名称]**

| 项目 | 内容 |
|---|---|
| **需求编号** | PR-I-0001 |
| **需求名称** |  |
| **优先级** |  |
| **需求描述** |  |
| **输入输出约束** |  |
| **验收标准** |  |
| **其它说明** |  |

#### 2. XXX

**PR-I-0002：[需求名称]**

| 项目 | 内容 |
|---|---|
| **需求编号** | PR-I-0002 |
| **需求名称** |  |
| **优先级** |  |
| **需求描述** |  |
| **输入输出约束** |  |
| **验收标准** |  |
| **其它说明** |  |

### 6.5. 界面需求

界面是指产品为使用者提供的交互展示窗体或页面。本章节需要描述产品的界面风格、界面提供的功能，明确界面涉及字段的状态选择、长度、数值限制等，如设备状态，将各种状态列全；设备类型，将涉及到的设备类型列全；有字段长度或数值限制，需加以说明。可采用自然语言描述出每个界面需要提供的功能，也可以采用原型图等展示。产品首页和监控大屏类页面，需协调产品设计部提供原型图设计，需提前一周与产品设计部联系。

| 需求编号 | 名称 | 需求描述 | 优先级 | 验收标准 |
|---|---|---|---|---|
| PR-U-0001 | | | | |
| PR-U-0002 | | | | |
| | | | | |
| | | | | |

**注：**

需求编号采用PR-U-**** 的形式，PR 代表产品，U 代表界面需求，编号从0001 开始依次累加。填写表格时需要按照需求的层次自行增加子章节。

### 6.6. 性能需求

性能通常指产品在功能上满足顾客要求的能力，包括使用性能和外观性能。

| 需求编号 | 名称 | 需求描述 | 优先级 | 验收标准 |
|---|---|---|---|---|
| PR-P-0001 | | | | |
| PR-P-0002 | | | | |
| | | | | |

**注：**

需求编号采用PR-P-**** 的形式，PR 代表产品，P 代表性能需求，编号从0001 开始依次累加。填写表格时需要按照需求的层次自行增加子章节。

### 6.7. 可靠性/可用性需求

产品在规定的条件下，在规定的时间内，在一定条件下无故障的完成规定的功能的能力或可能性称为可靠性。对那些发生质量事故会造成巨大损失或危及人身、社会安全的产品，可靠性是使用过程中主要的质量指标。可靠性可以通过平均无故障时间、平均修复时间、平均失效时间等指标体现。可用性是在某个特定时间，系统能够正常运行的概率或时间占有率期望值。通常使用N个九来表示系统可用性。如果该产品有国家或国际标准、行业标准可循，可以摘录标准中的可靠性/可用性相关要求内容，如完全遵守，可以直接写符合《***》标准第*章节安全性要求。原则上产品需要满足公司发布的《产品安全基线需求》，并参考竞品有竞争力的可用性/可靠性能力，具体内容参照《产品安全基线》。

| 需求编号 | 名称 | 需求描述 | 优先级 | 验收标准 |
|---|---|---|---|---|
| PR-R-0001 | | | | |
| PR-R-0002 | | | | |
| | | | | |

**注：**

1. 需求编号采用PR-R-**** 的形式，PR 代表产品，R 代表可靠性需求，编号从0001 开始依次累加。
2. 填写表格时需要按照需求的层次自行增加子章节。

### 6.8. 安全性需求

保护产品的要素，以防止各种非法的访问、使用，修改、破坏或者泄密。比如：

1. 利用可靠的密码技术。
2. 掌握特定的记录或历史数据集。
3. 给不同的模块分配不同的功能。
4. 限定一个程序中某些区域的通信。
5. 计算临界值的检查和。

如果该产品有国家或国际标准、行业标准可循，可以摘录标准中的安全性要求内容，如完全遵守，可以直接写符合《***》标准第*章节安全性要求。

原则上产品需要满足公司发布的《产品安全基线需求》，具体内容参照《产品安全基线》。同时结合产品安全相关的法律法规或行业规范,客户需求、结合公司《产品安全基线》、竞品分析、行业安全态势、威胁建模与风险分析、行业安全认证标准、最佳安全实践等进行分析，整理产品自身安全性方面的需求。

#### 6.8.1. 威胁建模过程

| 类型 | 安全风险 | 威胁描述 | 消减方案 |
|---|---|---|---|
| 接口或暴露面 | S-仿冒 | | |
| 接口或暴露面 | S-仿冒 | | |
| 接口或暴露面 | T-篡改 | | |
| 接口或暴露面 | R-信息泄露 | | |
| 接口或暴露面 | I-信息泄漏 | | |
| 接口或暴露面 | I-信息泄漏 | | |
| 接口或暴露面 | D-拒绝服务 | | |
| 接口或暴露面 | D-拒绝服务 | | |
| 接口或暴露面 | E-特权提升 | | |

#### 6.8.2. 安全需求汇总

| 需求编号 | 名称 | 需求描述 | 优先级 | 验收标准 |
|---|---|---|---|---|
| PR-S-0001 | | | | |
| PR-S-0002 | | | | |
| | | | | |
| | | | | |

**注：**

1. 需求编号采用PR-S-**** 的形式，PR 代表产品，S 代表安全需求，编号从0001 开始依次累加。
2. 填写表格时需要按照需求的层次自行增加子章节。

### 6.9. 可维护性需求

所有提高可维护性或可扩展性的需求。如使用行业标准，编码标准，开放式结构，可兼容语言，备份机复原，数据交换，命名约定，类库，维护访问，维护工具等。可以从维护者角度考虑，产品应该提供的错误码反馈、日志记录、自我诊断等功能。

| 需求编号 | 名称 | 需求描述 | 优先级 | 验收标准 |
|---|---|---|---|---|
| PR-M-0001 | | | | |
| PR-M-0002 | | | | |
| | | | | |
| | | | | |

**注：**

1. 需求编号采用PR-M-**** 的形式，PR 代表产品，M 代表可维护性需求，编号从0001 开始依次累加。
2. 填写表格时需要按照需求的层次自行增加子章节。

### 6.10. 工作状态需求

工作状态需求是指使用本硬件产品的客户或者是本公司其他部门的同事，能够通过硬件设备上的一些输出状态能够及时的获取硬件设备的工作情况，同时在硬件设备出现异常或作出其他反应时，用户能够及时针对特定情况做出反应。

| 需求编号 | 名称 | 需求描述 | 优先级 | 验收标准 |
|---|---|---|---|---|
| PR-WS-0001 | | | | |
| PR-WS-0002 | | | | |
| | | | | |
| | | | | |

**注：**

1. 需求编号采用PR-WS-**** 的形式，PR代表产品，WS代表工作状态，编号从0001开始依次累加。
2. 填写表格时需要按照需求的层次自行增加子章节。

### 6.11. 结构需求

硬件设备的设计开发过程涉及到硬件设备外观和结构方面的设计，一般硬件设备的结构尺寸会直接决定其应用场景，因此在需求阶段应该制定好硬件设备的结构方面的需求，可使用行业标准、相关政府标准等结构设计。

| 需求编号 | 名称 | 需求描述 | 优先级 |
|---|---|---|---|
| PR-ST-0001 | | | |
| PR-ST-0002 | | | |
| | | | |
| | | | |

**注：**

1. 需求编号采用PR-ST-**** 的形式，PR代表产品，ST代表结构需求，编号从0001开始依次累加。
2. 填写表格时需要按照需求的层次自行增加子章节。

### 6.12. 环保需求

环保相关的需求，一般涉及到硬件部件或者整机对环境、人体等影响的需求，例如要求硬件材料无铅等。

| 需求编号 | 名称 | 需求描述 | 优先级 |
|---|---|---|---|
| PR-E-0001 | | | |
| PR-E-0002 | | | |

**注：**

需求编号采用PR-E-**** 的形式，PR 代表产品，E 代表认证需求，编号从0001 开始依次累加。填写表格时需要按照需求的层次自行增加子章节。

### 6.13. 认证需求

产品在销售到特定的国家、地区或者行业之前，由第三方机构进行的准入检测，验证产品是否能满足国家、行业等相关技术标准。如：国密局产品型号认证、国际FIPS 认证、公安部销售许可证申请、中国CCC 认证，欧盟的CE 认证等。

| 需求编号 | 名称 | 需求描述 | 认证等级 | 接口规范 | 优先级 |
|---|---|---|---|---|---|
| PR-C-0001 | | | | | |
| PR-C-0002 | | | | | |

**注：**

1. 需求编号采用PR-C-**** 的形式，PR 代表产品，C 代表认证需求，编号从0001 开始依次累加。
2. 填写表格时需要按照需求的层次自行增加子章节。

### 6.14. 用户文档需求

用户指南、联机帮助、安装指南、配置文件等。

| 需求编号 | 名称 | 需求描述 | 优先级 |
|---|---|---|---|
| PR-D-0001 | | | |
| PR-D-0002 | | | |
| | | | |

**注：**

需求编号采用PR-D-**** 的形式，PR 代表产品，D 代表文档需求，编号从0001 开始依次累加。填写表格时需要按照需求的层次自行增加子章节。

### 6.15. 客户特殊需求

指客户提出以上需求以外的特殊需求。

| 需求编号 | 名称 | 需求描述 | 优先级 | 验收标准 |
|---|---|---|---|---|
| PR-CS-0001 | | | | |
| PR-CS-0002 | | | | |

### 6.16. 法律法规要求

描述产品需遵循的相关法律法规。

| 需求编号 | 名称 | 需求描述 | 优先级 |
|---|---|---|---|
| PR-LR-0001 | 中华人民共和国密码法 | 满足相关要求 | 高 |
| PR-LR-0002 | 计算机信息网络国际联网安全保护管理办法 | 满足相关要求 | 高 |

**注：** 以上所列通用法律法规，若本产品不满足或有其他标准，请自行修改或添加。

### 6.17. 国家及行业标准要求

描述产品需遵循的国家及行业标准要求。

| 需求编号 | 名称 | 需求描述 | 优先级 |
|---|---|---|---|
| PR-NIS-0001 | GB-T8567-2006计算机软件文档编制规范 | 满足相关要求 | 高 |
| PR-NIS-0002 | GB-T9386-2008计算机软件测试文档编制规范 | 满足相关要求 | 高 |

**注：** 以上所列行标，若本产品不满足或有其他标准，请自行修改或添加。

### 6.18. 失效模式分析

(参见《设计失效模式和影响分析(DFMEA)库》)

### 6.19. 其他需求

其他以上未涉及的需求，如使用寿命、可用性、经济性、易用性、效率、可移植性等。如果有明确需要，可在此处添加，否则可裁剪。

| 需求编号 | 名称 | 需求描述 | 优先级 |
|---|---|---|---|
| PR-O-0001 | | | |
| PR-O-0002 | | | |

**注：**

1. 需求编号采用PR-O-**** 的形式，PR 代表产品，O 代表其他需求，编号从0001 开始依次累加。
2. 填写表格时需要按照需求的层次自行增加子章节。